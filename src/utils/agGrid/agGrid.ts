import {AGColDef, RenderProperty} from '@/types/agGrid/agGrid';
import {ColDef} from '@/third-party/devops-federation/types';
import {formatContent, formatters} from '../dataset/datasetFormatContent';

// 自定义渲染组件，互斥关系
const customCellRenderers = [
    {
        code: '9',
        cellRenderer: 'markdownRenderer',
    },
    {
        code: 'image',
        cellRenderer: 'imageRenderer',
    },
];

export const applyColumnDefFormatters = (formatterCodes: string[] = [], columnDef: AGColDef) => {
    const {headerComponentParams} = columnDef;

    columnDef.valueFormatter = (params: any) => {
        return formatterCodes.length ? formatContent(params.value, formatterCodes) : params.value;
    };

    columnDef.tooltipValueGetter = (params: any) => {
        return formatterCodes.length ? formatContent(params.value, formatterCodes) : params.value;
    };

    let isFindCustomCellRenderer = false;
    // 倒序遍历，以最后选择的自定义组件为准
    for (let i = formatterCodes.length - 1; i >= 0; i--) {
        const customCellRenderer = customCellRenderers.find(item => item.code === formatterCodes[i]);
        if (customCellRenderer) {
            columnDef.cellRenderer = customCellRenderer.cellRenderer;
            isFindCustomCellRenderer = true;
            break;
        }
    }

    if (!isFindCustomCellRenderer) {
        columnDef.cellRenderer = null;
    }

    columnDef.headerComponentParams = {
        ...headerComponentParams,
        formatterCodes: [...formatterCodes],
    };
};

export const applyColumnDefsRenderProperties = (renderProperties: RenderProperty[], columnDefs: AGColDef[]) => {
    const formattedColumns = renderProperties.filter(
        item => item.opObj === 'COLUMN' && item.name === 'formatter' && item.value !== ''
    );

    formattedColumns.forEach(item => {
        const findColumn = columnDefs.find(column => column.field === item.colId);
        if (findColumn) {
            const formatterCodes = item?.value?.split(',') ?? [];
            applyColumnDefFormatters(formatterCodes, findColumn);
        }
    });
};

export const applyRowHeight = (renderProperties: RenderProperty[], gridRef: any) => {
    const items = renderProperties.filter(
        item => item.opObj === 'TABLE' && item.name === 'height' && item.value !== ''
    );

    if (items.length) {
        const columnDefs = (gridRef.current?.api?.getColumnDefs() ?? []) as ColDef[];
        // -1 表示自适应高度
        if (Number(items[0].value) === -1) {
            for (const columnDef of columnDefs) {
                columnDef.autoHeight = true;
                columnDef.cellEditorParams = {
                    ...columnDef.cellEditorParams,
                    rows: 25,
                };
            }
        }
        else {
            const newRowHeight = Number(items[0].value);
            const newEditorRows = Math.ceil(newRowHeight / 19);
            for (const columnDef of columnDefs) {
                columnDef.autoHeight = false;
                columnDef.cellEditorParams = {
                    ...columnDef.cellEditorParams,
                    rows: newEditorRows < 10 ? 10 : (newEditorRows > 25 ? 25 : newEditorRows),
                };
            }

            gridRef.current?.api?.setGridOption('rowHeight', newRowHeight);
        }

        gridRef.current?.api?.setGridOption('columnDefs', columnDefs);
    } else {
        // 行高默认 40
        gridRef.current?.api?.setGridOption('rowHeight', 40);
    }
};

export const exitEditAllHeaderNames = (gridRef: any) => {
    const columnDefs = (gridRef.current?.api?.getColumnDefs() ?? []) as ColDef[];
    for (const columnDef of columnDefs) {
        const {headerComponentParams} = columnDef;
        columnDef.headerComponentParams = {
            ...headerComponentParams,
            isEditing: false,
        };
    }

    gridRef.current?.api?.setGridOption('columnDefs', columnDefs);
};

export const agGridFormatters = [
    ...formatters,
    {
        id: 'image',
        label: '图片格式',
        shortLabel: '图片',
    },
];
