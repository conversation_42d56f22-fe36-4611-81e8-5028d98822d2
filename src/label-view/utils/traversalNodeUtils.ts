/* eslint-disable max-lines */
import {traversalNode} from './traversalNode';

export const getHasSourceName = (root: HTMLElement): boolean => {
    let hasSourceName = false;
    const callback = (node: HTMLElement) => {
        const sourceName = node?.getAttribute('sourceName');
        if (sourceName) {
            hasSourceName = true;
        }
    };
    traversalNode(root, callback);
    return hasSourceName;
};

export const getRootNameValueMap = (root: HTMLElement) => {
    const nameValue: Record<string, string> = {};
    const callback = (node: HTMLElement) => {
        const name = node?.getAttribute('name');
        const value = node?.getAttribute('value');
        if (name) {
            nameValue[name] = value;
        }
    };
    traversalNode(root, callback);
    return nameValue;
};

export const getRootBackgroundMap = (root: HTMLElement) => {
    const backgroundMap: Record<string, string> = {};
    const callback = (node: HTMLElement) => {
        if (node?.nodeName === 'Label') {
            const key = node?.getAttribute('value');
            const background = node?.getAttribute('background');
            if (key) {
                backgroundMap[key] = background;
            }
        }
    };
    traversalNode(root, callback);
    return backgroundMap;
};

export const getRootRankerMap = (root: HTMLElement) => {
    const rankerMap: Record<string, HTMLElement> = {};
    const callback = (node: HTMLElement) => {
        if (node?.nodeName === 'Ranker' || node?.nodeName === 'List') {
            const key = node?.getAttribute('toName');
            if (key) {
                rankerMap[key] = node;
            }
        }
    };
    traversalNode(root, callback);
    return rankerMap;
};

/**
 * @param root
 * @returns 返回一个对象，key 为 name, value 为当前节点
 */
export const getNameMap = (root: HTMLElement) => {
    const nameMap: Record<string, HTMLElement> = {};
    const callback = (node: HTMLElement) => {
        const key = node?.getAttribute('name');
        if (key) {
            nameMap[key] = node;
        }
    };
    traversalNode(root, callback);
    return nameMap;
};

/**
 * @param root
 * @returns 返回一个对象，key 为 toName, value 为当前节点
 */
export const getRootToNameMap = (root: HTMLElement) => {
    const toNameMap: Record<string, HTMLElement> = {};
    const callback = (node: HTMLElement) => {
        const key = node?.getAttribute('toName');
        if (key) {
            toNameMap[key] = node;
        }
    };
    traversalNode(root, callback);
    return toNameMap;
};

export const getRequiredNameMap = (root: HTMLElement) => {
    const requiredNameMap: Record<string, string> = {};
    const callback = (node: HTMLElement) => {
        const name = node?.getAttribute('name');
        const required = node?.getAttribute('required');
        if (name && (required === 'true')) {
            requiredNameMap[name] = node?.tagName;
        }
    };
    traversalNode(root, callback);
    return requiredNameMap;
};

interface BucketProps {
    name: string;
    title: string;
}

export const getNodeBucketPropsList = (root: HTMLElement) => {
    if (!root) {
        return [];
    }
    const bucketPropsList: BucketProps[] = [];
    const callback = (node: HTMLElement) => {
        if (node?.nodeName === 'Bucket') {
            const name = node?.getAttribute('name');
            const title = node?.getAttribute('title');
            if (name) {
                bucketPropsList.push({name, title});
            }
        }
    };
    traversalNode(root, callback);
    return bucketPropsList;
};

export const getNodeChildrenList = (root: HTMLElement, targetNodeName: string) => {
    const childrenList: HTMLElement[] = [];
    const callback = (node: HTMLElement) => {
        if (node?.nodeName === targetNodeName) {
            childrenList.push(node);
        }
    };
    traversalNode(root, callback);
    return childrenList;
};

interface TreeDataItem {
    title: string;
    value: string;
    key: string;
    children: TreeDataItem[];
}

export const traversalNodeChoiceToOptions = (node: HTMLElement, ref: TreeDataItem[]): TreeDataItem[] => {
    const childNodes = Array.from(node.childNodes);
    childNodes.forEach(child => {
        if (child.nodeType === 1) {
            const childNode = child as HTMLElement;
            if (childNode?.nodeName === 'Choice') {
                const value = childNode?.getAttribute('value');
                const title = childNode?.getAttribute('title');
                const children = traversalNodeChoiceToOptions(childNode, []);
                ref.push({
                    title: title ?? value,
                    value: value,
                    key: value,
                    children: children,
                });
            }
        }
    });
    return ref;
};
