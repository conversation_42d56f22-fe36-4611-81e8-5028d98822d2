import {ComponentType} from 'react';
import {CodeComponent} from '../components/Code/Component';
import {CodeEditorComponent} from '../components/CodeEditor/Component';
import {DiffComponent} from '../components/Diff/Component';
import {MarkdownComponent} from '../components/Markdown/Component';
import {EchartsComponent} from '../components/Echarts/Component';
import {ImageComponent} from '../components/Image/Component';
import {TextComponent} from '../components/Text/Component';
import {NumberComponent} from '../components/Number/Component';
import {TextAreaComponent} from '../components/TextArea/Component';
import {ChoicesComponent} from '../components/Choices/Component';
import {JsonComponent} from '../components/Json/Component';
import {TaxonomyComponent} from '../components/Taxonomy/Component';
import {LabelsComponent} from '../components/Labels/Component';
import {RankerComponent} from '../components/Ranker/Component';
import {PdfComponent} from '../components/Pdf/Component';
import {VideoComponent} from '../components/Video/Component';

// Visual
import {CollapseComponent} from '../components/Collapse/Component';
import {FieldComponent} from '../components/Field/Component';
import {HeaderComponent} from '../components/Header/Component';
import {AvailableNodeName} from '../types/node';

const Noop = (): null => null;

// [Component, initialProps]
type Result = ComponentType<any> | string;

// eslint-disable-next-line complexity
export const getNodeComponent = (nodeName: AvailableNodeName): Result => {
    if (nodeName.toLowerCase() === nodeName) {
        return nodeName;
    }

    switch (nodeName) {
        case 'Text':
            return TextComponent;
        case 'Labels':
        case 'RectangleLabels':
            return LabelsComponent;
        case 'Label':
            return Noop;
        case 'Taxonomy':
            return TaxonomyComponent;
        case 'Choices':
            return ChoicesComponent;
        case 'Choice':
            return Noop;
        case 'TextArea':
            return TextAreaComponent;
        case 'Json':
            return JsonComponent;
        case 'Code':
            return CodeComponent;
        case 'CodeEditor':
            return CodeEditorComponent;
        case 'Diff':
            return DiffComponent;
        case 'Markdown':
            return MarkdownComponent;
        case 'Echarts':
            return EchartsComponent;
        case 'Image':
            return ImageComponent;
        case 'Number':
            return NumberComponent;
        case 'List':
        case 'Ranker':
            return RankerComponent;
        case 'Bucket':
            return Noop;
        case 'Pdf':
            return PdfComponent;
        case 'Video':
            return VideoComponent;
        /* Visual */
        case 'Collapse':
            return CollapseComponent;
        case 'Panel':
            return Noop;
        case 'Field':
            return FieldComponent;
        case 'Header':
            return HeaderComponent;
        case 'Style':
            return 'style';
        case 'View':
        default:
            return 'div';
    }
};
