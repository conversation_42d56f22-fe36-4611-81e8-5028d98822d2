/* eslint-disable max-len */
import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';

const exampleCode = `<View>
    <Header value="PDF文档标注示例" />
    <Pdf
        name="document"
        file="https://now.bdstatic.com/stash/v1/db94409/comate-stack-fe/6caa38c/assets/pdf-example.pdf"
    />
</View>`;

const dataSource = [
    {
        name: 'name',
        type: 'string',
        defaultValue: '',
        description: '字段名称，用于标识该PDF组件',
        required: true,
    },
    {
        name: 'file',
        type: 'string',
        defaultValue: '',
        description: 'PDF文档的URL地址',
        required: true,
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '自定义类名',
    },
];

const features = [
    {text: 'PDF预览：支持在线预览PDF文档内容'},
    {text: '自适应布局：自动适应容器宽度，提供最佳阅读体验'},
    {text: '分页显示：支持多页PDF文档的分页展示'},
    {text: '加载状态：提供文档加载状态和错误提示'},
    {text: '跨域支持：支持加载允许跨域访问的PDF文档'},
];

export const PdfDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="PDF 文档" />
            <p>PDF 组件用于展示和标注PDF文档，提供了在线预览和交互功能，支持以下特性：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <Divider />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
        </div>
    );
};
