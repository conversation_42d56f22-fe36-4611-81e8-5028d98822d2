/* eslint-disable max-len */
import {Divider, Typography} from 'antd';
import {marginTop} from '@panda-design/components';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';
import {ParamConfig} from '@/label-view/types/editor';

const {Title} = Typography;

const classificationExampleCode = `<View>
    <Header value="图片分类标注示例" />
    <Image name="myImage1" value="$imageUrl" width="300px"/>
    <Choices name="myChoice1" toName="myImage1">
        <Choice value="风景" />
        <Choice value="壁纸" />
    </Choices>
</View>
<!-- {
    "data": {
        "imageUrl": ""
    }
} -->`;

const rectangleExampleCode = `<View>
    <Header value="图片框选标注（矩形）示例" />
    <Image name="myImage1" value="$imageUrl" width="300px"/>
    <RectangleLabels name="myRectangleLabels1" toName="myImage1">
        <Label value="天空" background="blue"/>
        <Label value="陆地" background="#87d068"/>
        <Label value="海洋" background="#2db7f5"/>
    </RectangleLabels>
</View>
<!-- {
    "data": {
        "imageUrl": ""
    }
} -->`;

const dataSource: ParamConfig[] = [
    {
        name: 'name',
        type: 'string',
        defaultValue: '',
        description: '当前组件的名称',
    },
    {
        name: 'value',
        type: 'string',
        defaultValue: '',
        description: '包含图像路径或URL的数据字段，支持使用变量，如 $imageUrl，则会读取数据集中的 imageUrl 字段作为图像路真正URL',
    },
    {
        name: '[width]',
        type: 'string',
        defaultValue: '',
        description: '指定图像的宽度，如 200 等，如不指定则图片宽度会自适应父容器大小，图片高度会自适应保持比例',
    },
];

const features = [
    {text: '图片展示：支持放大、缩小、旋转等操作'},
    {text: '图片分类标注：结合使用 Choices 或 Labels 组件进行图片分类标注'},
    {text: '图片框选标注（矩形）：结合使用 RectangleLabels 组件进行图片框选标注（RectangleLabels 组件与 Labels 组件用法一致）'},
];

const classificationExampleGuides = [
    {text: '进行图片分类标注，可结合使用 Choices 或 Labels 组件，Image 的 name 需与 Choices 或 Labels 的 toName 保持一致以建立绑定关系。'},
];

const rectangleExampleGuides = [
    {text: '进行图片框选标注（矩形），需结合使用 RectangleLabels 组件，Image 的 name 需与 RectangleLabels 的 toName 保持一致以建立绑定关系。'},
    {text: '选择一个 Label 标签后开始在图片中绘制矩形。'},
    {text: '选中绘制的矩形，可以进行拖拽、缩放、旋转等操作。'},
];

export const ImageDocument = () => {
    return (
        <div>
            <PageLayoutHeader title="图片 Image" />
            <p>Image 组件用于展示和标注图片，支持以下功能：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Title level={4}>图片分类标注</Title>
            <UnorderedList items={classificationExampleGuides} />
            <Example code={classificationExampleCode} />
            <Title level={4} className={marginTop(20)}>图片框选标注（矩形）</Title>
            <UnorderedList items={rectangleExampleGuides} />
            <Example code={rectangleExampleCode} />
            <Divider />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
        </div>
    );
};
