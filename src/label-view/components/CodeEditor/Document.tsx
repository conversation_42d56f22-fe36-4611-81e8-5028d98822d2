import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {ParamConfig} from '@/label-view/types/editor';

const exampleCode = `<CodeEditor
    name="code"
    toName="code"
    value="$code"
    fileName="example.ts"
    language="typescript"
/>
<!-- {
    "data": {
        "code": "// Your code here"
    }
} -->
`;

const dataSource: ParamConfig[] = [
    {
        name: 'name',
        type: 'string',
        defaultValue: '',
        description: '导出所需字段名',
    },
    {
        name: 'toName',
        type: 'string',
        defaultValue: '',
        description: '所标注的字段名',
    },
    {
        name: 'value',
        type: 'string',
        defaultValue: '',
        description: '初始代码值',
    },
    {
        name: '[fileName]',
        type: 'string',
        defaultValue: '',
        description: '文件名',
    },
    {
        name: '[language]',
        type: 'string',
        defaultValue: '',
        description: '代码语言',
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '类名',
    },
    {
        name: '[height]',
        type: 'string',
        defaultValue: '',
        description: '编辑器高度',
    },
    {
        name: '[width]',
        type: 'string',
        defaultValue: '',
        description: '编辑器宽度',
    },
];

export const CodeEditorDocument = () => {
    return (
        <div>
            <PageLayoutHeader title="代码编辑 CodeEditor" />
            <p>用于编辑代码的组件，支持多种编程语言。</p>
            <p>给定 fileName 或 language，可以开启合适的语法高亮。</p>
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
        </div>
    );
};
