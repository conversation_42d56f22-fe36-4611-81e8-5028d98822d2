import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';

const exampleHtmlCode = `<View>
    <Header value="HTML 示例" />
    <View>
        <View>View 会被渲染为 div</View>
    </View>
    <View>
        <View as="span">也可以定义为 span 标签，</View>
        <View as="span">多个 span 标签不会换行</View>
    </View>
</View>`;

const dataSource = [
    {
        name: '[as]',
        type: 'string',
        defaultValue: 'div',
        description: 'HTML 标签名称，如 div、span 等',
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '自定义样式类名',
    },
];
const features = [
    {text: '支持指定 HTML 标签类型'},
    {text: '支持添加自定义样式类名'},
    {text: '可以包含任意子组件'},
    {text: '用于布局和样式组织'},
];

const tips = [
    {text: '使用标准的 HTML 语法'},
    {text: '注意 XSS 安全问题，避免直接渲染不可信的 HTML 内容'},
    {text: '建议使用 CSS-in-JS 方案管理样式'},
    {text: '可以通过 className 属性添加自定义样式'},
];

export const HtmlDocument = () => {
    return (
        <div>
            <PageLayoutHeader title="Html标签 View" />
            <p>视图容器组件，用于创建自定义的 HTML 元素容器。主要功能包括：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="基础示例" />
            <Example code={exampleHtmlCode} />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
            <PageLayoutHeader title="使用提示" />
            <UnorderedList items={tips} />
        </div>
    );
};

const exampleStyleCode = `<View>
    <Header value="样式示例" />
    <Style>
        .red {
            color: red;
        }
        .blue {
            color: blue;
        }
    </Style>
    <View className="red">这是一段红色文本</View>
    <View className="blue">这是一段蓝色文本</View>
</View>`;

export const StyleDocument = () => {
    return (
        <div>
            <PageLayoutHeader title="样式 Style" />
            <p>样式组件，用于创建自定义的样式，并在其他组件中使用。使用 css 书写。</p>
            <Divider />
            <PageLayoutHeader title="基础示例" />
            <Example code={exampleStyleCode} />
        </div>
    );
};
