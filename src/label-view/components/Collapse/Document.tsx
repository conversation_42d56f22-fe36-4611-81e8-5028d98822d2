import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';

const exampleCode = `<View>
    <Header value="折叠面板示例" />
    <Collapse name="collapse-example">
        <Panel value="基本信息">
            <Text name="text-1" value="这是基本信息内容" />
        </Panel>
        <Panel value="详细说明">
            <Text name="text-2" value="这是详细说明内容" />
            <TextArea
                name="comment"
                rows="3"
            />
        </Panel>
    </Collapse>
</View>`;

const dataSource = [
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '自定义样式类名',
    },
];

const panelDataSource = [
    {
        name: 'value',
        type: 'string',
        defaultValue: '',
        description: '面板标题文本',
    },
];
const features = [
    {text: '支持内容的展开和收起'},
    {text: '支持自定义面板标题'},
    {text: '支持多个面板的独立控制'},
    {text: '支持与其他组件关联'},
    {text: '支持自定义展开图标'},
];

const tips = [
    {text: '建议合理组织面板内容，避免内容过多'},
    {text: '可以通过 style 属性自定义样式'},
    {text: '使用 defaultActiveKey 设置默认展开的面板'},
    {text: '注意处理面板展开/收起的性能问题'},
];

export const CollapseDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="折叠面板 Collapse" />
            <p>折叠面板组件，用于将内容区域折叠/展开。主要功能包括：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
            <PageLayoutHeader title="Panel 参数说明" />
            <ParamsTable dataSource={panelDataSource} />
            <PageLayoutHeader title="使用提示" />
            <UnorderedList items={tips} />
        </div>
    );
};
