/* eslint-disable max-len */
import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';

const exampleCode = `<View>
    <Header value="标签组示例" />
    <Text name="text" value="$text" />
    <Labels name="label" toName="text">
        <Label value="important" label="重要" background="#ff4d4f" />
        <Label value="review" label="待审核" background="#faad14" />
        <Label value="completed" label="已完成" background="#52c41a" />
    </Labels>
    <Text name="tip" value="或者你也可以从数据生成标签" />
    <Labels
        name="label2"
        toName="text"
        items="$labelItems"
    />
</View>
<!-- {
    "data": {
        "text": "这是一段需要标注的文本内容",
        "labelItems": [
            {"value": "error", "label": "错误", "background": "#ff4d4f"},
            {"value": "warning", "label": "警告", "background": "#faad14"},
            {"value": "success", "label": "成功", "background": "#52c41a"}
        ]
    }
} -->`;

const dataSource = [
    {
        name: 'name',
        type: 'string',
        defaultValue: '',
        description: '组件唯一标识名称',
    },
    {
        name: 'items',
        type: 'Label[]',
        defaultValue: '[]',
        description: '标签列表数据，每个标签包含value（值）、label（显示文本）和background（背景色）',
    },
    {
        name: 'toName',
        type: 'string',
        defaultValue: '',
        description: '关联的目标组件名称，用于指定标签要应用到哪个组件上',
    },
];

const labelValueItemSource = [
    {
        name: 'value',
        type: 'string',
        defaultValue: '',
        description: '标签的值',
    },
    {
        name: '[label]',
        type: 'string',
        defaultValue: '',
        description: '标签的显示文本，如果不提供则使用value',
    },
    {
        name: '[background]',
        type: 'string',
        defaultValue: '',
        description: '标签的背景颜色',
    },
];

const features = [
    {text: '支持自定义标签文本和值'},
    {text: '支持自定义标签背景颜色'},
    {text: '可以通过XML配置或直接传入 items 属性设置标签'},
    {text: '支持与其他组件关联，实现标注功能'},
];

export const LabelsDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="标签组 Labels" />
            <p>标签组组件，用于展示和管理一组标签。主要功能包括：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <PageLayoutHeader title="Labels 参数说明" />
            <ParamsTable dataSource={dataSource} />
            <PageLayoutHeader title="Label 参数说明" />
            <ParamsTable dataSource={labelValueItemSource} />
        </div>
    );
};
