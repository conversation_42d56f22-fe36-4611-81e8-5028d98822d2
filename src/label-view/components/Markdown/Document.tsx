/* eslint-disable max-len */
import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';

const exampleCode = `<View>
    <Header value="Markdown 标注对象" />
    <Markdown value="$markdownContent" />
</View>
<!-- {
    "data": {
        "markdownContent": "# 标注系统\\n\\n**版本**: 1.0.0\\n\\n## 功能特点\\n\\n- ✨ Markdown 实时渲染\\n- 💫 支持 GitHub 风格语法\\n- 📐 支持数学公式"
    }
} -->`;

const dataSource = [
    {
        name: 'value',
        type: 'string',
        defaultValue: '',
        description: 'Markdown 内容字符串，如果提供，将直接渲染，否则尝试通过 refName 获取动态内容',
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '类名',
    },
    {
        name: '[refName]',
        type: 'string',
        defaultValue: '',
        description: '提供一个引用名称，用于获取动态内容',
    },
];
const features = [
    {text: 'GitHub 风格语法：支持表格、任务列表、自动链接等 GFM 特性'},
    {text: '数学公式：支持 LaTeX 数学公式的编写和渲染'},
    {text: '实时渲染：内容变更时自动更新预览'},
    {text: '动态内容：通过 refName 支持动态内容更新'},
    {text: '样式主题：使用 GitHub 风格的 Markdown 样式'},
];

const tips = [
    {text: '使用标准的 Markdown 语法编写内容'},
    {text: '代码块建议指定语言以获得更好的语法高亮效果'},
    {text: '图片建议使用相对路径或完整的 URL'},
    {text: '表格建议使用标准的 Markdown 表格语法'},
];

export const MarkdownDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="Markdown 编辑器" />
            <p>用于渲染和编辑 Markdown 格式的文本内容，支持实时预览和丰富的扩展功能。支持以下功能：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
            <PageLayoutHeader title="使用提示" />
            <UnorderedList items={tips} />
        </div>
    );
};
