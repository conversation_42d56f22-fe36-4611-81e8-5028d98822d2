import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';

const exampleCode = `<View>
    <Header value="表单字段示例" />
    <Field
        name="field-example"
        label="用户名"
        tooltip="请输入您的用户名"
    >
        <TextArea
            name="username"
            rows="1"
            mode="single"
        />
    </Field>
</View>`;

const dataSource = [
    {
        name: 'label',
        type: 'string',
        defaultValue: '',
        description: '字段标签文本',
    },
    {
        name: '[width]',
        type: 'string',
        defaultValue: '',
        description: '字段宽度，单位为像素',
    },
    {
        name: '[tooltip]',
        type: 'string',
        defaultValue: '',
        description: '字段提示信息',
    },
    {
        name: '[extra]',
        type: 'string',
        defaultValue: '',
        description: '额外的说明文本',
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '自定义样式类名',
    },
];
const features = [
    {text: '支持自定义字段名和值'},
    {text: '支持字段值的实时更新'},
    {text: '支持字段的禁用状态'},
    {text: '支持与其他组件关联'},
    {text: '支持自定义字段样式'},
];

const tips = [
    {text: '建议为每个字段设置语义化的名称'},
    {text: '可以通过 style 属性自定义样式'},
    {text: '使用 disabled 属性控制字段是否可编辑'},
    {text: '注意处理字段值的类型转换'},
];

export const FieldDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="字段 Field" />
            <p>表单字段组件，用于为表单控件提供统一的布局和样式。主要功能包括：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
            <PageLayoutHeader title="使用提示" />
            <UnorderedList items={tips} />
        </div>
    );
};
