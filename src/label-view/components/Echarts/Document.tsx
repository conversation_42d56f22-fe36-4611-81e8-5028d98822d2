import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';
import {ParamConfig} from '@/label-view/types/editor';

const exampleCode = `<View>
    <Header value="Echarts 图表示例" />
    <Echarts
        name="echarts-example"
        option="$chart-data"
    />
</View>
<!-- {
    "data": {
        "chart-data": {
            "xAxis": {
                "type": "category",
                "data": ["Mon", "Tue", "Wed", "Thu", "Fri"]
            },
            "yAxis": {
                "type": "value"
            },
            "series": [{
                "data": [100, 200, 150, 250, 180],
                "type": "bar"
            }]
        }
    }
} -->`;


const dataSource: ParamConfig[] = [
    {
        name: 'name',
        type: 'string',
        defaultValue: '',
        description: '导出所需字段名',
    },
    {
        name: 'seriesType',
        type: 'string',
        defaultValue: 'line',
        description: '图表类型，如：line、bar、pie等',
    },
    {
        name: 'option',
        type: 'object',
        defaultValue: '{}',
        description: 'Echarts 图表配置项',
    },
    {
        name: '[seriesRefName]',
        type: 'string',
        defaultValue: '',
        description: '数据源字段名，用于引用数据',
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '类名',
    },
];

const features = [
    {text: '支持多种图表类型展示'},
    {text: '支持自定义图表配置'},
    {text: '支持引用外部数据源'},
    {text: '自动格式化数据'},
];

export const EchartsDocument = () => {
    return (
        <div>
            <PageLayoutHeader title="图表 Echarts" />
            <p>用于展示各类图表的组件。支持以下功能:</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
        </div>
    );
};
