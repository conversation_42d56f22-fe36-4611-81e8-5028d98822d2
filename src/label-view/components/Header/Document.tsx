import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';

const exampleCode = `<View>
    <Header value="标题示例" />
    <Header
        name="header-example"
        value="二级标题"
        level="2"
    />
    <Text name="text-1" value="这是一段普通文本内容" />
</View>`;

const dataSource = [
    {
        name: 'value',
        type: 'string',
        defaultValue: '',
        description: '标题文本内容',
    },
    {
        name: '[level]',
        type: "'1' | '2' | '3' | '4' | '5'",
        defaultValue: '1',
        description: '标题级别，对应 h1-h5',
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '自定义样式类名',
    },
];
const features = [
    {text: '支持自定义标题文本'},
    {text: '支持标题级别的设置'},
    {text: '支持自定义样式'},
    {text: '支持与其他组件关联'},
    {text: '支持标题的动态更新'},
];

const tips = [
    {text: '建议根据内容层级选择合适的标题级别'},
    {text: '可以通过 style 属性自定义样式'},
    {text: '注意保持标题层级的语义化'},
    {text: '避免过多的标题嵌套'},
];

export const HeaderDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="标题 Header" />
            <p>标题组件，用于展示各级标题。主要功能包括：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
            <PageLayoutHeader title="使用提示" />
            <UnorderedList items={tips} />
        </div>
    );
};
