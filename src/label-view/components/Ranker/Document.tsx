import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';

const exampleCode = `<View>
    <Header value="排序组件示例" />
    <Text name="text-1" value="请对以下选项进行排序:" />
    <Ranker
        name="ranker-example"
        title="候选项"
        value="$ranker-example"
    />
</View>
<!-- {
  "data": {
    "ranker-example": [
      {
        "id": "1",
        "title": "重要且紧急",
        "body": "需要立即处理的任务"
      },
      {
        "id": "2",
        "title": "重要不紧急",
        "body": "需要规划的任务"
      },
      {
        "id": "3",
        "title": "紧急不重要",
        "body": "可以委托的任务"
      },
      {
        "id": "4",
        "title": "不重要不紧急",
        "body": "可以暂缓的任务"
      }
    ]
  }
} -->`;

const dataSource = [
    {
        name: 'name',
        type: 'string',
        defaultValue: '',
        description: '组件唯一标识名称',
    },
    {
        name: 'value',
        type: 'RankerItemData[]',
        defaultValue: '[]',
        description: '排序项数据，每项包含 id、title、body 和 html 属性',
    },
    {
        name: '[title]',
        type: 'string',
        defaultValue: '',
        description: '排序列表的标题',
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '自定义样式类名',
    },
];
const features = [
    {text: '支持拖拽排序'},
    {text: '支持自定义排序项的内容'},
    {text: '支持排序结果的实时更新'},
    {text: '支持与其他组件关联'},
    {text: '支持自定义排序规则'},
];

const tips = [
    {text: '建议为每个排序项设置唯一的 key'},
    {text: '可以通过 style 属性自定义排序项样式'},
    {text: '注意处理拖拽过程中的性能问题'},
    {text: '可以通过 onChange 事件获取最新排序结果'},
];

export const RankerDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="排序 Ranker" />
            <p>排序组件，用于对列表项进行拖拽排序。主要功能包括：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
            <PageLayoutHeader title="使用提示" />
            <UnorderedList items={tips} />
        </div>
    );
};
