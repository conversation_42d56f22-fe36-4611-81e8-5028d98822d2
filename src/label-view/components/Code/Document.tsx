import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';
import {ParamConfig} from '@/label-view/types/editor';


const exampleCode = `<View>
    <Header value="代码标注对象" />
    <Code
        name="code-example"
        value="$json"
        language="json"
        enableJsonFormat="true"
        enableAutoScrollHighlight="true"
        fileName="config.json"
    />
</View>
<!-- {
    "data": {
        "json": {
            "name": "标注系统",
            "version": "1.0.0",
            "features": [
                "语法高亮",
                "JSON 格式化",
                "自动滚动定位"
            ]
        }
    }
} -->`;

const dataSource: ParamConfig[] = [
    {
        name: 'name',
        type: 'string',
        defaultValue: '',
        description: '导出所需字段名',
    },
    {
        name: 'value',
        type: 'string',
        defaultValue: '',
        description: '初始代码值',
    },
    {
        name: '[prefix]',
        type: 'string',
        defaultValue: '',
        description: '代码前缀，用于高亮显示时的前置内容',
    },
    {
        name: '[suffix]',
        type: 'string',
        defaultValue: '',
        description: '代码后缀，用于高亮显示时的后置内容',
    },
    {
        name: '[code]',
        type: 'string',
        defaultValue: '',
        description: '需要高亮的代码片段',
    },
    {
        name: '[toName]',
        type: 'string',
        defaultValue: '',
        description: '所标注的字段名',
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '类名',
    },
    {
        name: '[language]',
        type: 'string',
        defaultValue: 'plaintext',
        description: '代码语言，用于语法高亮，如：javascript、python、java等',
    },
    {
        name: '[fileName]',
        type: 'string',
        defaultValue: '',
        description: '显示的文件名',
    },
    {
        name: '[enableJsonFormat]',
        type: 'boolean',
        defaultValue: 'false',
        description: '是否启用 JSON 格式化，仅当 language 为 json 时生效',
    },
    {
        name: '[enableAutoScrollHighlight]',
        type: 'boolean',
        defaultValue: 'false',
        description: '是否启用自动滚动到高亮位置',
    },
];
const features = [
    {text: '多语言语法高亮：支持 JavaScript、Python、Java 等多种编程语言'},
    {text: 'JSON 格式化：自动格式化 JSON 数据，提升可读性'},
    {text: '自动滚动定位：可自动滚动到高亮的代码位置'},
    {text: '文件名显示：支持显示代码所属的文件名'},
];

const tips = [
    {text: '建议在 XML 配置中指定代码语言，以获得更好的语法高亮效果'},
    {text: '可以通过 language 属性指定代码语言'},
    {text: '可以通过 showLineNumbers 属性控制是否显示行号'},
    {text: '可以通过 foldable 属性控制是否允许代码折叠'},
];

export const CodeDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="代码 Code" />
            <p>代码展示组件，用于展示和标注代码块。主要功能包括：</p>
            <UnorderedList items={features} />
            <p>给定 fileName 或 language，可以开启合适的语法高亮。</p>
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
            <PageLayoutHeader title="使用提示" />
            <UnorderedList items={tips} />
        </div>
    );
};
