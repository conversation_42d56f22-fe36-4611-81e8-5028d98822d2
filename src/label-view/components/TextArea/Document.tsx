/* eslint-disable max-len */
import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';

const exampleCode = `<View>
    <Header value="文本输入示例" />
    <Text name="text-1" value="请在下方输入您的评论：" />
    <TextArea
        name="textarea-example"
        toName="text-1"
        rows="3"
    />
</View>`;

const singleModeExample = `<View>
    <Header value="单行模式示例" />
    <Text name="text-2" value="请输入单行文本：" />
    <TextArea
        name="textarea-single"
        toName="text-2"
        mode="single"
        rows="1"
    />
</View>`;

const dataSource = [
    {
        name: 'name',
        type: 'string',
        defaultValue: '',
        description: '组件唯一标识名称',
    },
    {
        name: 'toName',
        type: 'string',
        defaultValue: '',
        description: '关联的目标组件名称',
    },
    {
        name: '[mode]',
        type: "'single'",
        defaultValue: '',
        description: '输入模式。设置为 "single" 时为单行模式，输入内容会立即提交；不设置时为多行模式，需要点击确定按钮或使用快捷键提交',
    },
    {
        name: '[rows]',
        type: 'string',
        defaultValue: '1',
        description: '文本框的行数',
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '类名',
    },
    {
        name: '[sourceName]',
        type: 'string',
        defaultValue: '',
        description: '数据源名称，用于标识数据来源',
    },
    {
        name: '[value]',
        type: 'string',
        defaultValue: '',
        description: '文本框的值',
    },
    {
        name: '[defaultValue]',
        type: 'string',
        defaultValue: '',
        description: '文本框的默认值',
    },
];
const features = [
    {text: '支持多行文本输入'},
    {text: '支持单行和多行两种模式'},
    {
        text: '多行模式下支持快捷键：',
        children: [
            {text: 'Enter：提交文本'},
            {text: 'Command + Enter：换行'},
        ],
    },
    {text: '多行模式下显示历史输入记录'},
    {text: '支持自定义行数'},
    {text: '支持与其他组件关联'},
];

const tips = [
    {text: '使用多行模式时注意设置合适的行高'},
    {text: '建议配置合理的快捷键提示'},
    {text: '可以通过 maxRows 属性限制最大行数'},
    {text: '注意处理输入内容的换行符'},
];

export const TextAreaDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="文本域 TextArea" />
            <p>文本域组件，用于多行文本的输入和编辑。主要功能包括：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <Divider />
            <PageLayoutHeader title="单行模式示例" />
            <p>单行模式下，输入内容会立即提交，不需要点击确定按钮。</p>
            <Example code={singleModeExample} />
            <Divider />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
            <PageLayoutHeader title="注意" />
            <UnorderedList items={tips} />
        </div>
    );
};
