/* eslint-disable max-len */
import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';
import {exampleCode, exampleRefCode} from './exampleCode';

const dataSource = [
    {
        name: 'name',
        type: 'string',
        defaultValue: '',
        description: '组件唯一标识名称',
    },
    {
        name: 'toName',
        type: 'string',
        defaultValue: '',
        description: '关联的目标组件名称，用于指定分类要应用到哪个组件上',
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '自定义样式类名',
    },
    {
        name: '[sourceName]',
        type: 'string',
        defaultValue: '',
        description: '数据源名称，用于标识数据来源',
    },
    {
        name: '[defaultValue]',
        type: 'string[]',
        defaultValue: '[]',
        description: '默认选中的分类值数组',
    },
];

const choiceSource = [
    {
        name: 'value',
        type: 'string',
        defaultValue: '',
        description: '分类选项的值',
    },
    {
        name: '[title]',
        type: 'string',
        defaultValue: '',
        description: '分类选项的显示文本，如果不提供则使用value',
    },
];
const features = [
    {text: '支持树形结构的分类选择'},
    {text: '支持多级分类的展示'},
    {text: '支持展开/收起分类节点'},
    {text: '支持搜索过滤分类项'},
    {text: '支持与其他组件关联'},
];

const tips = [
    {text: '建议合理设计分类层级，避免过深的嵌套'},
    {text: '可以通过 showSearch 属性启用搜索功能'},
    {text: '使用 defaultExpandAll 属性控制是否默认展开所有节点'},
    {text: '注意处理分类数据的层级关系'},
];

export const TaxonomyDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="分类 Taxonomy" />
            <p>分类选择组件，用于展示和选择树形结构的分类数据。主要功能包括：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <p>你也可以从数据生成树结构</p>
            <Example code={exampleRefCode} />
            <Divider />
            <PageLayoutHeader title="Taxonomy 参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
            <PageLayoutHeader title="Choice 参数说明" />
            <ParamsTable dataSource={choiceSource} />
            <PageLayoutHeader title="使用提示" />
            <UnorderedList items={tips} />
        </div>
    );
};
