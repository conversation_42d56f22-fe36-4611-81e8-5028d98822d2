/* eslint-disable max-len */
import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';

const exampleCode = `<View>
    <Header value="数字标注示例" />
    <Number
        name="score"
        toName="text-1"
        min="0"
        max="100"
        defaultValue="50"
    />
</View>`;

const dataSource = [
    {
        name: 'name',
        type: 'string',
        defaultValue: '',
        description: '字段名称，用于标识该数字输入组件',
        required: true,
    },
    {
        name: 'toName',
        type: 'string',
        defaultValue: '',
        description: '关联的目标组件名称',
        required: true,
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '自定义类名',
    },
    {
        name: '[min]',
        type: 'string',
        defaultValue: '',
        description: '最小值',
    },
    {
        name: '[max]',
        type: 'string',
        defaultValue: '',
        description: '最大值',
    },
    {
        name: '[defaultValue]',
        type: 'string',
        defaultValue: '',
        description: '默认值',
    },
    {
        name: '[sourceName]',
        type: 'string',
        defaultValue: '',
        description: '数据源名称，用于关联动态数据',
    },
];

const features = [
    {text: '数字输入：支持输入和选择数字值'},
    {text: '范围限制：可设置最小值和最大值'},
    {text: '默认值：支持设置初始默认值'},
    {text: '动态数据：通过 sourceName 支持动态数据更新'},
    {text: '实时同步：数值变更时自动更新标注结果'},
];

export const NumberDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="Number 数字输入" />
            <p>Number 组件用于数字类型的标注，支持输入和选择数字值，可以设置数值范围，支持以下功能：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
        </div>
    );
};
