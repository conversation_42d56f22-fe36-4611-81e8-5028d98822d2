/* eslint-disable max-len */
import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';

const exampleCode = `<View>
    <Header value="JSON 编辑示例" />
    <Text name="text-1" value="请编辑以下 JSON 数据：" />
    <Json
        name="json-example"
        toName="text-1"
        value="$json-example"
    />
</View>
<!-- {
  "data": {
    "json-example": {
      "name": "example",
      "age": 25,
      "hobbies": ["reading", "coding"],
      "contact": {
        "email": "<EMAIL>",
        "phone": "************"
      }
    }
  }
} -->`;

const dataSource = [
    {
        name: 'node',
        type: 'HTMLElement',
        defaultValue: '',
        description: '目标 DOM 节点',
    },
    {
        name: 'name',
        type: 'string',
        defaultValue: '',
        description: '组件唯一标识名称',
    },
    {
        name: 'toName',
        type: 'string',
        defaultValue: '',
        description: '关联的目标组件名称',
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '类名',
    },
    {
        name: '[sourceName]',
        type: 'string',
        defaultValue: '',
        description: '数据源名称，用于标识数据来源',
    },
    {
        name: '[value]',
        type: 'string',
        defaultValue: '',
        description: 'JSON 数据的初始值',
    },
    {
        name: '[defaultValue]',
        type: 'string',
        defaultValue: '',
        description: 'JSON 数据的默认值，当 value 未提供时使用',
    },
];
const features = [
    {text: '支持 JSON 数据的可视化展示'},
    {text: '支持 JSON 数据的添加、编辑和删除操作'},
    {text: '支持数据类型的自动识别和验证'},
    {text: '支持与其他组件关联'},
    {text: '支持实时保存编辑结果'},
];

const tips = [
    {text: '建议使用标准的 JSON 格式'},
    {text: '编辑时注意保持数据结构的完整性'},
    {text: '可以使用格式化功能提高可读性'},
    {text: '注意处理大型 JSON 数据的性能问题'},
];

export const JsonDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="JSON 编辑器" />
            <p>JSON 编辑器组件，用于 JSON 数据的可视化编辑。主要功能包括：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
            <PageLayoutHeader title="使用提示" />
            <UnorderedList items={tips} />
        </div>
    );
};
