/* eslint-disable max-len */
import {Divider, Typography} from 'antd';
import {marginTop} from '@panda-design/components';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';
import {ParamConfig} from '@/label-view/types/editor';

const {Title} = Typography;

const classificationExampleCode = `<View>
    <Header value="视频分类标注示例" />
    <Video name="myVideo1" value="$videoUrl" height="400px" />
    <Choices name="myChoice1" toName="myVideo1">
        <Choice value="动漫类" />
        <Choice value="科幻类" />
    </Choices>
</View>
<!-- {
    "data": {
        "videoUrl": ""
    }
} -->`;

const timelineExampleCode = `<View>
    <Video name="myVideo1" value="$videoUrl" height="400px" />
    <Labels name="myChoices1" toName="myVideo1">
        <Label value="动作片段" background="#00cc6d" />
        <Label value="对话片段" background="#e62c3f" />
    </Labels>
</View>
<!-- {
    "data": {
        "videoUrl": ""
    }
} -->`;

const dataSource: ParamConfig[] = [
    {
        name: 'name',
        type: 'string',
        defaultValue: '',
        description: '当前组件的名称',
    },
    {
        name: 'value',
        type: 'string',
        defaultValue: '',
        description: '包含视频路径或URL的数据字段，支持使用变量，如 $videoUrl，则会读取数据集中的 videoUrl 字段作为视频真正URL',
    },
    {
        name: '[framerate]',
        type: 'string',
        defaultValue: '24',
        description: '每秒视频的帧率，默认 24 帧',
    },
    {
        name: '[height]',
        type: 'string',
        defaultValue: '600',
        description: '指定视频播放器的高度，如 800px, 800 等，默认 600',
    },
    {
        name: '[timelineHeight]',
        type: 'string',
        defaultValue: '',
        description: '指定视频播放器下方的时间线高度，如 200px, 200 等，默认 100',
    },
];

const features = [
    {text: '视频展示：支持播放、暂停、放大、缩小、逐帧播放等操作'},
    {text: '视频分类标注：结合使用 Choices 组件进行视频分类标注'},
    {text: '视频划选标注：结合使用 Labels 组件进行视频划选标注'},
];

const classificationExampleGuides = [
    {text: '进行视频分类标注，可结合使用 Choices 组件，Video 的 name 需与 Choices 的 toName 保持一致以建立绑定关系。'},
];

const timelineExampleGuides = [
    {text: '进行视频划选标注（逐帧），需结合使用 Labels 组件，Video 的 name 需与 Labels 的 toName 保持一致以建立绑定关系。'},
    {text: '选择一个 Label 标签后开始在视频下方的时间线区域拖动绘制。'},
    {text: '选中绘制的时间线，可以进行拖拽长度等操作。'},
];

export const VideoDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="视频 Video" />
            <p>Video 组件用于展示和标注视频，支持以下功能：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Title level={4}>视频分类标注</Title>
            <UnorderedList items={classificationExampleGuides} />
            <Example code={classificationExampleCode} />
            <Title level={4} className={marginTop(20)}>视频划选标注（逐帧）</Title>
            <UnorderedList items={timelineExampleGuides} />
            <Example code={timelineExampleCode} />
            <Divider />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
        </div>
    );
};
