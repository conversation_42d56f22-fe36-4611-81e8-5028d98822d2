import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';
import {ParamConfig} from '@/label-view/types/editor';

const exampleCode = `<View>
    <Header value="请对文本的情感和词性进行标注" />
    <Text name="word" value="$word" />
    <Choices name="emotion" toName="word">
        <Choice value="positive" label="积极" />
        <Choice value="neutral" label="中性" />
        <Choice value="negative" label="消极" />
    </Choices>
</View>
<!-- {
    "data": {
        "word": "苹果"
    }
} -->
`;

const dataSource: ParamConfig[] = [
    {
        name: 'name',
        type: 'string',
        defaultValue: '',
        description: '导出所需字段名',
    },
    {
        name: 'toName',
        type: 'string',
        defaultValue: '',
        description: '所标注的字段名',
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '类名',
    },
    {
        name: '[sourceName]',
        type: 'string',
        defaultValue: '',
        description: '配置后可修改源数据',
    },
    {
        name: '[defaultValue]',
        type: 'string',
        defaultValue: '',
        description: '未标注时展示的默认值',
    },
];

const choiceDataSource: ParamConfig[] = [
    {
        name: 'value',
        type: 'string',
        defaultValue: '',
        description: '选项值',
    },
    {
        name: '[label]',
        type: 'string',
        defaultValue: '',
        description: '选项文本',
    },
];

const features = [
    {text: '支持自定义选项文本和值'},
    {text: '支持与其他组件关联'},
];

export const ChoicesDocument = () => {
    return (
        <div>
            <PageLayoutHeader title="选项（单选） Choices" />
            <p>单选选项组件，用于在预设的选项中进行单选标注。主要功能包括：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <PageLayoutHeader title="Choices 参数说明" />
            <ParamsTable dataSource={dataSource} />
            <PageLayoutHeader title="Choice 参数说明" />
            <ParamsTable dataSource={choiceDataSource} />
        </div>
    );
};
