import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';
import {ParamConfig} from '@/label-view/types/editor';

const exampleCode = `<View>
    <Header value="文本标注对象" />
    <Text name="sentence" value="$sentence" />
</View>
<!-- {
    "data": {
        "sentence": "这是一个示例句子，用于展示 Text 的功能。"
    }
}
-->`;

const dataSource: ParamConfig[] = [
    {
        name: 'name',
        type: 'string',
        defaultValue: '',
        description: '导出所需字段名',
    },
    {
        name: 'value',
        type: 'string',
        defaultValue: '',
        description: '文本内容',
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '类名',
    },
];
const features = [
    {text: '文本选择标注：通过鼠标选择文本进行标注'},
    {text: '标注高亮展示：已标注的文本会根据标签类型显示对应的背景色'},
    {text: '标注详情查看：点击已标注的文本可查看标注详情'},
    {text: '标注结果管理：支持查看和管理已添加的标注'},
];

const tips = [
    {text: '如果未提供 value，在预览模式下会显示默认文本 &quot;The quick brown fox jumps over the lazy dog.&quot;，编辑模式下显示为空。'},
];

export const TextDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="文本 Text" />
            <p>在文本中选择特定的部分进行标注，并对已标注的部分进行管理。支持以下功能：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <Divider />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
            <PageLayoutHeader title="注意" />
            <UnorderedList items={tips} />
        </div>
    );
};
