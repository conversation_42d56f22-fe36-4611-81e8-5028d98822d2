import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from '@/label-view/document/Example';
import {ParamsTable} from '@/label-view/document/ParamsTable';
import {UnorderedList} from '@/label-view/document/UnorderedList';
import {ParamConfig} from '@/label-view/types/editor';

const exampleCode = `<View>
    <Header value="代码变更标注对象" />
    <Diff
        name="diff-example"
        oldSource="$oldCode"
        newSource="$newCode"
        language="javascript"
        viewType="split"
        enableHighlight="true"
        oldPath="oldFile.js"
        newPath="newFile.js"
    />
</View>
<!-- {
    "data": {
        "oldCode": "function greet() {\\n    console.log('Hello');\\n}",
        "newCode": "function greet(name) {\\n    console.log('Hello, ' + name);\\n}"
    }
} -->`;

const dataSource: ParamConfig[] = [
    {
        name: 'name',
        type: 'string',
        defaultValue: '',
        description: '对象名',
    },
    {
        name: 'oldSource',
        type: 'string',
        defaultValue: '',
        description: '旧源代码',
    },
    {
        name: 'oldPath',
        type: 'string',
        defaultValue: '',
        description: '旧文件路径',
    },
    {
        name: 'newSource',
        type: 'string',
        defaultValue: '',
        description: '新源代码',
    },
    {
        name: 'newPath',
        type: 'string',
        defaultValue: '',
        description: '新文件路径',
    },
    {
        name: '[className]',
        type: 'string',
        defaultValue: '',
        description: '类名',
    },
];
const features = [
    {text: '分屏对比：以并排方式展示新旧代码的差异'},
    {text: '行级对比：精确显示每行代码的变更内容'},
    {text: '变更高亮：使用不同颜色标识添加、删除和修改的代码'},
    {text: '代码折叠：自动折叠未变更的代码块，聚焦展示变更部分'},
    {text: '文件信息：显示新旧文件的路径信息'},
];

const tips = [
    {text: '提供完整的文件路径信息，便于用户理解代码上下文'},
    {text: '合理设置代码块的大小，过大的代码块可能影响加载性能'},
    {text: '使用适当的代码格式化，保持代码的可读性'},
];

export const DiffDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="差异对比 Diff" />
            <p>用于展示和标注两个文本源（oldSource 和 newSource）之间的差异。支持以下功能：</p>
            <UnorderedList items={features} />
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
            <PageLayoutHeader title="参数说明" />
            <ParamsTable dataSource={dataSource} />
            <Divider />
            <PageLayoutHeader title="注意" />
            <p>为了获得最佳的展示效果，建议：</p>
            <UnorderedList items={tips} />
        </div>
    );
};
