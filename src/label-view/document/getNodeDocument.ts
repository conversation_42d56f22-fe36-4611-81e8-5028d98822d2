import {ComponentType} from 'react';
import {CodeDocument} from '../components/Code/Document';
import {CodeEditorDocument} from '../components/CodeEditor/Document';
import {DiffDocument} from '../components/Diff/Document';
import {MarkdownDocument} from '../components/Markdown/Document';
import {EchartsDocument} from '../components/Echarts/Document';
import {ImageDocument} from '../components/Image/Document';
import {TextDocument} from '../components/Text/Document';
import {NumberDocument} from '../components/Number/Document';
import {TextAreaDocument} from '../components/TextArea/Document';
import {ChoicesDocument} from '../components/Choices/Document';
import {JsonDocument} from '../components/Json/Document';
import {TaxonomyDocument} from '../components/Taxonomy/Document';
import {LabelsDocument} from '../components/Labels/Document';
import {RankerDocument} from '../components/Ranker/Document';
import {PdfDocument} from '../components/Pdf/Document';
import {VideoDocument} from '../components/Video/Document';
//
// // Visual
import {CollapseDocument} from '../components/Collapse/Document';
import {FieldDocument} from '../components/Field/Document';
import {HeaderDocument} from '../components/Header/Document';
import {HtmlDocument, StyleDocument} from '../components/Html/Document';
import {IntroductionDocument} from './IntroductionDocument';
import {AttributeDocument} from './AttributeDocument';

// const Noop = (): null => null;

// eslint-disable-next-line complexity
export const getNodeDocument = (name: string): ComponentType => {

    switch (name) {
        case 'Introduction':
            return IntroductionDocument;
        case 'Attribute':
            return AttributeDocument;
        case 'Text':
            return TextDocument;
        case 'Labels':
            return LabelsDocument;
        case 'Taxonomy':
            return TaxonomyDocument;
        case 'Choices':
            return ChoicesDocument;
        case 'TextArea':
            return TextAreaDocument;
        case 'Json':
            return JsonDocument;
        case 'Code':
            return CodeDocument;
        case 'CodeEditor':
            return CodeEditorDocument;
        case 'Diff':
            return DiffDocument;
        case 'Markdown':
            return MarkdownDocument;
        case 'Echarts':
            return EchartsDocument;
        case 'Image':
            return ImageDocument;
        case 'Number':
            return NumberDocument;
        case 'Ranker':
        // case 'Bucket':
            return RankerDocument;
        case 'Pdf':
            return PdfDocument;
        case 'Video':
            return VideoDocument;
        // /* Visual */
        case 'Collapse':
            return CollapseDocument;
        // case 'Panel':
        //     return [Noop];
        case 'Field':
            return FieldDocument;
        case 'Header':
            return HeaderDocument;
        case 'Style':
            return StyleDocument;
        case 'View':
        default:
            return HtmlDocument;
    }
};
