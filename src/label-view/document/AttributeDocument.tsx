import {Text} from '@panda-design/components';
import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {Example} from './Example';

const exampleCode = `<View>
    <Header value="$headerText" />
    <Text name="sentence" value="$sentence" />
    <Text name="complex" value="$item.text" />
</View>
<!-- {
    "data": {
        "headerText": "这是一个示例标题",
        "sentence": "这是一个示例句子，用于展示 Text 的功能。",
        "item": {
            "text": "可以使用 . 访问深层数据"
        }
    }
} -->`;

export const AttributeDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="引用数据字段" />
            <p>你可以使用<Text code>$</Text>引用数据字段。优先显示你的数据中的字段，如果你的数据中没有对应字段，则会显示注释中定义的 fallback 值。</p>
            <p>所有组件的所有参数都支持此功能。</p>
            <Divider />
            <PageLayoutHeader title="示例" />
            <Example code={exampleCode} />
        </div>
    );
};
