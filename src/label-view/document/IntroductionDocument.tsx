import {Divider} from 'antd';
import {PageLayoutHeader} from '@/design/Layouts/PageLayout';

export const IntroductionDocument = () => {

    return (
        <div>
            <PageLayoutHeader title="简介" />
            <p>Comate Stack 标注功能使用 XML 来配置标注模版，包含三类组件：</p>
            <p><strong>标注对象类</strong> 用于展示需要进行标注的各种对象，比如文本、代码、图片、Pdf、视频等</p>
            <p><strong>标注组件类</strong> 用于对上述的对象进行标注，比如标签允许你对文本中的语义划选、单选和多选可以用于分类、文本框可以用于描述对象等
            </p>
            <p><strong>视觉体验类</strong> 用于更好的优化标注过程，比如标题、折叠面板、样式等</p>
            <Divider />
            <p>此文档仍在撰写中...</p>
        </div>
    );
};
