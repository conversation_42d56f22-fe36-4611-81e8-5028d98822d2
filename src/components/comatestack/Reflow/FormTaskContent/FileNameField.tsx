import {Input, Select} from 'antd';
import {Field, useFieldValue} from '@panda-design/path-form';
import {format} from 'date-fns';

function FileNameField() {
    const clientId = useFieldValue('clientId');
    const uuid = useFieldValue('uuid');
    const dataSourceType = useFieldValue('dataSourceType');
    const options = [
        {
            label: 'jsonl',
            value: 'jsonl',
        },
        {
            label: 'xlsx',
            value: 'xlsx',
        },
        {
            label: 'csv',
            value: 'csv',
        },
    ];

    return (
        <Field
            label="文件名"
            name=""
            required
        >
            <div style={{display: 'flex', gap: 8, alignItems: 'center', width: '100%'}}>
                <Field
                    name="clientId"
                    hasGap={false}
                    style={{flex: 1}}
                >
                    <Input
                        disabled
                        value={dataSourceType === 'AFS' ? uuid : clientId}
                        placeholder="项目标识"
                    />
                </Field>
                <span>-</span>
                <Field
                    name="taskFileName"
                    hasGap={false}
                    style={{flex: 1}}
                >
                    <Input />
                </Field>
                <span>-</span>
                <Field
                    name="taskFileTimestamp"
                    hasGap={false}
                    style={{flex: 1}}
                >
                    <Input
                        disabled
                        defaultValue={format(new Date(), 'yyyyMMddHH')}
                    />
                </Field>
                <span>.</span>
                <Field
                    name="fileSuffix"
                    hasGap={false}
                    style={{width: 100}}
                >
                    <Select
                        options={options}
                    />
                </Field>
            </div>
        </Field>
    );
}

export default FileNameField;
