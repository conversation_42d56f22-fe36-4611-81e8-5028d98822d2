import styled from '@emotion/styled';
import {useFieldValue} from '@panda-design/path-form';
import CollapseButton from '@/design/CollapseButton';
import StatusLabel from './StatusLabel';
import DataSourceField from './DataSourceField';
import TaskNameField from './TaskNameField';
import File<PERSON><PERSON><PERSON>ield from './FileNameField';
import StorageIntervalField from './StorageIntervalField';
import StorageDirectoryField from './StorageDirectoryField';
import ArchiveField from './ArchiveField';
import StorageBranchField from './StorageBranchField';
import StorageEncryptField from './StorageEncryptField';
import Desensitization<PERSON>ield from './DesensitizationField';

const Wrapper = styled.div`
    margin-top: 20px;
`;

interface Props {
    toggleHiddenAdvancedParams: (v: boolean) => void;
    disabledValue: boolean;
}

function FormTaskContent({toggleHiddenAdvancedParams, disabledValue}: Props) {
    const dataSourceType = useFieldValue('dataSourceType');
    const clientId = useFieldValue('clientId');
    const taskName = useFieldValue('taskName');
    const taskFileName = useFieldValue('taskFileName');
    const taskTransferTime = useFieldValue('taskTransferTime');
    const taskTargetPath = useFieldValue('taskTargetPath');
    const isCompleted = (dataSourceType === 'AFS' || clientId)
        && taskName
        && taskFileName
        && taskTransferTime
        && taskTargetPath;

    return (
        <Wrapper>
            <StatusLabel
                title="任务信息"
                isCompleted={isCompleted}
            />
            {dataSourceType !== 'AFS' && <DataSourceField disabledValue={disabledValue} />}
            <TaskNameField />
            <FileNameField />
            <CollapseButton title="高级配置" toggleHiddenAdvancedParams={toggleHiddenAdvancedParams}>
                <StorageIntervalField />
                <ArchiveField />
                <StorageDirectoryField />
                <StorageBranchField />
                <StorageEncryptField />
                <DesensitizationField />
            </CollapseButton>
        </Wrapper>
    );
}

export default FormTaskContent;
