import {Input} from 'antd';
import {DeleteOutlined} from '@ant-design/icons';
import {Field, FieldArray, FieldLayout, useFieldValue} from '@panda-design/path-form';
import {IconAdd} from '@baidu/ee-icon';
import {useMemo} from 'react';
import {Button} from '@panda-design/components';
import {head} from 'lodash';

interface Props {
    validatePathList?: () => void;
}

const afsExamples = [
    {
        title: '示例1：日期目录',
        path: 'afs://wudang.afs.baidu.com:9902/user/cpd_data/test1/test2/20250716',
        format: '/test1/test2/${yyyyMMdd}/',
    },
    {
        title: '示例2：日期时间目录',
        path: 'afs://wudang.afs.baidu.com:9902/user/cpd_data/test1/test2/2025071612',
        format: '/test1/test2/${yyyyMMddHH}/',
    },
    {
        title: '示例3：多级日期目录',
        path: 'afs://wudang.afs.baidu.com:9902/user/cpd_data/test1/test2/20250716/12',
        format: '/test1/test2/${yyyyMMdd/HH}/',
    },
    {
        title: '示例4：带时间戳的文件',
        path: 'afs://wudang.afs.baidu.com:9902/user/cpd_data/test1/test2/data_2025071612.jsonl',
        format: '/test1/test2/data_${yyyyMMddHH}.jsonl',
    },
];

const getMinTimeGranularity = (dataSourceType: string, value: string) => {
    if (dataSourceType === 'AFS') {
        if (value.includes('HH')) {
            return 'HOUR';
        } else if (value.includes('dd')) {
            return 'DAY';
        }
    }

    return null;
};

const pathValidator = (
    value: string,
    dataSourceType: string,
    taskTransferTime: any,
    validatePathList?: () => void
) => {
    validatePathList?.();
    if (!value) {
        return '请按照规则填入路径信息，如/test1/test2/${yyyyMMdd/HH}/,详细规则详见文档';
    }

    const timePatternMatch = /\$\{([^}]+)\}/.exec(value);
    if (!timePatternMatch) {
        return '路径必须包含${}格式的时间变量，如/test1/test2/${yyyyMMdd}/';
    }

    const timePattern = timePatternMatch[1];
    if (!/^yyyy([-/]?)MM([-/]?)dd(?:([-/]?)HH)?$/.test(timePattern)) {
        return '时间格式应为${yyyy[分隔符]MM[分隔符]dd[分隔符]HH}（可以不含HH），分隔符可为-或/或省略';
    }

    const minTimeGranularity = getMinTimeGranularity(dataSourceType, value);
    if (dataSourceType === 'AFS' && minTimeGranularity) {
        if (minTimeGranularity === 'DAY' && head(taskTransferTime) === 'HOUR') {
            return '存储间隔的粒度不能小于路径中配置的时间粒度，请在下方 任务信息-高级配置-存储间隔 中选择「天」';
        }
    }

    return null;
};

const DataPathListField = ({validatePathList}: Props) => {
    const dataSourceType = useFieldValue('dataSourceType');
    const taskTransferTime = useFieldValue('taskTransferTime');
    const AFSTooltip = useMemo(
        () => (
            <div style={{height: 300, margin: 0, overflow: 'auto'}}>
                {
                    afsExamples.map((example, index) => (
                        <div key={index}>
                            <h4 style={{margin: '10px 0'}}>示例{index + 1}</h4>
                            <p>若完整路径: {example.path}</p>
                            <p>则配置格式: {example.format}</p>
                        </div>
                    ))
                }
            </div>
        ),
        []
    );

    return (
        <FieldLayout
            label="AFS路径信息"
            required={dataSourceType === 'AFS'}
            tooltip={AFSTooltip}
        >
            <FieldArray
                name="dataPathList"
                atLeastOne
                AddButton={({onAdd}) => (
                    <Button
                        type="text"
                        icon={<IconAdd />}
                        onClick={onAdd}
                    >
                        新增AFS路径
                    </Button>
                )}
                DeleteButton={
                    ({onDelete, disabled, disabledReason}) => (
                        <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={onDelete}
                            disabled={disabled}
                            title={disabled ? disabledReason : undefined}
                        />
                    )
                }
            >
                {index => (
                    <Field
                        name={['dataPathList', index]}
                        validate={value => pathValidator(
                            value, dataSourceType, taskTransferTime, validatePathList
                        )}
                        style={{marginBottom: 8, width: '100%'}}
                    >
                        <Input
                            placeholder="请按照规则填入路径信息，如/test1/test2/${yyyyMMdd/HH}/"
                        />
                    </Field>
                )}
            </FieldArray>
        </FieldLayout>
    );
};

export default DataPathListField;
