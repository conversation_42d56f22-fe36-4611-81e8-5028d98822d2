import {Input} from 'antd';
import {Field, createRequiredFieldValidate, useFieldValue} from '@panda-design/path-form';

const AccessKeyField = () => {
    const accessKeyValidator = createRequiredFieldValidate('请输入AFS账号');
    const dataSourceType = useFieldValue('dataSourceType');

    return (
        <Field
            name="accessKey"
            label="账号"
            required={dataSourceType === 'AFS'}
            validate={accessKeyValidator}
        >
            <Input placeholder="请输入AFS账号" />
        </Field>
    );
};

export default AccessKeyField;
