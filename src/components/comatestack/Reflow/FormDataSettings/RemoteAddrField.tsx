import {Input, Space} from 'antd';
import {Field, createRequiredFieldValidate, useFieldValue, useFormContext} from '@panda-design/path-form';
import {ChangeEvent, useCallback} from 'react';

const RemoteAddrField = () => {
    const remoteAddr = useFieldValue('remoteAddr');
    const {setFieldValue} = useFormContext();
    const dataSourceType = useFieldValue('dataSourceType');
    const remoteAddrValidator = createRequiredFieldValidate('请输入AFS集群信息');

    const handleChange = useCallback(
        (e: ChangeEvent<HTMLInputElement>) => {
            const value = e.target.value;
            if (value) {
                setFieldValue('remoteAddr', value);
            }
        },
        [setFieldValue]
    );

    return (
        <Field
            name="remoteAddr"
            label="AFS集群信息"
            required={dataSourceType === 'AFS'}
            validate={remoteAddrValidator}
        >
            <Space.Compact style={{width: '100%'}}>
                <span style={{padding: '0 8px', display: 'flex', alignItems: 'center'}}>afs://</span>
                <Input
                    value={remoteAddr || ''}
                    placeholder="请填入AFS集群信息"
                    onChange={handleChange}
                    style={{borderRadius: '4px'}}
                />
            </Space.Compact>
        </Field>
    );
};

export default RemoteAddrField;
