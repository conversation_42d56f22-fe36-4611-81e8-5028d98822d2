import {Input} from 'antd';
import {Field, createRequiredFieldValidate, useFieldValue} from '@panda-design/path-form';

const SecretKeyField = () => {
    const secretKeyValidator = createRequiredFieldValidate('请输入AFS账号的密码');
    const dataSourceType = useFieldValue('dataSourceType');

    return (
        <Field
            name="secretKey"
            label="密码"
            required={dataSourceType === 'AFS'}
            validate={secretKeyValidator}
        >
            <Input.Password placeholder="请输入AFS密码" />
        </Field>
    );
};

export default SecretKeyField;
