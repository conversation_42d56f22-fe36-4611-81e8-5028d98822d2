import {Radio, Typography} from 'antd';
import {Field, useFieldValue} from '@panda-design/path-form';
import {DataReflowManualLink} from '@/links/external';

interface Props {
    disabled?: boolean;
}

const DataSourceTypeField = ({disabled}: Props) => {
    const dataSourceType = useFieldValue('dataSourceType');

    return (
        <Field
            name="dataSourceType"
            label="数据接收方式"
            required
            extra={
                dataSourceType === 'AFS' && (
                    <Typography.Text type="secondary">
                        通过AFS接入，需要您直接输入账号和密码，同时按规则告知路径信息。详情请参考：
                        <DataReflowManualLink>接入文档</DataReflowManualLink>
                    </Typography.Text>
                )
            }
            hasGap={dataSourceType !== 'AFS'}
        >
            <Radio.Group disabled={disabled}>
                <Radio value="DEFAULT">SDK/API</Radio>
                <Radio value="AFS">AFS数据接入</Radio>
            </Radio.Group>
        </Field>
    );
};

export default DataSourceTypeField;
