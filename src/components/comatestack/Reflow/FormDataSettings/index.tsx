import styled from '@emotion/styled';
import {useFieldValue, useFormContext} from '@panda-design/path-form';
import {useCallback, useEffect, useState} from 'react';
import StatusLabel from '../FormTaskContent/StatusLabel';
import DataSourceTypeField from './DataSourceTypeField';
import AccessKeyField from './AccessKeyField';
import SecretKeyField from './SecretKeyField';
import RemoteAddrField from './RemoteAddrField';
import DataPathListField from './DataPathListField';

const Wrapper = styled.div`
    margin-top: 20px;
`;

interface Props {
    disabled?: boolean;
}

export const FormDataSettings = ({disabled}: Props) => {
    const dataSourceType = useFieldValue('dataSourceType');
    const accessKey = useFieldValue('accessKey');
    const secretKey = useFieldValue('secretKey');
    const remoteAddr = useFieldValue('remoteAddr');
    const dataPathList = useFieldValue('dataPathList');
    const [isCompleted, setIsCompleted] = useState(false);
    const {setFieldValue} = useFormContext();

    useEffect(
        () => {
            if (dataSourceType === 'AFS' && (!dataPathList || dataPathList.length === 0)) {
                setFieldValue('dataPathList', ['']);
            }
        },
        [dataSourceType, dataPathList, setFieldValue]
    );

    const checkIsCompleted = useCallback(
        () => {
            const newStatus = dataSourceType && (
                dataSourceType === 'DEFAULT' || (
                    dataSourceType === 'AFS'
                    && accessKey
                    && secretKey
                    && remoteAddr
                    && dataPathList
                    && dataPathList.length > 0
                    && dataPathList.join('').length > 0
                )
            );

            setIsCompleted(newStatus);
        },
        [dataSourceType, accessKey, secretKey, remoteAddr, dataPathList]
    );

    return (
        <Wrapper>
            <StatusLabel
                title="数据设置"
                isCompleted={isCompleted}
            />
            <DataSourceTypeField disabled={disabled} />

            {dataSourceType === 'AFS' && (
                <>
                    <AccessKeyField />
                    <SecretKeyField />
                    <RemoteAddrField />
                    <DataPathListField
                        validatePathList={checkIsCompleted}
                    />
                </>
            )}

        </Wrapper>
    );
};

export default FormDataSettings;
