/* eslint-disable max-lines */
import {CopyOutlined} from '@ant-design/icons';
import {css} from '@emotion/css';
import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import {Divider, Space, Typography} from 'antd';
import {Resizable} from 're-resizable';
import {ReactNode, useCallback, useMemo, useState} from 'react';
import {FullScreen, useFullScreenHandle} from 'react-full-screen';
import {Boundary} from 'react-suspense-boundary';
import {IconExitFullScreen, IconFullScreen} from '@/icons/ievalue';
import {CaseEvaluateItem, RecordFeatureItem} from '@/api/ievalue/case';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {TableMDEditButton} from '@/components/ievalue/TableMDPopover/TableMDEditButton';
import {
    useGroupCaseID,
    useGroupID,
    useTaskInfo,
    useTaskStageID,
} from '@/hooks/ievalue/task';
import {copyToClipboard, countTokenSize, countValidCharacters} from '@/utils/ievalue';
import JsonOrMarkdownOrDiff from '@/components/ievalue/JsonOrMarkdownOrDiff';
import {
    useEvaluateDiffBaseContent,
    useEvaluateDiffSetBaseContent,
} from '@/providers/TaskDetailProviders/EvaluateDiffProvider';
import {ImagePreviewButton} from '@/components/ievalue/TableMDPopover/ImagePreviewButton';
import {SearchResults} from '@/components/ievalue/SearchResults';
import {SearchResultsItem} from '@/api/ievalue/prompt';
import {ScratchWordsBaseProvider} from '@/components/ievalue/ScratchWordsMarkdown/ScratchWordsBaseProvider';
import {ReasonContentButton} from '../../Evaluate/ModalCompare/ReasonContentButton';

interface Props {
    isCompact?: boolean;
    fixHeight?: boolean;
    showDiff?: boolean;
}

const Screen = styled(FullScreen)`
    background: #fff;
    width: 100%;
    height: 100%;
    .ant-5-btn.ant-5-btn-sm {
        padding: 4px;
    }
`;

const MarkdownContent = styled.div<Props>`
    width: 100%;
    padding: ${(props: Props) => (props.isCompact ? 0 : '10px 20px')};
    overflow: auto;
    flex: 1 0;
    ::-webkit-scrollbar {
        display: none;
    }
`;

const ResultContent = styled.div<Props>`
    width: 100%;
    max-height: ${(props: Props) => (props.fixHeight ? '300px' : undefined)};
    overflow: auto;
    ::-webkit-scrollbar {
        display: none;
    }
`;

const CharCountContent = styled(FlexLayout)`
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    width: 100%;
    user-select: none;
`;

interface ContentProps extends Props {
    content: any;
    reasoningContent?: string;
    searchResults?: SearchResultsItem[];
    showCharCount?: boolean;
    caseEvaluateItem?: CaseEvaluateItem | RecordFeatureItem;
    enableScratchWords?: boolean;
    predictRecordID?: number;
    full?: boolean;
    showOrigin?: boolean;
    diffKey?: string;
}
interface FullScreenComponentProps extends ContentProps {
    title: ReactNode;
    extra?: ReactNode;
    canCollapse?: boolean;
    editKey?: string;
    showEdit?: boolean;
    recordID?: number;
    onRefresh?: () => void;
    background?: string;
    resizable?: boolean;
    showCopy?: boolean;
    showMD?: boolean;
    showFixHeight?: boolean;
}

const Content = ({
    enableScratchWords,
    content,
    reasoningContent,
    searchResults,
    predictRecordID,
    showCharCount,
    caseEvaluateItem,
    full,
    isCompact,
    showDiff,
    showOrigin,
    diffKey,
}: ContentProps) => {
    const diffBaseContent = useEvaluateDiffBaseContent();
    const diffBaseValue = useMemo(
        () => {
            return showDiff && diffKey !== diffBaseContent.key
                ? diffBaseContent.content
                : null;
        },
        [diffBaseContent.content, diffBaseContent.key, diffKey, showDiff]
    );
    return (
        <FlexLayout
            direction="column"
            style={{
                overflow: full ? 'auto' : 'hidden',
                width: '100%',
                height: '100%',
                maxHeight: full ? 'calc(100vh - 70px)' : undefined,
            }}
            id={`markdownPanel_${full}`}
        >
            <div style={{padding: '10px 0 0 20px'}}>
                <ReasonContentButton reasoningContent={reasoningContent} />
            </div>
            <MarkdownContent
                id={`id_${predictRecordID}_${full}`}
                isCompact={isCompact}
            >
                <ScratchWordsBaseProvider
                    predictRecordID={predictRecordID}
                    containerId={`normalContainerId_${full}`}
                >
                    <JsonOrMarkdownOrDiff
                        content={content}
                        diffBaseContent={diffBaseValue}
                        showOrigin={showOrigin}
                        enableScratchWords={enableScratchWords}
                    />
                </ScratchWordsBaseProvider>
                <SearchResults data={searchResults} />
            </MarkdownContent>

            {showCharCount && (
                <CharCountContent justify="end" gap={4}>
                    {caseEvaluateItem && (
                        <>
                            <span style={{marginRight: 8}}>
                                请求token数：
                                {caseEvaluateItem?.promptTokens
                                    || countTokenSize(caseEvaluateItem?.input)}
                            </span>
                            <span style={{marginRight: 8}}>
                                答案token数：
                                {caseEvaluateItem?.completionTokens
                                    || countTokenSize(caseEvaluateItem?.output)}
                            </span>
                            <span style={{marginRight: 8}}>
                                推理耗时：{caseEvaluateItem?.timeUsed || '-'}秒
                            </span>
                        </>
                    )}
                    <span style={{marginRight: 8}}>
                        字数：{countValidCharacters(content)}
                    </span>
                </CharCountContent>
            )}
        </FlexLayout>
    );
};

// eslint-disable-next-line complexity
export function FullScreenComponent({
    content,
    reasoningContent,
    searchResults,
    title,
    canCollapse = false,
    editKey,
    onRefresh,
    recordID,
    background,
    showCharCount,
    caseEvaluateItem,
    resizable = true,
    enableScratchWords,
    predictRecordID,
    isCompact = false,
    showDiff = false,
    showEdit = true,
    showMD = true,
    showCopy = true,
    showFixHeight = true,
}: FullScreenComponentProps) {
    const [full, setFull] = useState(false);
    const [collapse, setCollapse] = useState(false);
    const [fixHeight, setFixHeight] = useState(false);
    const [showOrigin, setShowOrigin] = useState(false);
    const [taskInfo] = useTaskInfo();
    const caseID = useGroupCaseID();
    const groupID = useGroupID();
    const stageID = useTaskStageID();
    const handle = useFullScreenHandle();
    const diffBaseContent = useEvaluateDiffBaseContent();
    const setDiffBaseContent = useEvaluateDiffSetBaseContent();
    const diffKey = useMemo(
        () => {
            return editKey ?? `${predictRecordID ?? ''}`;
        },
        [editKey, predictRecordID]
    );
    const handleFullScreen = useCallback(
        () => {
            if (full) {
                setFull(false);
                handle.exit();
            } else {
                setFull(true);
                handle.enter();
            }
        },
        [full, setFull, handle]
    );

    const handleCollapse = useCallback(
        () => {
            setCollapse(collapse => !collapse);
        },
        []
    );
    const handleFixHeight = useCallback(
        () => {
            setFixHeight(fixHeight => !fixHeight);
        },
        []
    );

    const handleSetBaseContent = useCallback(
        () => {
            setDiffBaseContent({content, key: diffKey});
        },
        [setDiffBaseContent, content, diffKey]
    );

    const handleShowOrigin = useCallback(
        () => {
            setShowOrigin(pre => !pre);
        },
        []
    );

    return (
        <Boundary>
            <Screen handle={handle} onChange={setFull}>
                <FlexLayout
                    direction="column"
                    style={{
                        width: '100%',
                        height: '100%',
                        background,
                        borderRadius: '6px',
                    }}
                >
                    <FlexLayout
                        justify="space-between"
                        style={{
                            width: '100%',
                            flex: '0 0 30px',
                            padding: isCompact ? '5px 10px' : '8px 10px',
                        }}
                        align="center"
                    >
                        {title}
                        <Space size={isCompact ? 0 : 2}>
                            {canCollapse && !full && (
                                <Button
                                    size="small"
                                    type="text"
                                    style={{display: 'flex', alignItems: 'center'}}
                                    onClick={handleCollapse}
                                >
                                    {collapse ? '展开' : '收缩'}
                                </Button>
                            )}
                            {!!caseEvaluateItem?.outputMultiData?.images?.length && (
                                <ImagePreviewButton
                                    type="text"
                                    size="small"
                                    images={caseEvaluateItem?.outputMultiData?.images}
                                />
                            )}
                            {showFixHeight && !full && (
                                <Button
                                    size="small"
                                    type="text"
                                    style={{display: 'flex', alignItems: 'center'}}
                                    onClick={handleFixHeight}
                                >
                                    {fixHeight ? '自适应高' : '定高'}
                                </Button>
                            )}
                            {showDiff && !full && (
                                <Button
                                    size="small"
                                    type="text"
                                    style={{display: 'flex', alignItems: 'center'}}
                                    onClick={handleSetBaseContent}
                                >
                                    {diffBaseContent.content === content
                                && diffKey === diffBaseContent.key
                                        ? '取消基线'
                                        : '设为基线'}
                                </Button>
                            )}
                            {showMD && (
                                <Button
                                    size="small"
                                    style={{display: 'flex', alignItems: 'center'}}
                                    type="text"
                                    onClick={handleShowOrigin}
                                >
                                    {showOrigin ? 'Markdown' : '原文'}
                                </Button>
                            )}
                            {(editKey || recordID) && showEdit && (
                                <TableMDEditButton
                                    onRefresh={onRefresh}
                                    style={{display: 'flex', alignItems: 'center'}}
                                    editKey={editKey}
                                    titleName="编辑内容"
                                    caseID={caseID}
                                    content={content}
                                    groupID={groupID}
                                    stageID={stageID}
                                    recordID={recordID}
                                    datasetID={taskInfo.datasetID}
                                    size="small"
                                    type="text"
                                />
                            )}
                            <Button
                                size="small"
                                icon={full ? (
                                    <IconExitFullScreen />
                                ) : (
                                    <IconFullScreen />
                                )}
                                onClick={handleFullScreen}
                                type="text"
                            />
                            {showCopy && <Typography.Text
                                strong
                                copyable={{
                                    text: content,
                                    onCopy: () => {
                                        copyToClipboard(content);
                                    },
                                    icon: (
                                        <CopyOutlined
                                            style={{
                                                color: '#000',
                                            }}
                                        />
                                    ),
                                }}
                            />}
                        </Space>
                    </FlexLayout>
                    <Divider style={{margin: 0, color: '#E9E9E9'}} />
                    {resizable ? (
                        <Resizable
                            className={css`
                                width: 100% !important;
                            `}
                            style={{
                                display: collapse && !full ? 'none' : 'block',
                                flex: '1 0 auto',
                            }}
                            enable={{bottom: true}}
                            size={
                                fixHeight
                                    ? {height: '300px', width: '100%'}
                                    : undefined
                            }
                        >
                            <Content
                                enableScratchWords={enableScratchWords}
                                content={content}
                                reasoningContent={reasoningContent}
                                searchResults={searchResults}
                                predictRecordID={predictRecordID}
                                showCharCount={showCharCount}
                                caseEvaluateItem={caseEvaluateItem}
                                full={full}
                                isCompact={isCompact}
                                showDiff={showDiff}
                                showOrigin={showOrigin}
                                diffKey={diffKey}
                            />
                        </Resizable>
                    ) : (
                        <ResultContent fixHeight={fixHeight} id="resultContent">
                            <Content
                                enableScratchWords={enableScratchWords}
                                content={content}
                                reasoningContent={reasoningContent}
                                searchResults={searchResults}
                                predictRecordID={predictRecordID}
                                showCharCount={showCharCount}
                                caseEvaluateItem={caseEvaluateItem}
                                full={full}
                                isCompact={isCompact}
                                showDiff={showDiff}
                                showOrigin={showOrigin}
                                diffKey={diffKey}
                            />
                        </ResultContent>
                    )}
                </FlexLayout>
            </Screen>
        </Boundary>
    );
}
