import {useCallback} from 'react';
import copy from 'copy-to-clipboard';
import {CellPosition} from '@/third-party/devops-federation/types';

export const useCopy = (gridRef: any) => {
    const handleCopyValue = useCallback(
        () => {
            const focusedCell = gridRef.current?.api?.getFocusedCell() as CellPosition;
            if (!focusedCell) {
                return;
            }

            const {rowIndex, column} = focusedCell;
            const rowNode = gridRef.current?.api?.getRowNode(rowIndex);

            const cellValue = gridRef.current?.api?.getCellValue({
                rowNode,
                colKey: column,
                useFormatter: true,
            });

            copy(cellValue ?? '');
        },
        [gridRef]
    );

    return handleCopyValue;
};
