import {useMemo} from 'react';
import CustomHeader from '../CustomHeader';
import CustomTooltip from '../CustomTooltip';

interface Props {
    handleOpenColumnContextMenu: (params: any) => void;
}

export const useDefaultColDefs = ({
    handleOpenColumnContextMenu,
}: Props) => {
    const defaultColDef = useMemo(
        () => ({
            minWidth: 100,
            sortable: false,
            headerComponentParams: {
                innerHeaderComponent: CustomHeader,
                onColumnContextMenu: handleOpenColumnContextMenu,
            },
            tooltipComponent: CustomTooltip,
        }),
        [handleOpenColumnContextMenu]
    );

    return defaultColDef;
};

