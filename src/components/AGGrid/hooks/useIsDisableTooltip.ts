import {useMemo} from 'react';
import {RenderProperty} from '@/types/agGrid/agGrid';

// 编辑态 或 行高是自适应内容 的时候，禁用 Tooltip
export const useIsDisableTooltip = (editable: boolean, renderProperties: RenderProperty[]) => {
    const isDisableTooltip = useMemo(
        () => {
            if (editable) {
                return true;
            }

            const items = renderProperties.filter(
                item => item.opObj === 'TABLE' && item.name === 'height' && item.value !== ''
            );

            if (items.length && Number(items[0].value) === -1) {
                return true;
            }

            return false;
        },
        [editable, renderProperties]
    );

    return isDisableTooltip;
};
