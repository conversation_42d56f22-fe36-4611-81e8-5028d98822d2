import {useCallback} from 'react';
import {ColDef, Column, GridApi} from '@/third-party/devops-federation/types';

export const useHeaderNames = (api: GridApi<any>, column: Column) => {
    const handleChangeEditState = useCallback(
        (isEditing: boolean) => {
            const columnDefs = (api?.getColumnDefs() ?? []) as ColDef[];
            const colId = column.getColId();
            for (const columnDef of columnDefs) {
                const {headerComponentParams} = columnDef;
                if (columnDef.colId === colId) {
                    columnDef.headerComponentParams = {
                        ...headerComponentParams,
                        isEditing,
                    };
                }
                else {
                    columnDef.headerComponentParams = {
                        ...headerComponentParams,
                        isEditing: false,
                    };
                }
            }

            api?.setGridOption('columnDefs', columnDefs);
        },
        [api, column]
    );

    const handleExitEditOtherHeaderNames = useCallback(
        () => {
            const columnDefs = (api?.getColumnDefs() ?? []) as ColDef[];
            const colId = column.getColId();
            for (const columnDef of columnDefs) {
                const {headerComponentParams} = columnDef;
                if (columnDef.colId !== colId) {
                    columnDef.headerComponentParams = {
                        ...headerComponentParams,
                        isEditing: false,
                    };
                }
            }

            api?.setGridOption('columnDefs', columnDefs);
        },
        [api, column]
    );

    const handleCheckHeaderName = useCallback(
        (originName: string, newName: string) => {
            const columnDefs = (api?.getColumnDefs() ?? []) as ColDef[];
            // 禁止出现重名的列名
            for (const columnDef of columnDefs) {
                if (columnDef.field !== originName && columnDef.field === newName) {
                    return true;
                }
            }

            return false;
        },
        [api]
    );

    const handleSaveHeaderName = useCallback(
        (oldName: string, newName: string) => {
            const columnDefs = (api?.getColumnDefs() ?? []) as ColDef[];
            const colId = column.getColId();
            const columnDef = columnDefs.find(columnDef => columnDef.colId === colId);
            if (columnDef) {
                columnDef.headerName = newName;
                columnDef.field = newName;
                columnDef.colId = newName;

                api?.setGridOption('columnDefs', columnDefs);
                const rowDataToUpdate: any[] = [];
                api?.forEachNode(rowNode => {
                    const data = rowNode.data;
                    data[newName] = data[oldName];
                    delete data[oldName];
                    rowDataToUpdate.push(rowNode.data);
                });

                api?.applyTransaction({
                    update: rowDataToUpdate,
                });
            }
        },
        [api, column]
    );

    return {
        handleChangeEditState,
        handleExitEditOtherHeaderNames,
        handleCheckHeaderName,
        handleSaveHeaderName,
    };
};
