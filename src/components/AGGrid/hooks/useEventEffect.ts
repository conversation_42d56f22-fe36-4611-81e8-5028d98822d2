import {useEffect} from 'react';

export const useEventEffect = () => {
    // 注册事件
    useEffect(
        () => {
            // 阻止浏览器自带的右键菜单
            const handleContextMenu = (e: MouseEvent) => {
                e.preventDefault();
            };

            document.addEventListener('contextmenu', handleContextMenu);

            return () => {
                document.removeEventListener('contextmenu', handleContextMenu);
            };
        },
        []
    );
};
