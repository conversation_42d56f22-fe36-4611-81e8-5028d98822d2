/* eslint-disable max-lines */
import {useCallback, useMemo, useState} from 'react';
import {Dropdown, InputNumber} from 'antd';
import type {InputNumberProps, MenuProps} from 'antd';
import {css} from '@emotion/css';
import styled from '@emotion/styled';
import {ContextMenuProps} from '@/types/agGrid/agGrid';
import {ColDef} from '@/third-party/devops-federation/types';
import {getCurrentColId} from '../utils/getCurrentCol';
import {getCurrentRowId, getCurrentRowIndex} from '../utils/getCurrentRow';

const Container = styled.div<{top: number, left: number, isOpen: boolean}>`
    position: fixed  !important;
    transform: translateX(-50%) !important;
    z-index: 1000 !important;
    top: ${({top}) => `${top}px`};
    left: ${({left}) => `${left}px`};
    display: ${({isOpen}) => (isOpen ? 'block' : 'none')}  !important;

    .ant-5-popover-inner {
        padding: 2px !important;
    }
`;

const numberInputCss = css`
    margin: 0 6px !important;
    width: 40px !important;
`;

const NumberInput = (props: InputNumberProps) => {
    return (
        <InputNumber
            className={numberInputCss}
            size="small"
            controls={false}
            min={1}
            max={100}
            onClick={e => e.stopPropagation()}
            {...props}
        />
    );
};

const ContextMenu = ({
    gridRef,
    gridId,
    editable,
    isOpen,
    position: {top, left},
    currentColumn,
    currentRowNode,
    type,
    rowIdName,
    onRowAdded,
    onColAdded,
    onRowRemoved,
    onColRemoved,
    closeContextMenu,
    handleChangeFormatters,
    onSaveHeaderName,
}: ContextMenuProps) => {
    const [addRowTopCount, setAddRowTopCount] = useState<number>(1);
    const [addRowBottomCount, setAddRowBottomCount] = useState<number>(1);
    const [addColLeftCount, setAddColLeftCount] = useState<number>(1);
    const [addColRightCount, setAddColRightCount] = useState<number>(1);
    const currentRowIndex = getCurrentRowIndex(currentRowNode);
    const currentRowId = getCurrentRowId(currentRowNode, rowIdName);
    const currentColId = getCurrentColId(currentColumn);

    const menuItems: MenuProps['items'] = useMemo(
        () => {
            return [
                {
                    key: 'addRowTop',
                    label: (
                        <span>
                            向上插入
                            <NumberInput
                                value={addRowTopCount}
                                onChange={value => value && setAddRowTopCount(Math.round(Number(value)))}
                                disabled={type === 'Column'}
                            />
                            行
                        </span>
                    ),
                    disabled: type === 'Column',
                },
                {
                    key: 'addRowBottom',
                    label: (
                        <span>
                            向下插入
                            <NumberInput
                                value={addRowBottomCount}
                                onChange={value => value && setAddRowBottomCount(Math.round(Number(value)))}
                                disabled={type === 'Column'}
                            />
                            行
                        </span>
                    ),
                    disabled: type === 'Column',
                },
                {
                    key: 'removeRow',
                    label: <span>删除行</span>,
                    disabled: type === 'Column',
                },
                {
                    type: 'divider',
                },
                {
                    key: 'addColLeft',
                    label: (
                        <span>
                            向左插入
                            <NumberInput
                                value={addColLeftCount}
                                onChange={value => value && setAddColLeftCount(Math.round(Number(value)))}
                            />
                            列
                        </span>
                    ),
                },
                {
                    key: 'addColRight',
                    label: (
                        <span>
                            向右插入
                            <NumberInput
                                value={addColRightCount}
                                onChange={value => value && setAddColRightCount(Math.round(Number(value)))}
                            />
                            列
                        </span>
                    ),
                },
                {
                    key: 'removeCol',
                    label: <span>删除列</span>,
                },
            ];
        },
        [type, addColLeftCount, addColRightCount, addRowBottomCount, addRowTopCount]
    );

    const createNewColumnDefs = useCallback(
        (count: number) => {
            const newColumnDefs = (Array.from({length: count})).map(() => {
                return {
                    editable,
                    wrapText: true,
                    cellEditor: 'agLargeTextCellEditor',
                    cellEditorPopup: true,
                    cellEditorParams: {
                        maxLength: 1e20,
                        cols: 80,
                    },
                    headerClass: ['ag-custom-header'],
                    headerComponentParams: {
                        gridId,
                        columnType: 'text', // 所有新建的列，先默认为文本
                        isEditing: false,
                        onSaveHeaderName,
                    },
                };
            });

            return newColumnDefs;
        },
        [editable, gridId, onSaveHeaderName]
    );

    const handleClickMenu: MenuProps['onClick'] = useCallback(
        ({key}) => {
            switch (key) {
                case 'addRowTop':
                    onRowAdded?.(currentRowId, currentRowIndex, 'up', addRowTopCount);
                    closeContextMenu?.();
                    break;
                case 'addRowBottom':
                    if (!currentRowNode) {
                        return;
                    }

                    onRowAdded?.(currentRowId, currentRowIndex, 'down', addRowBottomCount);
                    closeContextMenu?.();
                    break;
                case 'removeRow':
                    gridRef.current!.api?.applyTransaction({
                        remove: [currentRowNode?.data],
                    });

                    onRowRemoved?.(currentRowId);
                    closeContextMenu?.();
                    break;
                case 'addColLeft':
                {
                    const newColumnDefs = createNewColumnDefs(addColLeftCount);
                    onColAdded?.(currentColId, 'left', newColumnDefs, handleChangeFormatters);
                    closeContextMenu?.();
                    break;
                }
                case 'addColRight':
                {
                    const newColumnDefs = createNewColumnDefs(addColRightCount);
                    onColAdded?.(currentColId, 'right', newColumnDefs, handleChangeFormatters);
                    closeContextMenu?.();
                    break;
                }
                case 'removeCol':
                {
                    const columnDefs = (gridRef.current?.api?.getColumnDefs() ?? []) as ColDef[];
                    const findIndex = columnDefs.findIndex(col => col.colId === currentColId);
                    columnDefs.splice(findIndex, 1);
                    gridRef.current?.api?.setGridOption('columnDefs', columnDefs);
                    onColRemoved?.(currentColId);
                    closeContextMenu?.();
                    break;
                }
            }
        },
        [
            currentRowNode,
            currentRowId,
            currentRowIndex,
            addRowTopCount,
            addRowBottomCount,
            gridRef,
            addColLeftCount,
            currentColId,
            addColRightCount,
            onRowAdded,
            onColAdded,
            onRowRemoved,
            onColRemoved,
            closeContextMenu,
            handleChangeFormatters,
            createNewColumnDefs,
        ]
    );

    return (
        <Container
            id={`${gridId}-context-menu`}
            top={top}
            left={left}
            isOpen={isOpen}
        >
            <Dropdown
                overlayStyle={{width: '200px'}}
                open
                menu={{
                    items: menuItems,
                    onClick: handleClickMenu,
                }}
                getPopupContainer={() => document.getElementById(`${gridId}-context-menu`)}
            />
        </Container>
    );
};

export default ContextMenu;
