/* eslint-disable max-lines */
import {useCallback, useEffect, useMemo, useState} from 'react';
import {Input, Tooltip, Popover} from 'antd';
import {Text} from '@panda-design/components';
import styled from '@emotion/styled';
import {css} from '@emotion/css';
import {CustomInnerHeaderProps} from '@/third-party/devops-federation/types';
import {useHeaderNames} from '../hooks/useHeaderNames';
import {columnTypes} from '../constants/agGrid';
import FormatterSelector from './FormatterSelector';

const HeaderContainer = styled.div`
    display: flex;
    align-items: center;
    width: 100%;
    height: 30px;
`;

const StyledPopover = styled(Popover)`
    display: flex;
    align-items: center;
    width: 100%;
`;

const popoverCss = css`
    width: 260px;

    .ant-5-popover-content {
        margin-left: -16px;
        margin-top: -2px;
    }
`;

export interface MyCustomInnerHeaderProps extends CustomInnerHeaderProps {
    gridId: string;
    columnType: string;
    formatterCodes: string[];
    isEditing: boolean;
    onChangeFormatters: (formatterCodes: string[]) => void;
    onSaveHeaderName: (oldName: string, newName: string) => void;
    onColumnContextMenu: () => void;
}

const CustomHeader = ({
    displayName,
    gridId,
    columnType,
    formatterCodes = [],
    isEditing,
    api,
    column,
    onChangeFormatters,
    onSaveHeaderName,
    onColumnContextMenu,
}: MyCustomInnerHeaderProps) => {
    const findColumnType = columnTypes.find(item => item.value === columnType);
    const [newHeaderName, setNewHeaderName] = useState(displayName);
    // 通过列的编辑态拿到表格的编辑态
    const gridEditable = column?.getColDef()?.editable;
    const {
        handleChangeEditState,
        handleExitEditOtherHeaderNames,
        handleCheckHeaderName,
        handleSaveHeaderName,
    } = useHeaderNames(api, column);

    const isInvalid = handleCheckHeaderName(displayName, newHeaderName);

    const handleClickHeader = useCallback(
        () => {
            if (!gridEditable) {
                return;
            }

            handleExitEditOtherHeaderNames();
            if (isEditing) {
                handleChangeEditState(false);
            }
        },
        [gridEditable, handleChangeEditState, handleExitEditOtherHeaderNames, isEditing]
    );

    const handleDoubelClickHeader = useCallback(
        () => {
            if (!gridEditable) {
                return;
            }

            handleChangeEditState(true);
            setNewHeaderName(displayName);
        },
        [displayName, gridEditable, handleChangeEditState]
    );

    // 修改列名
    useEffect(
        () => {
            if (!gridEditable) {
                setNewHeaderName(displayName);
                return;
            }

            if (isEditing || isInvalid || displayName === newHeaderName || !newHeaderName) {
                return;
            }

            handleSaveHeaderName(displayName, newHeaderName);
            onSaveHeaderName && onSaveHeaderName(displayName, newHeaderName);
        },
        [gridEditable, displayName, isEditing, newHeaderName, isInvalid, handleSaveHeaderName, onSaveHeaderName]
    );

    const popoverContent = useMemo(
        () => {
            return (
                <>
                    <Text type="secondary">列名</Text>
                    <Input
                        autoFocus
                        value={newHeaderName}
                        onChange={(e: any) => setNewHeaderName(e.target.value)}
                        onPressEnter={() => handleChangeEditState(false)}
                        onClick={(e: any) => e.stopPropagation()}
                        status={isInvalid && 'error'}
                    />
                    {
                        isInvalid && (
                            <Text type="error" style={{fontSize: '12px'}}>列名重复</Text>
                        )
                    }
                </>
            );
        },
        [newHeaderName, isInvalid, handleChangeEditState]
    );

    return (
        <HeaderContainer
            onContextMenu={onColumnContextMenu}
            onClick={handleClickHeader}
            onDoubleClick={handleDoubelClickHeader}
        >
            <StyledPopover
                rootClassName={popoverCss}
                open={isEditing}
                content={popoverContent}
                arrow={false}
                placement="bottomLeft"
                getPopupContainer={() => document.getElementById(gridId)}
            >
                <>
                    <Tooltip title={findColumnType?.value}>
                        <span style={{height: '28px'}}>{findColumnType?.icon}</span>
                    </Tooltip>
                    {displayName}
                    <FormatterSelector
                        gridId={gridId}
                        formatterCodes={formatterCodes}
                        onChangeFormatters={onChangeFormatters}
                    />
                </>
            </StyledPopover>
        </HeaderContainer>
    );
};

export default CustomHeader;
