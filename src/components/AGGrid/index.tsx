/* eslint-disable complexity */
/* eslint-disable max-lines */
import {useCallback, useEffect, useRef, useState} from 'react';
import {useShortKey} from 'use-short-key';
import {AgGridReact, themeQuartz} from 'devopsFederationSource/AgGridReact';
import {
    ColDef,
    Column,
    BodyScrollEvent,
    CellContextMenuEvent,
    ColumnHeaderContextMenuEvent,
    RowNode,
} from '@/third-party/devops-federation/types';
import {MAX_ROW_COUNT} from '@/constants/comatestack/agGrid';
import {AGGridProps, ContextMenuType, RenderProperty} from '@/types/agGrid/agGrid';
import {
    applyColumnDefFormatters,
    applyColumnDefsRenderProperties,
    applyRowHeight,
    exitEditAllHeaderNames,
} from '@/utils/agGrid/agGrid';
import {AgGridContainer} from './StyledElement';
import ContextMenu from './ContextMenu';
import {useIsDisableTooltip} from './hooks/useIsDisableTooltip';
import {useHasMoreData} from './hooks/useHasMoreData';
import {useEventEffect} from './hooks/useEventEffect';
import {useCustomComponents} from './hooks/useCustomComponents';
import {useCopy} from './hooks/useCopy';
import {useDefaultColDefs} from './hooks/useDefaultColDefs';

const editModeTheme = themeQuartz?.withParams({
    rowBorder: {style: 'dotted', width: 1, color: '#9696C8'},
    columnBorder: {style: 'dotted', width: 1, color: '#9696C8'},
    backgroundColor: '#E5F2FF',
});

const AGGrid = ({
    gridRef,
    gridId,
    loading = false,
    rowData = [],
    columns = [],
    editable = false,
    pagination = {} as AGGridProps['pagination'],
    renderProperties = [],
    rowIdName,
    onUpdateRenderProperties,
    onCellValueChanged,
    onColumnMoved,
    onRowAdded,
    onColAdded,
    onRowRemoved,
    onColRemoved,
    onSaveHeaderName,
}: AGGridProps) => {
    const [columnDefs, setColumnDefs] = useState<ColDef[]>([]);
    const loadedPages = useRef<number[]>([]);
    const loadedOffsets = useRef<number[]>([]);
    const [totalRowCount, setTotalRowCount] = useState(0);
    const isLoading = useRef<boolean>(false);

    const {
        pageNumber = 1,
        pageSize = 10,
        totalRows = 0,
        pageChangeMode = 'page',
        offset = 0,
        limit = 10,
        onPageChange,
        onOffsetChange,
    } = pagination;

    const [contextMenuPosition, setContextMenuPosition] = useState({top: 0, left: 0});
    const [contextMenuType, setContextMenuType] = useState<ContextMenuType>(null);
    const [isOpenContextMenu, setIsOpenContextMenu] = useState(false);
    const [currentColumn, setCurrentColumn] = useState<Column | null>(null);
    const [currentRowNode, setCurrentRowNode] = useState<RowNode | null>(null);
    const isDisableTooltip = useIsDisableTooltip(editable, renderProperties);
    const hasMoreData = useHasMoreData(
        pageChangeMode === 'page' ? loadedPages.current.length * pageSize : offset + limit,
        totalRowCount
    );
    const components = useCustomComponents();
    const handleCopyValue = useCopy(gridRef);

    // 注册事件
    useEventEffect();

    const handleOpenColumnContextMenu = useCallback(
        ({clientX, clientY}: any) => {
            setContextMenuPosition({left: clientX, top: clientY});
        },
        []
    );

    const defaultColDef = useDefaultColDefs({handleOpenColumnContextMenu});

    const handleChangeFormatters = useCallback(
        (colId: string, formatterCodes: string[]) => {
            const columnDefs = (gridRef.current?.api?.getColumnDefs() ?? []) as ColDef[];
            const columnDef = columnDefs.find(column => column.colId === colId);
            if (columnDef) {
                applyColumnDefFormatters(formatterCodes, columnDef);
                gridRef.current?.api?.setGridOption('columnDefs', columnDefs);

                // 保存格式化渲染
                const renderProperties: RenderProperty[] = [
                    {
                        opObj: 'COLUMN',
                        rowId: '',
                        colId,
                        name: 'formatter',
                        value: formatterCodes.join(','),
                    },
                ];

                onUpdateRenderProperties?.(renderProperties);
            }
        },
        [gridRef, onUpdateRenderProperties]
    );

    // 这里只用于初始化 ColumnDefs。
    // 所有其他涉及 ColumnDefs 的修改，都必须通过 「api.getColumnDefs()」 + 「api.setGridOption()」 的方式。
    // 这里的修改也要同步到「插入新列」的地方
    useEffect(
        () => {
            if (columns.length === 0 || columnDefs.length > 0) {
                return;
            }

            const nextColumnDefs = columns.map((item, index) => {
                const {columnType, field, ...props} = item;
                const newColumn: ColDef = {
                    ...props,
                    colId: field,
                    field,
                    editable,
                    tooltipField: field,
                    width: field.length + 200,
                    wrapText: true,
                    cellEditor: 'agLargeTextCellEditor',
                    cellEditorPopup: true,
                    cellEditorParams: {
                        maxLength: 1e20,
                        cols: 80,
                    },
                    headerClass: ['ag-custom-header'],
                    headerComponentParams: {
                        gridId,
                        columnType,
                        isEditing: false,
                        onChangeFormatters: (formatterCodes: string[]) =>
                            handleChangeFormatters(field, formatterCodes),
                        onSaveHeaderName,
                    },
                };

                if (index === columns.length - 1) {
                    newColumn.minWidth = 200;
                    newColumn.flex = 1;
                }

                return newColumn;
            });

            // 格式化渲染
            if (renderProperties.length) {
                applyColumnDefsRenderProperties(renderProperties, nextColumnDefs);
                setColumnDefs(nextColumnDefs);
                return;
            }

            setColumnDefs(nextColumnDefs);
        },
        [columnDefs.length, columns, gridId, renderProperties, editable, handleChangeFormatters, onSaveHeaderName]
    );

    useEffect(
        () => {
            totalRows && setTotalRowCount(totalRows);
        },
        [totalRows]
    );
    // 格式化渲染
    useEffect(
        () => {
            if (renderProperties.length && columnDefs.length) {
                applyRowHeight(renderProperties, gridRef);
            }
        },
        [renderProperties, gridRef.currentef, gridRef, columnDefs.length]
    );

    const handleClearEditingMode = useCallback(
        () => {
            setIsOpenContextMenu(false);
            exitEditAllHeaderNames(gridRef);
        },
        [gridRef]
    );

    // 编辑态
    useEffect(
        () => {
            const columnDefs = gridRef.current?.api?.getColumnDefs() ?? [];
            for (const columnDef of columnDefs) {
                columnDef.editable = editable;
            }

            gridRef.current?.api?.setGridOption('columnDefs', columnDefs);

            // 退出编辑态
            if (!editable) {
                handleClearEditingMode();
            }
        },
        [columnDefs, editable, gridRef, handleClearEditingMode]
    );

    const handleAutoLoadData = useCallback(
        () => {
            // 如果首屏没有填充满，则自动请求新一页数据
            const gridElement = document.querySelector(`[grid-id="${gridId}"] .ag-body-viewport`);
            const rowContainerElement = document.querySelector(`[grid-id="${gridId}"] .ag-full-width-container`);
            if (!gridElement || !rowContainerElement || totalRows > MAX_ROW_COUNT) {
                return;
            }

            if (rowContainerElement.clientHeight < gridElement.clientHeight) {
                isLoading.current = true;

                if (pageChangeMode === 'page') {
                    onPageChange?.(pageNumber + 1, pageSize);
                    return;
                }

                if (pageChangeMode === 'offset') {
                    onOffsetChange(gridRef.current?.api?.getDisplayedRowCount() ?? 0, limit);
                }
            }
        },
        [gridId, gridRef, limit, pageChangeMode, pageNumber, pageSize, totalRows, onOffsetChange, onPageChange]
    );

    // 监听新数据并自动追加
    useEffect(
        () => {
            if (!gridRef.current?.api || rowData.length === 0) {
                return;
            }

            if (pageChangeMode === 'page' && (!onPageChange || loadedPages.current.includes(pageNumber))) {
                return;
            }

            const displayedCount = gridRef.current?.api?.getDisplayedRowCount() ?? 0;
            if (pageChangeMode === 'offset' && (!onOffsetChange || loadedOffsets.current.includes(offset))) {
                return;
            }

            gridRef.current?.api?.applyTransaction({
                addIndex: displayedCount,
                add: rowData,
            });

            if (pageChangeMode === 'page') {
                loadedPages.current = [...loadedPages.current, pageNumber];
            }

            if (pageChangeMode === 'offset') {
                loadedOffsets.current = [...loadedOffsets.current, offset];
            }

            isLoading.current = false;

            handleAutoLoadData();
        },
        [
            pageChangeMode,
            gridId,
            gridRef,
            limit,
            pageNumber,
            pageSize,
            rowData,
            totalRows,
            offset,
            onPageChange,
            onOffsetChange,
            handleAutoLoadData,
        ]
    );

    // 滚动翻页
    const handleScroll = useCallback(
        ({direction, top}: BodyScrollEvent) => {
            if (!onPageChange && !onOffsetChange) {
                return;
            }

            const gridElement = document.querySelector(`[grid-id="${gridId}"] .ag-body-viewport`);
            if (!hasMoreData || isLoading.current || direction === 'horizontal' || !gridElement) {
                return;
            }

            const {scrollHeight, clientHeight} = gridElement;
            if (scrollHeight - top - clientHeight < 50) {
                isLoading.current = true;
                if (pageChangeMode === 'page') {
                    onPageChange?.(pageNumber + 1, pageSize);
                    return;
                }

                if (pageChangeMode === 'offset') {
                    onOffsetChange(gridRef.current?.api?.getDisplayedRowCount() ?? 0, limit);
                }
            }
        },
        [gridId, gridRef, hasMoreData, limit, onOffsetChange, onPageChange, pageChangeMode, pageNumber, pageSize]
    );

    const handleColumnContextMenu = useCallback(
        ({column}: ColumnHeaderContextMenuEvent) => {
            if (!editable) {
                return;
            }

            setCurrentColumn(column as Column);
            setContextMenuType('Column');
            setIsOpenContextMenu(true);
        },
        [editable]
    );

    const handleCellContextMenu = useCallback(
        ({column, event, node}: CellContextMenuEvent) => {
            if (!editable) {
                return;
            }

            setCurrentColumn(column);
            setContextMenuType('Cell');
            setIsOpenContextMenu(true);
            const {clientX, clientY} = event as PointerEvent;
            setContextMenuPosition({left: clientX, top: clientY});
            setCurrentRowNode(node as RowNode);
        },
        [editable]
    );

    useShortKey({ctrlKey: true, code: 'KeyC', keydown: handleCopyValue});
    useShortKey({metaKey: true, code: 'KeyC', keydown: handleCopyValue});

    return (
        <>
            <AgGridContainer id={gridId}>
                <AgGridReact
                    ref={gridRef}
                    theme={editable ? editModeTheme : null}
                    gridId={gridId}
                    rowData={(onPageChange || onOffsetChange) ? undefined : rowData}
                    loading={loading}
                    defaultColDef={defaultColDef}
                    columnDefs={columnDefs}
                    components={components}
                    onBodyScroll={handleScroll}
                    suppressColumnVirtualisation
                    suppressRowVirtualisation
                    suppressFieldDotNotation
                    tooltipShowDelay={isDisableTooltip ? 1000 * 60 * 60 : 500} // 编辑态模拟禁用 Tooltip
                    headerHeight={40}
                    rowHeight={40}
                    onCellValueChanged={onCellValueChanged}
                    onColumnMoved={onColumnMoved}
                    onColumnHeaderContextMenu={handleColumnContextMenu}
                    onCellContextMenu={handleCellContextMenu}
                    tooltipInteraction
                    onRowClicked={() => {
                        exitEditAllHeaderNames(gridRef);
                        setIsOpenContextMenu(false);
                    }}
                    onColumnHeaderClicked={() => {
                        setIsOpenContextMenu(false);
                    }}
                />
            </AgGridContainer>
            <ContextMenu
                gridRef={gridRef}
                gridId={gridId}
                editable={editable}
                isOpen={isOpenContextMenu}
                rowIdName={rowIdName}
                position={contextMenuPosition}
                type={contextMenuType}
                currentColumn={currentColumn}
                currentRowNode={currentRowNode}
                onChangeContextMenuType={setContextMenuType}
                onRowAdded={onRowAdded}
                onColAdded={onColAdded}
                onRowRemoved={onRowRemoved}
                onColRemoved={onColRemoved}
                closeContextMenu={() => setIsOpenContextMenu(false)}
                handleChangeFormatters={handleChangeFormatters}
                onSaveHeaderName={onSaveHeaderName}
            />
        </>
    );
};

export default AGGrid;
