import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {Dropdown} from 'antd';
import type {MenuProps} from 'antd';
import {PlusOutlined} from '@ant-design/icons';
import {message} from '@panda-design/components';
import styled from '@emotion/styled';
import {
    setFileTreeParentPath,
    setIsShowFileTreeCreateModal,
} from '@/regions/icode/dataset/fileTreeCreateRegion';
import {apiPostCreateTempFile} from '@/api/icode/datasetFileEdit';
import {pushTempFiles} from '@/regions/dataset/tempFiles';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {getFileUrl} from '@/utils/icode/route/getFileUrl';
import {useCurrentRefName} from '@/hooks/icode/current/useCurrentRefName';

const PlusIcon = styled(PlusOutlined)`
    cursor: pointer;
    margin-right: 4px;

    &:hover {
        color: var(--color-brand-6);
    }
`;

const items: MenuProps['items'] = [
    {
        key: 'createFile',
        label: '新建文件',
    },
    {
        key: 'createFolder',
        label: '新建目录',
    },
];

interface Props {
    folderPath?: string;
    [key: string]: any;
}

const CreateButton = ({folderPath = '', ...props}: Props) => {
    const repoName = useCurrentRepoName();
    const refName = useCurrentRefName();
    const navigate = useNavigate();

    const handleSelectCreateType: MenuProps['onClick'] = useCallback(
        async ({key, domEvent}) => {
            domEvent.stopPropagation();
            switch (key) {
                case 'createFolder':
                    setFileTreeParentPath(folderPath);
                    setIsShowFileTreeCreateModal(true);
                    break;
                case 'createFile':
                {
                    const params = {
                        dataset: repoName,
                        fileDir: folderPath,
                    };

                    try {
                        const result = await apiPostCreateTempFile(params);
                        pushTempFiles(repoName, result);
                        // 跳转到新建的临时文件
                        const url = getFileUrl({
                            repoName,
                            type: 'blob',
                            encodedRefName: encodeURIComponent(refName),
                            path: result?.path,
                        });

                        navigate(url);
                    }
                    catch (e) {
                        message.error('新建文件失败');
                    }

                    break;
                }
            }
        },
        [folderPath, navigate, refName, repoName]
    );

    return (
        <Dropdown
            menu={{
                items,
                onClick: handleSelectCreateType,
            }}
        >
            <PlusIcon {...props} onClick={e => e.stopPropagation()} />
        </Dropdown>
    );
};

export default CreateButton;
