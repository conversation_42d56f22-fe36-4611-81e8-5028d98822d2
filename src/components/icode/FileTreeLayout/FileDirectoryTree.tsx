import {Key, ReactNode, useCallback, useEffect, useMemo, useState} from 'react';
import {Tree} from 'antd';
import styled from '@emotion/styled';
import stableStringify from 'json-stable-stringify';
import {isEmpty} from 'lodash';
import {css, cx} from '@emotion/css';
import {useNavigate} from 'react-router-dom';
import {DownOutlined} from '@ant-design/icons';
import {fontSize} from '@panda-design/components';
import {FormProvider} from '@panda-design/path-form';
import {loadFileNode, useFileNode, useForceUpdateFileTree} from '@/regions/icode/fileTree';
import {pushRealFiles} from '@/regions/dataset/realFiles';
import {useCurrentPath} from '@/hooks/icode/current/useCurrentPath';
import {useCurrentBranchType} from '@/hooks/icode/current/useCurrentBranchType';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {useCurrentRefName} from '@/hooks/icode/current/useCurrentRefName';
import {useIsDataset} from '@/providers/icode/IsDatasetProvider';
import {useDefaultShowSystemFile, useFileSortType} from '@/regions/icode/datasetFileSettings';
import {getFileUrl} from '@/utils/icode/route/getFileUrl';
import {directoryTreeCss} from '@/styles/components';
import FileTreeCreateModal from '../FileTreeCreateModal';
import {loadFileNodeAndPathAndReturnKeys} from './loadFileNodeAndPathAndReturnKeys';
import {DataNodeExtend, filterTree, toTreeData} from './toTreeData';
import CreateButton from './CreateButton';

const mainCss = css`
    overflow-y: auto;
    font-size: 14px;
    flex: 1;
`;

interface SelectInfo {
    node: DataNodeExtend;
}

interface Props {
    searchValue?: string;
}

const TitleContainer = styled.div`
    &:hover {
        width: calc(100% - 28px);
        overflow: hidden;
        text-overflow: ellipsis;

        .btn-create-folder {
            visibility: visible;
        }
    }
`;

const AddFolderContainer = styled.span`
    position: absolute;
    right: 5px;

    .btn-create-folder {
        visibility: hidden;
    }
`;

export const FileDirectoryTree = ({searchValue}: Props) => {
    const count = useForceUpdateFileTree();
    const navigate = useNavigate();
    const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);
    const repoName = useCurrentRepoName();
    const refName = useCurrentRefName();
    const path = useCurrentPath();
    const isDataset = useIsDataset();
    const fileNode = useFileNode({repo: repoName, commit: refName, path: ''});
    const branchType = useCurrentBranchType();
    const isShowSystemFile = useDefaultShowSystemFile();
    const fileSortType = useFileSortType();

    const treeData = useMemo(
        () => {
            const list = fileNode?.children || [];
            if (!isEmpty(list)) {
                return toTreeData(list, {repo: repoName, commit: refName}, {isShowSystemFile, isDataset, fileSortType});
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [fileNode?.children, repoName, refName, isShowSystemFile, isDataset, fileSortType, count]
    );

    useEffect(
        () => {
            const keys = loadFileNodeAndPathAndReturnKeys({
                repo: repoName,
                commit: refName,
                type: branchType,
                path: path,
                withFileSize: isDataset,
            });
            setExpandedKeys(keys);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [branchType, repoName, refName]
    );

    const handleLoadData = useCallback(
        async (treeNode: DataNodeExtend) => {
            return loadFileNode({
                repo: repoName,
                commit: refName,
                type: branchType,
                path: treeNode.key as string,
                withFileSize: isDataset,
                isDataset,
            });
        },
        [repoName, refName, branchType, isDataset]
    );

    const handleSelect = useCallback(
        (_: Array<string | number>, e: SelectInfo) => {
            const {type, path} = e.node.fileNode;
            if (isDataset && e.node.fileNode?.type === 'BLOB') {
                pushRealFiles(repoName, e.node.fileNode);
            }

            const url = getFileUrl({
                repoName,
                type: type.toLowerCase(),
                encodedRefName: encodeURIComponent(refName),
                path,
            });
            navigate(url);
        },
        [isDataset, navigate, refName, repoName]
    );

    const renderTitle = useCallback(
        (nodeData: DataNodeExtend) => {
            return (
                // eslint-disable-next-line no-undef
                <TitleContainer>{nodeData.title as ReactNode}
                    {
                        !nodeData.isLeaf && (
                            <AddFolderContainer>
                                <CreateButton className="btn-create-folder" folderPath={nodeData.key as string} />
                            </AddFolderContainer>
                        )
                    }
                </TitleContainer>
            );
        },
        []
    );

    const filteredTreeData = useMemo(
        () => {
            if (isEmpty(searchValue)) {
                return treeData;
            }
            return filterTree(treeData, searchValue);
        },
        [searchValue, treeData]
    );

    return (
        <>
            <Tree.DirectoryTree
                blockNode
                key={stableStringify({repo: repoName, commit: refName})}
                className={cx(directoryTreeCss, mainCss)}
                switcherIcon={<DownOutlined className={fontSize(12)} />}
                loadData={handleLoadData}
                // @ts-expect-error
                onSelect={handleSelect}
                selectedKeys={[path]}
                expandedKeys={expandedKeys}
                treeData={filteredTreeData}
                titleRender={isDataset ? renderTitle : undefined}
                onExpand={setExpandedKeys}
            />
            <FormProvider>
                <FileTreeCreateModal />
            </FormProvider>
        </>
    );
};
