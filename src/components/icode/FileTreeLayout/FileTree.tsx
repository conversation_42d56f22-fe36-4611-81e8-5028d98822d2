import {useState} from 'react';
import {Tabs} from 'antd';
import {css} from '@emotion/css';
import styled from '@emotion/styled';
import {useCurrentRepo} from '@/regions/icode/currentRepo';
import {useSymbolUrlInfo} from '@/hooks/icode/url/useSymbolUrlInfo';
import {useIsDataset} from '@/providers/icode/IsDatasetProvider';
import {SymbolListView} from '../SymbolListView/SymbolListView';
import Settings from './Settings';
import FileListSortButton from './FileListSortButton';
import {FileTreeView} from './FileTreeView';
import CreateButton from './CreateButton';

const tabsCss = css`
    width: 100% !important;

    .ant-5-tabs-nav-wrap {
        margin-left: 10px;
    }

    .ant-5-tabs-tab-btn {
        font-size: 14px !important;
    }
`;

const ViewDiv = styled.div`
    position: sticky;
    top: var(--icode-sticky-top);
    height: var(--icode-sticky-height);
    overflow-x: hidden;
`;

const SettingContainer = styled.div`
    font-size: 16px;
    margin-top: 4px;
    margin-right: 16px;
`;

const fileItem = {
    key: 'file',
    label: '文件',
    children: <FileTreeView />,
};

const symbolItem = {
    key: 'symbol',
    label: '符号',
    children: <SymbolListView />,
};

const FileTree = () => {
    const repo = useCurrentRepo();
    /**
     * @note: 如有指定 symbol name, 则默认使用 symbol tab
     */
    const symbolUrlInfo = useSymbolUrlInfo();
    const [tabKey, setTabKey] = useState<'file' | 'symbol'>(
        symbolUrlInfo.symbolInfo.name ? 'symbol' : 'file'
    );

    const isDataset = useIsDataset();

    if (!repo) {
        return null;
    }

    return (
        <ViewDiv>
            <Tabs
                className={tabsCss}
                size="small"
                activeKey={tabKey}
                onChange={setTabKey as (activeKey: string) => void}
                items={isDataset ? [fileItem] : [fileItem, symbolItem]}
                tabBarExtraContent={isDataset ? (
                    <SettingContainer>
                        <CreateButton />
                        <FileListSortButton />
                        <Settings />
                    </SettingContainer>
                ) : null}
            />
        </ViewDiv>
    );
};

export default FileTree;
