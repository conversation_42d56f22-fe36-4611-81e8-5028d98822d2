import {createContext, ReactNode, RefObject, useContext, useRef} from 'react';
interface ScratchWordsMarkdownBaseProps {
    predictRecordID?: number;
    chatRecordID?: number;
    getContainer?: () => HTMLElement;
    disabled?: boolean;
    containerId?: string;
    exceptSelectors?: string[];
}
interface ProviderProps extends ScratchWordsMarkdownBaseProps {
    children: ReactNode;
}
interface ContextValue extends ScratchWordsMarkdownBaseProps {
    ref: RefObject<any>;
}

const Context = createContext<ContextValue>({} as ContextValue);

export const ScratchWordsBaseProvider = ({
    children,
    ...rest
}: ProviderProps) => {
    const ref = useRef<any>(null);
    return (
        <Context.Provider
            value={{
                ...rest,
                ref,
            }}
        >
            {children}
        </Context.Provider>
    );
};

export const useScratchWordsBaseContext = () => {
    return useContext(Context);
};
