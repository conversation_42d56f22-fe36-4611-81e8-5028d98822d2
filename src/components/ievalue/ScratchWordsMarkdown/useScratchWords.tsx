import {useState, useCallback} from 'react';
import {apiDelineateWordTagCreate, apiDelineateWordTagUpdate} from '@/api/ievalue/task';
import {useEvaluateTagList, useGroupCaseInfo, useTaskInfo} from '@/hooks/ievalue/task';

export const useScratchWords = ({
    chatRecordID,
    refresh,
    predictRecordID,
}: {
    chatRecordID: number;
    refresh: () => void;
    predictRecordID: number;
}) => {
    const [groupInfo] = useGroupCaseInfo();
    const [refreshComp, setRefreshComp] = useState<string>(
        new Date().getTime().toString()
    );
    const [taskInfo] = useTaskInfo();
    const [tagList] = useEvaluateTagList(taskInfo?.spacePolicyID ?? 0);
    const onRefreshComp = useCallback(
        () => {
            setRefreshComp(new Date().getTime().toString());
        },
        []
    );

    const onChangeDatas = useCallback(
        (props: {eventType: string, id: any, item?: any, tags?: any[], callback?: () => void}) => {
            const {eventType, tags, id, item, callback} = props;
            if (tags && tags?.length > 0) {
                if (eventType === 'markEdit') {
                    apiDelineateWordTagUpdate({delineateWordID: id, position: item}).then(_ => {
                        if (callback) {
                            callback();
                        } else {
                            refresh();
                        }

                    }).catch(_ => {});
                } else {
                    const params = tags?.map(e => ({
                        delineateWordID: id,
                        position: item,
                        predictRecordID: predictRecordID,
                        stageID: groupInfo.stageID,
                        stageName: groupInfo.stageName,
                        tagName: e.tagName,
                        chatRecordID,
                        eventType,
                    }));
                    apiDelineateWordTagCreate(params).then(_ => {
                        if (callback) {
                            callback();
                        } else {
                            refresh();
                        }
                    }).catch(_ => {});
                }

            }
        },
        [
            chatRecordID,
            groupInfo.stageID,
            groupInfo.stageName,
            predictRecordID,
            refresh,
        ]
    );


    return {onChangeDatas, refreshComp, onRefreshComp, tagList, groupInfo};
};
