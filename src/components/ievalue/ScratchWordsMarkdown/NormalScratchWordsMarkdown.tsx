import {
    memo,
    useCallback,
    useEffect,
    useMemo,
    useRef,
} from 'react';
import {useDelineateWordTagList} from '@/hooks/ievalue/task';
import {DelineateWordItem} from '@/api/ievalue/task';
import {Markdown} from '@/design/Markdown';
import {ScratchWords} from '../ScratchWords';
import {useScratchWords} from './useScratchWords';
import {ScratchWordsProvider} from './ScratchWordsProvider';
import {useScratchWordsBaseContext} from './ScratchWordsBaseProvider';

interface Ref {
    reset: () => void;
}
export const configSingleData = (data: DelineateWordItem[]) => {
    return data?.filter(item => !!item.tagList?.length && !!item.position);
};
export const NormalScratchWordsMarkdown = memo(({content}: {content: string}) => {
    const {
        predictRecordID,
        chatRecordID,
        getContainer,
        disabled,
        containerId,
    } = useScratchWordsBaseContext();
    const ref = useRef<Ref>(null);
    const [data, {refresh, pending}] =
        useDelineateWordTagList(predictRecordID);

    const {onChangeDatas, tagList, refreshComp, onRefreshComp, groupInfo} =
        useScratchWords({refresh, predictRecordID, chatRecordID});

    const list = useMemo(
        () => {
            return configSingleData(data);
        },
        [data]
    );
    const refreshData = useCallback(
        (callback?: (list: DelineateWordItem[]) => void) => {
            refresh((data: DelineateWordItem[]) => {
                callback?.(configSingleData(data));
            });
        },
        [refresh]
    );

    useEffect(
        () => {
            setTimeout(() => {
                onRefreshComp();
            }, 2000);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [content]
    );

    return (
        <ScratchWordsProvider
            key={`${containerId}_${predictRecordID}_${
                chatRecordID || ''
            }_${refreshComp}`}
            pending={pending}
            predictRecordID={predictRecordID}
            chatRecordID={chatRecordID}
            groupInfo={groupInfo}
            list={list}
            disabled={disabled}
            onChangeDatas={onChangeDatas}
            refresh={refreshData}
            tag={`${containerId}_${predictRecordID}_${
                chatRecordID || ''
            }_${refreshComp}`}
            getContainer={getContainer}
            tagList={tagList}
            onRefreshComp={onRefreshComp}
        >
            <ScratchWords ref={ref}>
                <Markdown content={content} codeHighlight />
            </ScratchWords>
        </ScratchWordsProvider>
    );
});
