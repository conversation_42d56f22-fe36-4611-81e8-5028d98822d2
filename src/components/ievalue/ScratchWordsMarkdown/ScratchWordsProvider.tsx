import {ReactNode, createContext, useContext, useRef, RefObject, useCallback, useEffect} from 'react';
import PubSub from 'pubsub-js';
import {GroupCaseInfoItem} from '@/api/ievalue/case';
import {DelineateWordItem} from '@/api/ievalue/task';
import {onChangeDatasType} from '../ScratchWords';

export interface ScratchWordsBaseType {
    isMuti?: boolean;
    pending?: boolean;
    disabled?: boolean;
    predictRecordID?: number;
    chatRecordID?: number;
    groupInfo?: GroupCaseInfoItem;
    list: DelineateWordItem[];
    onChangeDatas: onChangeDatasType;
    refresh: (callback?: (data: DelineateWordItem[]) => void) => void;
    getContainer?: () => HTMLElement;
    tagList?: any[];
    onRefreshComp?: () => void;
    exceptSelectors?: string[];
}
interface ContextValue extends ScratchWordsBaseType {
    // highlighter: Highlighter;
    tagRef: RefObject<string>;
    // update: (id: string, content: any, type: string) => void;
    // getItem: (id: string) => Position;
    // reset: (data?: DelineateWordItem[]) => void;
}
const Context = createContext<ContextValue>({} as ContextValue);


export interface ScratchWordsProviderProps extends ScratchWordsBaseType {
    children: ReactNode;
    tag?: string;
}

export const ScratchWordsProvider = ({
    children,
    isMuti,
    disabled,
    pending,
    predictRecordID,
    chatRecordID,
    groupInfo,
    list,
    tag,
    onChangeDatas,
    refresh,
    getContainer,
    tagList,
    onRefreshComp,
    exceptSelectors,
}: ScratchWordsProviderProps) => {

    const tagRef = useRef(tag || new Date().getTime().toString());
    const getSWContainer = useCallback(
        () => {
            return getContainer?.() || document.querySelector('.fullscreen-enabled') || document.body;
        },
        [getContainer]
    );
    const refreshScratchWords = useCallback(
        () => {
            refresh?.((_: DelineateWordItem[]) => {
                onRefreshComp?.();
            });
        },
        [onRefreshComp, refresh]
    );

    useEffect(
        () => {
            const swRefresh = PubSub.subscribe('refreshScratchWords', refreshScratchWords);
            return () => {
                PubSub.unsubscribe(swRefresh);
            };
        },
        [refresh, refreshScratchWords]
    );
    return (
        <Context.Provider
            value={{
                isMuti,
                pending,
                predictRecordID,
                chatRecordID,
                groupInfo,
                getContainer: getSWContainer,
                list,
                tagRef,
                onChangeDatas,
                refresh,
                tagList,
                disabled,
                exceptSelectors,
            }}
        >
            {children}
        </Context.Provider>
    );
};

export const useScratchWordsContext = () => {
    return useContext(Context);
};

