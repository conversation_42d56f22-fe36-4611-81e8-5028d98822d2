import {
    ReactNode,
    memo,
    useCallback,
    useEffect,
    useMemo,
    useRef,
} from 'react';
import {DelineateWordItem, MultiDelineateWordItem} from '@/api/ievalue/task';
import {useResourceMultiDelineateWordTagList} from '@/hooks/ievalue/task';
import {ScratchWords} from '../ScratchWords';
import {useScratchWords} from './useScratchWords';
import {ScratchWordsProvider} from './ScratchWordsProvider';
interface ScratchWordsMarkdownProps {
    content: any;
    containerId?: string;
    codeHighlight: boolean;
    predictRecordID?: number;
    chatRecordID?: number;
    getContainer?: () => HTMLElement;
    children?: ReactNode;
    disabled?: boolean;
    exceptSelectors?: string[];
}
interface Ref {
    reset: () => void;
}

export const configData = (data: MultiDelineateWordItem[], chatRecordID: number) => {
    const delineateWordTags = data?.find(
        (e: MultiDelineateWordItem) => e.chatRecordID === chatRecordID
    )?.delineateWordTags;
    return delineateWordTags?.filter(item => !!item.tagList?.length && !!item.position);
};

export const ChatScratchWords = memo((props: ScratchWordsMarkdownProps) => {
    const {predictRecordID, chatRecordID, containerId, getContainer, content, children, disabled, exceptSelectors} =
        props;
    const ref = useRef<Ref>(null);

    // const {refresh, data} = useMultiDelineateWordTagList(predictRecordID);
    const [data, {refresh, pending}] = useResourceMultiDelineateWordTagList(predictRecordID);

    const {onChangeDatas, tagList, refreshComp, onRefreshComp, groupInfo} =
        useScratchWords({refresh, predictRecordID, chatRecordID});

    const list = useMemo(
        () => {
            return configData(data, chatRecordID);
        },
        [chatRecordID, data]
    );

    const refreshData = useCallback(
        (callback?: (list: DelineateWordItem[]) => void) => {
            refresh((data: MultiDelineateWordItem[]) => {
                callback?.(configData(data, chatRecordID));
            });
        },
        [refresh, chatRecordID]
    );

    useEffect(
        () => {
            setTimeout(() => {
                onRefreshComp();
            }, 1000);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [content]
    );

    return (
        <ScratchWordsProvider
            isMuti
            disabled={disabled}
            pending={pending}
            key={`${containerId}_${predictRecordID}_${chatRecordID}_${refreshComp}`}
            predictRecordID={predictRecordID}
            chatRecordID={chatRecordID}
            groupInfo={groupInfo}
            list={list}
            onChangeDatas={onChangeDatas}
            refresh={refreshData}
            tag={`${containerId}_${predictRecordID}_${chatRecordID}`}
            getContainer={getContainer}
            tagList={tagList}
            onRefreshComp={onRefreshComp}
            exceptSelectors={exceptSelectors}
        >

            <ScratchWords
                ref={ref}
            >
                {children}
            </ScratchWords>

        </ScratchWordsProvider>
    );
});
