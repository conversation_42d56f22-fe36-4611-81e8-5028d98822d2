/* eslint-disable max-statements */
/* eslint-disable func-names */
/* eslint-disable max-lines */
import {css} from '@emotion/css';
import {
    startTransition,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
import Highlighter from 'web-highlighter';
import {RootElement} from 'web-highlighter/dist/types';
import HighlightSource from 'web-highlighter/dist/model/source';
import {createRoot} from 'react-dom/client';
import {debounce, uniqueId} from 'lodash';
import {
    DelineateWordItem,
    TagItem,
    TagList,
    apiDelineateWordTagDelete,
} from '@/api/ievalue/task';
import {
    getOnceByRegion,
    resetOnceByRegion,
    setOnceByRegion,
} from '@/regions/ievalue/prompt/onceRegion';
import {useScratchWordsContext} from '../ScratchWordsMarkdown/ScratchWordsProvider';
import {CompBaseType, CompSaveType} from './useComp';
import {useSetClass} from './useSetClass';
import {addTip} from './useSetClass';
import {PopCreate} from './PopCreate';
import {PopCreateProvider} from './PopCreate/PopCreateProvider';
import {ColorType} from './PopTextArea/ColorsTool';

function getIds(
    selected: { $node: { parentNode: HTMLElement } },
    ht: Highlighter
) {
    if (!selected || !selected.$node || !selected.$node.parentNode) {
        return [];
    }
    return [
        ht.getIdByDom(selected.$node.parentNode),
        ...ht.getExtraIdByDom(selected.$node.parentNode),
    ].filter(i => i);
}
function getIntersection(arrA: any[], arrB: any[]) {
    const record: any = {};
    const intersection: any[] = [];
    arrA.forEach(i => (record[i] = true));
    arrB.forEach(
        i => record[i] && intersection.push(i) && (record[i] = false)
    );
    return intersection;
}

function isScrollable(ele: any) {
    const style = window.getComputedStyle(ele);
    return (
        style.overflow === 'auto'
        || style.overflow === 'scroll'
        || style.overflowX === 'auto'
        || style.overflowY === 'auto'
        || style.overflowX === 'scroll'
        || style.overflowY === 'scroll'
    );
}
function getOffsetParent(ele: any) {
    let offsetParent = ele.offsetParent;
    while (
        offsetParent
        && offsetParent !== document.body
        && !offsetParent?.className?.includes('fullscreen-enabled')
        && !isScrollable(offsetParent)
    ) {
        offsetParent = offsetParent.offsetParent;
    }
    return offsetParent;
}

/*
function includeClassNameByParent(ele: HTMLElement, classNames: string[]) {
    let parentElement = ele.parentElement;
    while (
        parentElement
        && parentElement !== document.body
        && !classNames.includes(parentElement.className)
    ) {
        console.log('parentElement', parentElement.className);
        parentElement = parentElement.parentElement;
    }
    return !!(
        parentElement !== document.body
        && classNames.includes(parentElement.className)
    );
}
*/
function getElementPositionRelativeToScrollingParent(ele: any) {
    const rect = ele.getBoundingClientRect();
    const bodyRect = document.body.getBoundingClientRect();
    let offsetTop = rect.top;
    let offsetLeft = rect.left;
    let currentEle = ele;

    // 遍历元素的祖先元素，累加偏移量，直到找到滚动父容器或body
    while (currentEle) {
        const offsetParent = getOffsetParent(currentEle);
        if (offsetParent) {
            offsetTop += isScrollable(offsetParent)
                ? offsetParent.scrollTop
                : 0;
            offsetLeft += isScrollable(offsetParent)
                ? offsetParent.scrollLeft
                : 0;
            currentEle = offsetParent === document.body ? null : offsetParent;
        } else {
            break;
        }
    }
    return {
        top: offsetTop - bodyRect.top,
        left: offsetLeft,
        width: ele.offsetWidth,
    };
}

const swHighlight = css`
    text-decoration-line: underline;
    color: #0da4f2;
`;

export const markHighlight = (colors: ColorType) => {
    return css`
        background-color: ${colors.backgroundColor};
        color: ${colors.color};
    `;
};

export const cb: any = (ht: Highlighter, id: string, selectedNodes: any[]) => {
    const nodes = selectedNodes.filter(n => n.$node.textContent);
    if (nodes.length === 0) {
        return [];
    }

    const candidates = nodes
        .slice(1)
        .reduce(
            (left: any, selected: any) =>
                getIntersection(left, getIds(selected, ht)),
            getIds(nodes[0], ht)
        );
    for (let i = 0; i < candidates?.length; i++) {
        if (ht.getDoms(candidates[i]).length === nodes.length) {
            return [];
        }
    }
    return selectedNodes;
};

/*
 * @Descripttion: automobile
 * @version: 1.0
 * @Author: 刘钊
 * @Date: 2023-10-14 19:39:08
 */
export const useHighlighter = function () {
    const [highlighter, setHighlighter] = useState<Highlighter>();
    const {
        tagRef,
        list,
        onChangeDatas,
        refresh,
        disabled,
        getContainer,
        exceptSelectors,
    } = useScratchWordsContext();
    const key = useMemo(
        () => {
            return tagRef.current;
        },
        [tagRef]
    );
    const storeRef = useRef<any>({});
    const dataRef = useRef(list);
    const hoveredTipIdRef = useRef<string>('');
    const historyEventRef = useRef('');

    const htRef = useRef<Highlighter>(
        new Highlighter({
            exceptSelectors: ['.addTip', 'pre', 'code'],
            wrapTag: 'span',
        })
    );
    useEffect(
        () => {
            dataRef.current = list;
        },
        [list]
    );

    const removeData = useCallback(
        (id: string) => {
            const data = dataRef.current;
            dataRef.current = data?.filter(e => e.delineateWordID !== id);
        },
        []
    );

    const getAllDatas = useCallback(
        () => {
            return storeRef.current[key] || [];
        },
        [key]
    );
    const save = useCallback(
        (source: any) => {
            const map: any = {};
            const list = getAllDatas();
            list.forEach((store: any, idx: number) => (map[store.hs.id] = idx));
            let data = source;
            if (!Array.isArray(data)) {
                data = [data];
            }

            data.forEach((item: any) => {
                // update
                if (
                    map[item.hs.id] !== undefined
                    && item.hs?.extra?.type !== 'edit'
                ) {
                    list[map[item.hs.id]] = {
                        ...item,
                        hs: {
                            ...item.hs,
                            extra: {
                                ...item.hs.extra,
                                type: 'selection',
                                value: '',
                            },
                        },
                    };
                }
                // append
                else {
                    list.push({
                        ...item,
                        hs: {
                            ...item.hs,
                            extra: {
                                ...item.hs.extra,
                                type:
                                    item.hs?.extra?.type === 'edit'
                                        ? 'edit'
                                        : 'selection',
                                value: '',
                            },
                        },
                    });
                }
            });
            storeRef.current = {
                ...storeRef.current,
                [key]: list,
            };
        },
        [getAllDatas, key]
    );
    const removeCreateTag = useCallback(
        (highlighter: any, tag: string) => {
            const data = getAllDatas();
            const newData: any[] = [];
            data.forEach((element: any) => {
                if (element.hs?.extra?.type === 'edit') {
                    newData.push(element);
                } else {
                    highlighter.removeClass('swHighlight', element.hs.id);
                    highlighter.remove(element.hs.id);
                    const $ele = document.querySelector(`.addTip_${tag}`);
                    $ele?.parentNode?.removeChild($ele);
                    removeData(element.hs.id);
                }
            });

            storeRef.current = {
                ...storeRef.current,
                [key]: newData,
            };
        },
        [getAllDatas, key, removeData]
    );
    const remove = useCallback(
        (id: string) => {
            let index = null;
            const data = getAllDatas();
            for (let i = 0; i < data.length; i++) {
                if (data[i].hs.id === id) {
                    index = i;
                    break;
                }
            }
            data.splice(index, 1);
            storeRef.current = {
                ...storeRef.current,
                [key]: data,
            };
        },
        [getAllDatas, key]
    );

    const removeAll = useCallback(
        () => {
            storeRef.current = {
                ...storeRef.current,
                [key]: [],
            };
        },
        [key]
    );
    const getItem = useCallback(
        (id: string) => {
            const allList = getAllDatas();
            const data = allList?.find((e: any) => e?.hs?.id === id);
            const refData = dataRef.current?.find(
                (e: any) => e?.position?.hs?.id === id
            );
            return data || refData?.position;
        },
        [getAllDatas]
    );
    const update = useCallback(
        (id: string, content: any, type: string) => {
            const allList = getAllDatas();
            const data = allList?.find((e: any) => e.hs.id === id);
            if (data) {
                data.hs.extra = {
                    ...data.hs.extra,
                    type,
                    value: content,
                };
            }
            //  else {
            //     dataRef.current = dataRef.current?.map((e: any) => {
            //         if (e.position?.hs?.id === id) {
            //             e.position.hs.extra = {
            //                 ...e.position.hs.extra,
            //                 type,
            //                 value: content,
            //             };
            //         }
            //         return e;
            //     });
            // }
        },
        [getAllDatas]
    );
    const updateLocalData = useCallback(
        (id: string, content: any, type: string) => {
            const data = dataRef.current?.find(e => e.delineateWordID === id);
            const extra = data?.position?.hs?.extra;
            if (extra) {
                if (type === 'add') {
                    const tag = extra.value.find((e: any) =>
                        content.some((c: any) => c.ID === e.ID)
                    );
                    if (!tag) {
                        data.position.hs.extra = {
                            ...data.position.hs.extra,
                            type,
                            value: [...extra.value, ...content],
                        };
                        data.tagList.push(...content);
                    }
                } else if (type === 'delete') {
                    data.position.hs.extra =
                        data.position.hs.extra.value?.filter((e: any) =>
                            content.some((c: any) => c.ID === e.ID)
                        );
                    const tagList: TagList[] = [];
                    data.tagList.forEach(l => {
                        const tags = l.tags.filter((t: any) =>
                            content.some((c: any) => t.ID === c.ID)
                        );
                        tagList.push({
                            ...l,
                            tags,
                        });
                    });
                    data.tagList = tagList;
                } else {
                    data.position.hs.extra = {
                        ...data.position.hs.extra,
                        type,
                        value: content,
                    };
                }
            }
        },
        []
    );
    const updateDatas = useCallback(
        (datas: any[]) => {
            storeRef.current = {
                ...storeRef.current,
                [key]: datas,
            };
        },
        [key]
    );

    const getDatas = useCallback(
        () => {
            const data = getAllDatas();
            return data.filter((e: any) => e.hs?.extra?.type === 'edit');
        },
        [getAllDatas]
    );
    const {resetClass, removeAddClass, selectionClass} = useSetClass();
    const reset = useCallback(
        (data?: DelineateWordItem[], ht?: Highlighter) => {
            const highter = ht || htRef.current;
            if (htRef.current) {
                if (data) {
                    dataRef.current = data;
                }
                const datas = data || dataRef.current;

                if (datas?.length > 0) {
                    datas?.forEach((item: DelineateWordItem) => {
                        if (item.position) {
                            const hs = item.position.hs;
                            highter?.fromStore(
                                hs.startMeta,
                                hs.endMeta,
                                hs.text,
                                hs.id,
                                hs.extra
                            );
                            const colors = hs?.extra?.value?.[0]?.colors;
                            if (colors) {
                                // 不要删除 color: ${uniqueId()};
                                highter?.addClass(
                                    css`
                                        color: ${uniqueId()};
                                        background-color: ${colors.backgroundColor
                                        || 'unset'};
                                        color: ${colors.color || 'unset'};
                                    `,
                                    hs.id
                                );
                                // text-decoration-line: unset !important;
                            }
                        }
                    });
                } else {
                    highter?.removeAll();
                }
            }
        },
        []
    );

    const clearSelection = useCallback(
        (ht: Highlighter) => {
            const aels = document.querySelectorAll(`.addTip_${key}`);
            aels?.forEach((element: Element) => {
                element.parentNode?.removeChild(element);
            });
            const eels = document.querySelectorAll(`.pop_${key}`);
            eels?.forEach((element: Element) => {
                element.parentNode?.removeChild(element);
            });
            removeAddClass(ht, hoveredTipIdRef.current, key);
        },
        [key, removeAddClass]
    );
    const {
        predictRecordID,
        chatRecordID,
        groupInfo,
        tagList,
        isMuti,
        pending,
    } = useScratchWordsContext();

    const addClickListener = useCallback(
        () => {
            if (
                (historyEventRef.current === 'create'
                || historyEventRef.current === 'saved'
                || historyEventRef.current === 'HOVER'
                || historyEventRef.current === 'Create_HOVER')
            && hoveredTipIdRef.current
            ) {
                const id = hoveredTipIdRef.current;
                resetClass(htRef.current, id);
                const item = getItem(id);
                const extra = item?.hs?.extra;
                if (extra?.type !== 'edit') {
                    remove(id);
                    htRef.current?.remove(id);
                }
                clearSelection(htRef.current);
                hoveredTipIdRef.current = '';
                startTransition(() => {
                    if (!pending) {
                        reset(list, highlighter);
                    }
                });
            }
        },
        [
            clearSelection,
            getItem,
            highlighter,
            list,
            pending,
            remove,
            reset,
            resetClass,
        ]
    );

    useEffect(
        () => {
            document.addEventListener('click', addClickListener);
            return () => {
                document.removeEventListener('click', addClickListener);
            };
        },
        [addClickListener]
    );

    const onTagClick = useCallback(
        ({highlighter, id}: CompBaseType) => {
            clearSelection(highlighter);
            resetClass(highlighter, id);
            hoveredTipIdRef.current = '';
        },
        [clearSelection, resetClass]
    );
    const onMarkClick = useCallback(
        ({
            highlighter,
            id,
            colors,
            remark,
        }: CompBaseType & { colors: ColorType, remark?: string }) => {
            const item = getItem(id);
            const eventType = item?.hs?.extra?.value
                ? 'markEdit'
                : 'markCreate';
            update(id, [{colors, remark}], 'edit');
            updateLocalData(id, [{colors, remark}], 'edit');
            onChangeDatas({
                eventType,
                id,
                item: getItem(id),
                tags: [{colors, remark}],
                callback: () => {
                    resetClass(highlighter, id);
                    refresh((data: DelineateWordItem[]) => {
                        reset(data);
                    });
                },
            });
            historyEventRef.current = 'saved';
        },
        [
            getItem,
            onChangeDatas,
            refresh,
            reset,
            resetClass,
            update,
            updateLocalData,
        ]
    );

    const onDeleteMark = useCallback(
        ({highlighter, id}: { highlighter: Highlighter, id: string }) => {
            highlighter.addClass(
                css`
                    background-color: unset;
                    color: unset;
                `,
                id
            );
            refresh((data: DelineateWordItem[]) => {
                reset(data);
            });
            remove(id);
            removeData(id);
            highlighter.remove(id);
            // 移除所有弹窗
            clearSelection(highlighter);
            resetClass(highlighter, id);
            hoveredTipIdRef.current = '';
        },
        [clearSelection, refresh, remove, removeData, reset, resetClass]
    );

    const onSave = useCallback(
        ({highlighter, id, value}: CompSaveType) => {
            update(hoveredTipIdRef.current, value, 'edit');
            onChangeDatas({
                eventType: 'add',
                id,
                item: getItem(id),
                tags: value,
                callback: () => {
                    resetClass(highlighter, id);
                    refresh((data: DelineateWordItem[]) => {
                        reset(data);
                    });
                },
            });
            historyEventRef.current = 'saved';
        },
        [getItem, onChangeDatas, refresh, reset, resetClass, update]
    );

    const onTagDelete = useCallback(
        ({
            ht,
            item,
            id,
            tagList,
            tag,
        }: {
            ht: Highlighter;
            item: DelineateWordItem;
            id: string;
            tagList: TagList[];
            tag: TagItem;
        }) => {
            // eslint-disable-next-line no-console
            console.log(item, tagList);

            refresh((data: DelineateWordItem[]) => {
                reset(data);
            });
            if (!tagList || tagList?.length === 1) {
                const spreadTags: TagItem[] = [];
                tagList.forEach(e => {
                    spreadTags.push(...e.tags);
                });
                const tags = spreadTags?.filter(e => e.ID !== tag.ID);
                if (!tags.length) {
                    remove(id);
                    removeData(id);
                    ht.remove(id);
                    clearSelection(ht);
                    hoveredTipIdRef.current = '';
                }
            }
        },
        [clearSelection, refresh, remove, removeData, reset]
    );

    const onCreateTag = useCallback(
        (id: string, type: string) => {
            document
                .querySelectorAll('.addTipClass')
                ?.forEach(element => element?.remove());
            const doms = htRef.current?.getDoms(id);
            // eslint-disable-next-line no-console
            console.log('onCreateTag', id, doms);
            if (!doms?.length) {
                return;
            }
            hoveredTipIdRef.current = id;
            setTimeout(() => {
                historyEventRef.current = type;
                hoveredTipIdRef.current = id;
            }, 0);
            selectionClass(htRef.current, id);
            let sectionsText: string = '';
            let node = doms?.[0];
            doms?.forEach(n => {
                if (node.innerText === '\n' || node.innerText === '') {
                    node = n;
                }
                sectionsText += n.innerText;
                // 添加事件监听器，并在事件处理函数中调用 stopPropagation
                // n.addEventListener('click', event => {
                //     event.stopPropagation(); // 阻止事件冒泡
                //     // 其他处理逻辑
                //     // eslint-disable-next-line no-console
                //     console.log('Clicked on myElement');
                // });
            });

            const position = getElementPositionRelativeToScrollingParent(node);
            const {top, left} = position;
            const clientLeft = left + 30 - 58;
            const clientTop = top - 56;
            const $div = document.createElement('div');
            $div.style.left = `${clientLeft}px`;
            $div.style.top = `${clientTop}px`;
            $div.style.zIndex = '1050';
            $div.classList.add(addTip);
            $div.classList.add('addTipClass');
            $div.classList.add(`addTip_${key}`);
            const container = getContainer();
            container.appendChild($div);
            const root = createRoot($div);
            root.render(
                <PopCreateProvider
                    id={id}
                    disabled={disabled}
                    isMuti={isMuti}
                    highlighter={htRef.current}
                    groupInfo={groupInfo}
                    predictRecordID={predictRecordID}
                    chatRecordID={chatRecordID}
                    tagList={tagList}
                    sectionsText={sectionsText}
                    update={update}
                    getItem={getItem}
                    getContainer={getContainer}
                    onClick={() => {
                        onTagClick({highlighter: htRef.current, id});
                    }}
                    onSave={(value: any[]) => {
                        onSave({highlighter: htRef.current, id, value});
                    }}
                    onMark={(colors: ColorType, remark?: string) => {
                        onMarkClick({
                            highlighter: htRef.current,
                            id,
                            colors,
                            remark,
                        });
                    }}
                    onDeleteMark={(id: string) => {
                        if (!id) {
                            return;
                        }
                        apiDelineateWordTagDelete({
                            delineateWordID: id,
                        })
                            .then(() => {
                                onDeleteMark({
                                    highlighter: htRef.current,
                                    id,
                                });
                            })
                            .catch(() => {});
                    }}
                    onDelete={async (tagList: TagList[], tag: TagItem) => {
                        await apiDelineateWordTagDelete({ID: tag.ID});
                        onTagDelete({
                            ht: htRef.current,
                            item: null,
                            id,
                            tagList: [],
                            tag,
                        });
                    }}
                >
                    <PopCreate />
                </PopCreateProvider>
            );
        },
        [
            selectionClass,
            key,
            getContainer,
            disabled,
            isMuti,
            groupInfo,
            predictRecordID,
            chatRecordID,
            tagList,
            update,
            getItem,
            onTagClick,
            onSave,
            onMarkClick,
            onDeleteMark,
            onTagDelete,
        ]
    );
    const onDebounceCreateTag = debounce(onCreateTag, 300);

    const onHighlighterClick = useCallback(
        (ht: Highlighter, id: string, list: DelineateWordItem[]) => {
            const historyEvent = historyEventRef.current;
            if (!id) {
                historyEventRef.current = '';
                return;
            }
            const delineateWordItem = list?.find(
                e => e.delineateWordID === id
            );
            const item = delineateWordItem?.position;
            if (!item?.hs?.extra?.type) {
                historyEventRef.current = 'Create_HOVER';
                return;
            }
            if (
                item?.hs?.extra?.type === 'selection'
                && historyEvent === 'create'
            ) {
                historyEventRef.current = 'Create_HOVER';
                return;
            }
            const historyId = historyEventRef.current;
            if (historyId) {
                resetClass(ht, historyId);
                removeCreateTag(ht, key);
                historyEventRef.current = '';
            }
            onCreateTag(id, 'HOVER');
        },
        [key, onCreateTag, removeCreateTag, resetClass]
    );

    const onHighlighterCreate = useCallback(
        (ht: Highlighter, sources: HighlightSource[]) => {
            const historyId = hoveredTipIdRef.current;
            sources.forEach(s => {
                if (
                    historyId
                    && historyEventRef.current === 'create'
                    && historyId !== s.id
                ) {
                    resetClass(ht, historyId);
                    resetClass(ht, historyId);
                    ht.removeClass('swHighlight', historyId);
                    ht.remove(historyId);
                }
                historyEventRef.current = '';
                selectionClass(ht, s.id);
                hoveredTipIdRef.current = s.id;
                setTimeout(() => {
                    historyEventRef.current = 'create';
                }, 0);
            });
            onDebounceCreateTag(hoveredTipIdRef.current, 'create');

            const newSources = sources.map(hs => ({hs}));
            save(newSources);
        },
        [onDebounceCreateTag, resetClass, save, selectionClass]
    );

    const init = useCallback(
        () => {
            if (htRef.current) {
                const $root = document.querySelector(`#mdroot_${key}`);
                htRef.current?.setOption({
                    $root: $root as RootElement,
                    style: {
                        className: swHighlight,
                    },
                    exceptSelectors,
                });
                htRef.current
                    .on(Highlighter.event.CLICK, ({id}: { id: string }) => {
                    // eslint-disable-next-line no-console
                        console.log('CLICK', id);
                        if (!id) {
                            return;
                        }
                        onHighlighterClick(htRef.current, id, dataRef.current);
                    })
                    .on(
                        Highlighter.event.CREATE,
                        ({
                            sources,
                            type,
                        }: {
                        sources: HighlightSource[];
                        type: string;
                    }) => {
                        // eslint-disable-next-line no-console
                            console.log('create', sources, type);
                            if (type === 'from-input') {
                                onHighlighterCreate(htRef.current, sources);
                            }
                        }
                    );
                if (!disabled) {
                    htRef.current?.run();
                }
            }
        },
        [
            key,
            exceptSelectors,
            disabled,
            onHighlighterClick,
            onHighlighterCreate,
        ]
    );

    useEffect(
        () => {
            const ht = getOnceByRegion(key);
            ht?.stop?.();
            ht?.dispose?.();
            init();
            setHighlighter(() => htRef.current);
            setOnceByRegion(key, htRef.current);

            return () => {
                ht?.stop?.();
                ht?.dispose?.();
                resetOnceByRegion(key);
                document
                    .querySelectorAll('.addTipClass')
                    ?.forEach(element => element?.remove());
                // htRef.current = null;
            };
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [key]
    );

    useEffect(
        () => {
            reset(list);
        },
        [list, reset]
    );

    const wrapNodecb: any = useCallback(
        (id: string, node: HTMLElement) => {
        // onCreateTag(id);
            return node;
        },
        []
    );

    /**
     * FIXME: avoid re-highlighting the existing selection
     */
    // eslint-disable-next-line @reskript/hooks-deps-new-line
    useEffect(() => {
        if (highlighter) {
            highlighter.hooks.Render.SelectedNodes.tap((...rest) => {
                return cb(highlighter, ...rest);
            });
            // highlighter.hooks.Render.WrapNode.tap(wrapNodecb);
            highlighter.hooks.Serialize.Restore.tap((source: any) => {
                // console.log("Serialize.Restore hook -", source);

                return [source.startMeta, source.endMeta];
            });

            highlighter.hooks.Serialize.RecordInfo.tap(() => {
                const extraInfo = Math.random().toFixed(4);
                // eslint-disable-next-line no-console
                console.log('Serialize.RecordInfo hook -', extraInfo);
                return extraInfo;
            });
        }
    }, [highlighter, key, wrapNodecb]);

    return {
        highlighter: htRef.current,
        data: dataRef.current,
        save,
        remove,
        getDatas,
        getAllDatas,
        updateDatas,
        stores: storeRef.current[key] || [],
        removeCreateTag,
        removeAll,
        update,
        reset,
        getItem,
    };
};
