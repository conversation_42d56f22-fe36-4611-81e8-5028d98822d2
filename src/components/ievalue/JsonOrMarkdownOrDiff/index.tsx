import {isObject} from 'lodash';
import ReactJson from '@microlink/react-json-view';
import {Markdown} from '@/design/Markdown';
import {DiffView} from '@/components/ievalue/DiffView';
import {isJson} from '@/utils/ievalue/json';
import {NormalScratchWordsMarkdown} from '../ScratchWordsMarkdown/NormalScratchWordsMarkdown';

interface JsonOrMarkdownProps {
    content: any;
    diffBaseContent?: string;
    showOrigin?: boolean;
    enableScratchWords?: boolean;
}

const JsonOrMarkdownOrDiff = ({content, diffBaseContent, showOrigin, enableScratchWords}: JsonOrMarkdownProps) => {
    if (showOrigin) {
        return content;
    }

    if (diffBaseContent) {
        return <DiffView oldVal={diffBaseContent} newVal={content} />;
    }
    if (typeof content === 'string') {
        if (isJson(content) && isObject(JSON.parse(content))) {
            return (
                <ReactJson
                    src={JSON.parse(content)}
                    collapsed={false}
                    name={false}
                    indentWidth={2}
                    iconStyle="square"
                    displayDataTypes={false}
                />
            );
        }
        if (enableScratchWords) {
            return <NormalScratchWordsMarkdown content={content} />;
        }
        return <Markdown content={content} codeHighlight />;
    }

    return typeof content === 'string' ? (
        isJson(content) && isObject(JSON.parse(content)) ? (
            <ReactJson
                src={JSON.parse(content)}
                collapsed={false}
                name={false}
                indentWidth={2}
                iconStyle="square"
                displayDataTypes={false}
            />
        ) : (
            <Markdown content={content} codeHighlight />
        )
    ) : (
        content
    );
};

export default JsonOrMarkdownOrDiff;
