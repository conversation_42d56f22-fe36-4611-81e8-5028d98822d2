import {css} from '@emotion/css';
import {FlexLayout} from '../FlexLayout';
import {SearchResultsContentItem} from './SearchResultsContentItem';
import {useSearchResultsContext} from './SearchResultsProvider';

export const SearchResultsContent = () => {
    const {data} = useSearchResultsContext();
    return (
        <FlexLayout
            direction="column"
            gap={10}
            className={css`
                padding: 0 10px 10px 10px;
                box-sizing: border-box;
                flex-wrap: nowrap;
                .flex-item:last-child {
                    border-right: none;
                }
            `}
        >
            {data?.map(item => (
                <SearchResultsContentItem key={item.url} item={item} />
            ))}
        </FlexLayout>
    );
};
