import {Button} from '@panda-design/components';
import {useBatchMarkTaskIndex, setBatchMarkTaskIndexNext} from '@/regions/labelBatchTask/batchMarkTaskIndex';
import {useBatchTaskTotal} from '@/hooks/label/useLabelBatchTaskParams';
import {useLabelParams} from '@/hooks/label/useLabelParams';

export const SkipButton = () => {
    const {labelStudioProjectId} = useLabelParams();
    const currentIndex = useBatchMarkTaskIndex(labelStudioProjectId);
    const total = useBatchTaskTotal();

    return (
        <Button
            onClick={() => setBatchMarkTaskIndexNext(labelStudioProjectId)}
            disabled={currentIndex === total - 1}
            disabledReason="已经是最后一个了"
        >
            不标这条，跳过
        </Button>
    );
};
