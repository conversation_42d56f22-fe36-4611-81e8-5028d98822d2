import {useCallback, useEffect, useMemo, useState} from 'react';
import {Modal, message} from '@panda-design/components';
import {Input} from 'antd';
import {Field, useFormSubmit, useFormContext} from '@panda-design/path-form';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {
    useFilePublishModalOpen,
    resetFilePublishModalOpen,
} from '@/regions/icode/dataset/fileEditCommonRegion';
import {loadFileEditStatus, useFileEditStatus} from '@/regions/icode/dataset/fileEditStatusRegion';
import {loadVersionList, useVersionList} from '@/regions/icode/dataset/filePreviewVersionRegion';
import {apiPostPublishDatasetFileDraft} from '@/api/icode/datasetFileEdit';
import {loadFileNode} from '@/regions/icode/fileTree';
import {useCurrentBranchType} from '@/hooks/icode/current/useCurrentBranchType';
import {useIsDataset} from '@/providers/icode/IsDatasetProvider';
import {useCurrentRefName} from '@/hooks/icode/current/useCurrentRefName';
import {useCurrentPath} from '@/hooks/icode/current/useCurrentPath';
import {useFileNodeContext} from '@/icode/DatasetFileList/FileDetailMain/context/FileNodeContext';

const DatasetFilePublishModal = () => {
    const {onSelectVersion, fileNode} = useFileNodeContext();
    const {path, lfsOid} = fileNode;
    const isOpen = useFilePublishModalOpen(path);
    const {editSourceOid, draftSessionId, editAt, editBy} = useFileEditStatus(path);
    const [isPublishing, setIsPublishing] = useState(false);
    const repo = useCurrentRepoName();
    const versionList = useVersionList({path, oid: lfsOid, repo});
    const {resetFields, setFieldValue} = useFormContext();
    const branchType = useCurrentBranchType();
    const isDataset = useIsDataset();
    const refName = useCurrentRefName();
    const folderPath = useCurrentPath();
    const [latestVersion, setLatestVersion] = useState<string>(null);

    const fileName = useMemo(
        () => path.split('/').pop(),
        [path]
    );

    useEffect(
        () => {
            if (latestVersion) {
                return;
            }

            if (versionList.length) {
                const {version = ''} = versionList[0];
                const matches = version?.match(/\d+/g);
                setLatestVersion(matches ? `V${(matches.map(Number)[0] + 1)}` : 'V1');
            }
        },
        [latestVersion, versionList]
    );

    useEffect(
        () => {
            if (draftSessionId && isOpen) {
                setFieldValue('description', `${editBy}在${editAt}编辑的版本`);
            }
        },
        [draftSessionId, editAt, editBy, isOpen, setFieldValue]
    );

    const resetAllData = useCallback(
        () => {
            resetFilePublishModalOpen(path);
            setLatestVersion(null);
            resetFields();
        },
        [path, resetFields]
    );

    const handlePublish = useFormSubmit(
        async (values: any) => {
            const {description} = values;
            const params = {
                dataset: repo,
                path,
                oid: editSourceOid,
                timestamp: Date.now(),
                sessionId: draftSessionId,
                description: description ?? '',
            };

            try {
                setIsPublishing(true);
                await apiPostPublishDatasetFileDraft(params);
                setTimeout(async () => {
                    message.success('发布成功');
                    setIsPublishing(false);
                    await loadVersionList({path, oid: lfsOid, repo});
                    loadFileEditStatus({
                        dataset: repo,
                        path,
                        oid: editSourceOid,
                    });

                    onSelectVersion(null);
                    resetAllData();

                    // 发布后重新加载文件列表显示的版本号
                    loadFileNode({
                        repo,
                        commit: refName,
                        type: branchType,
                        path: folderPath,
                        forceUpdate: true,
                        withFileSize: true,
                        isDataset,
                    });
                }, 2000);
            }
            catch (e) {
                message.error(e.message);
                setIsPublishing(false);
            }
        }
    );

    return (
        <Modal
            title="发布"
            open={isOpen}
            onOk={handlePublish}
            onCancel={resetAllData}
            zIndex={9999}
            confirmLoading={isPublishing}
        >
            <Field name="fileName" label="文件名" required>
                {fileName}
            </Field>
            <Field name="filePath" label="存储路径" required>
                {path}
            </Field>
            <Field
                name="filePath"
                label="版本号"
                tooltip="系统已按照默认规则，为你自动生成版本号，如需特殊标记，请在下方添加版本描述"
                required
            >
                {latestVersion}{' (以实际发布为准)'}
            </Field>
            <Field name="description" label="描述">
                <Input.TextArea rows={4} placeholder="请输入版本信息描述" />
            </Field>
        </Modal>
    );
};

export default DatasetFilePublishModal;
