/* eslint-disable max-lines */
import {useCallback, useState} from 'react';
import {Input, Radio} from 'antd';
import {message, Modal} from '@panda-design/components';
import styled from '@emotion/styled';
import {Field, useFieldValue, useFormContext, useFormSubmit} from '@panda-design/path-form';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {apiPostPublishDatasetFileDraft} from '@/api/icode/datasetFileEdit';
import {getFileNode, loadFileNode} from '@/regions/icode/fileTree';
import {useCurrentRefName} from '@/hooks/icode/current/useCurrentRefName';
import {useCurrentBranchType} from '@/hooks/icode/current/useCurrentBranchType';
import {useIsDataset} from '@/providers/icode/IsDatasetProvider';
import {setTempFilePublishModalOpen, useTempFilePublishModalOpen} from '@/regions/icode/dataset/fileEditCommonRegion';
import DatasetTreeFilePathSelect from '@/components/icode/DatasetTreeFilePathSelect';
import {useCurrentUsername} from '@/regions/user/currentUser';
import {loadVersionList} from '@/regions/icode/dataset/filePreviewVersionRegion';
import {loadFileEditStatus} from '@/regions/icode/dataset/fileEditStatusRegion';
import {useFileNodeContext} from '@/icode/DatasetFileList/FileDetailMain/context/FileNodeContext';
import {useAllOpenedFileNodes} from '@/icode/DatasetFileList/hooks/useAllOpenedFileNodes';

const StyledModal = styled(Modal)`
    .ant-5-form-item {
        margin-bottom: 10px !important;
    }
`;

const fileExtensionOptions = [
    {
        label: '.jsonl',
        value: '.jsonl',
    },
    {
        label: '.csv',
        value: '.csv',
    },
    {
        label: '.xlsx',
        value: '.xlsx',
    },
];

const fileNamePattern = /^[a-zA-Z0-9\u4e00-\u9fa5_\-/]+$/;

interface Props {
    oid: string;
}

const TempFilePublishModal = ({oid}: Props) => {
    const {onSelectVersion, fileNode, draftSessionId} = useFileNodeContext();
    const {path, lfsOid} = fileNode;
    const isOpen = useTempFilePublishModalOpen(path);
    const {resetFields} = useFormContext();
    const dataset = useCurrentRepoName();
    const refName = useCurrentRefName();
    const branchType = useCurrentBranchType();
    const isDataset = useIsDataset();
    const fileName = useFieldValue('fileName');
    const fileExtension = useFieldValue('fileExtension');
    const formFolderPath = useFieldValue('folderPath');
    const userName = useCurrentUsername();
    const [isPublishing, setIsPublishing] = useState(false);
    const {closeFileNode} = useAllOpenedFileNodes();

    const isFileExist = useCallback(
        (newFileName: string, formFolderPath: string = '') => {
            const existFiles = getFileNode({
                repo: dataset,
                commit: refName,
                path: formFolderPath,
            });

            return (existFiles?.children ?? []).filter(item => item.type === 'BLOB')
                ?.some(item => item.name === newFileName);
        },
        [refName, dataset]
    );

    const validateFileName = useCallback(
        async (name: string) => {
            if (!fileNamePattern.test(name)) {
                return '只能包含汉字、字母、数字、下划线和中划线';
            }

            if (name.startsWith('/')) {
                return '不能以 / 开头';
            }

            if (isFileExist(`${name}${fileExtension}`, formFolderPath)) {
                return '文件已存在';
            }

            return undefined;
        },
        [fileExtension, formFolderPath, isFileExist]
    );

    const handleClose = useCallback(
        () => {
            resetFields();
            setTempFilePublishModalOpen(path, false);
        },
        [path, resetFields]
    );

    const handleSumbit = useFormSubmit(
        async (values: any) => {
            try {
                const {
                    fileName,
                    fileExtension,
                    folderPath,
                } = values;

                const filePath = `${folderPath}/${fileName}${fileExtension}`;

                const params = {
                    dataset,
                    path: filePath,
                    oid,
                    timestamp: Date.now(),
                    sessionId: draftSessionId,
                    description: `${userName} 新建的文件`,
                };

                try {
                    setIsPublishing(true);
                    await apiPostPublishDatasetFileDraft(params);
                    setTimeout(async () => {
                        message.success('发布成功');
                        setIsPublishing(false);
                        await loadVersionList({path: filePath, oid: lfsOid, repo: dataset});
                        loadFileEditStatus({
                            dataset,
                            path: filePath,
                            oid,
                        });

                        onSelectVersion(null);
                        handleClose();
                        closeFileNode(path, true);

                        // 发布后重新加载文件列表显示的版本号
                        loadFileNode({
                            repo: dataset,
                            commit: refName,
                            type: branchType,
                            path: folderPath,
                            forceUpdate: true,
                            withFileSize: true,
                            isDataset,
                        });
                    }, 3000);
                }
                catch (e) {
                    message.error(e.message);
                    setIsPublishing(false);
                }
            }
            catch (error) {
                message.error(error.message);
            }
        }
    );

    return (
        <StyledModal
            open={isOpen}
            title="发布文件"
            onCancel={handleClose}
            onOk={handleSumbit}
            confirmLoading={isPublishing}
            okButtonProps={{disabled: !fileName || fileName.length === 0}}
        >
            <Field name="fileName" label="文件名" validate={validateFileName}>
                <Input />
            </Field>
            <Field name="fileExtension" label="文件类型">
                <Radio.Group
                    options={fileExtensionOptions}
                />
            </Field>
            <Field name="folderPath" label="文件路径">
                <DatasetTreeFilePathSelect value={formFolderPath} style={{width: '100%'}} />
            </Field>
        </StyledModal>
    );
};

export default TempFilePublishModal;
