import {useRequestCallback} from 'huse';
import {useEffect, useMemo} from 'react';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {apiGetDatasetTable, apiGetMatchedDatasetTableRows} from '@/api/icode/dataset';
import {apiGetDraftDatasetTable} from '@/api/icode/datasetFileEdit';

interface Params {
    filePath: string;
    oid: string;
    page: number;
    pageSize: number;
    draftSessionId?: string;
    searchKeyword?: string;
    isDraft?: boolean;
    dataset: string;
    offset?: number;
    limit?: number;
}

// 能复用的我都复用了，这个改不动了
export const usePreviewData = ({
    filePath, oid, page, pageSize, offset, limit, searchKeyword = '', isDraft = false, dataset, draftSessionId,
}: Params) => {
    const currentRepoName = useCurrentRepoName();

    // 搜索结果
    const [
        fetchMatchedTableRows,
        {data: searchTableData, pending: searchPending, error: searchError},
    ] = useRequestCallback(
        apiGetMatchedDatasetTableRows,
        {
            dataSet: dataset ?? currentRepoName,
            filePath,
            branch: 'master',
            query: searchKeyword,
            oid,
        }
    );

    // 预览内容
    const [
        fetchDatasetTable,
        {data: browseTableData, pending: browsePending, error: browseError},
    ] = useRequestCallback(
        apiGetDatasetTable,
        {
            dataSet: dataset ?? currentRepoName,
            filePath,
            pageSize,
            pageNum: page,
            oid,
        }
    );

    // 草稿内容
    const [
        fetchDraftTable,
        {data: draftTableData, pending: draftPending, error: draftError},
    ] = useRequestCallback(
        apiGetDraftDatasetTable,
        {
            dataset: dataset ?? currentRepoName,
            path: filePath,
            oid,
            query: searchKeyword,
            sessionId: draftSessionId,
            offset,
            limit,
        }
    );

    const {loading, data, error} = useMemo(
        () => {
            if (isDraft) {
                return {
                    loading: draftPending,
                    data: draftTableData,
                    error: draftError,
                };
            }
            else {
                if (searchKeyword) {
                    return {
                        loading: searchPending,
                        data: searchTableData,
                        error: searchError,
                    };
                }
                return {
                    loading: browsePending,
                    data: browseTableData,
                    error: browseError,
                };
            }
        },
        [
            browsePending,
            browseTableData,
            draftPending,
            draftTableData,
            isDraft,
            searchKeyword,
            searchPending,
            searchTableData,
            draftError,
            browseError,
            searchError,
        ]
    );

    useEffect(
        () => {
            if (!oid) {
                return;
            }

            if (isDraft) {
                return;
            }

            if (filePath) {
                if (searchKeyword) {
                    fetchMatchedTableRows();
                }
                else {
                    fetchDatasetTable();
                }
            }
        },
        [filePath, oid, isDraft, searchKeyword, fetchDatasetTable, fetchMatchedTableRows]
    );

    useEffect(
        () => {
            if (!oid) {
                return;
            }

            if (filePath && isDraft) {
                fetchDraftTable();
            }
        },
        [oid, filePath, isDraft, fetchDraftTable]
    );

    return {fetchDatasetTable, loading, data, error};
};
