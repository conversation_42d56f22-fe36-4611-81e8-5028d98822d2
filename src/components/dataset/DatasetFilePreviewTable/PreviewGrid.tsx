/* eslint-disable max-lines */
/* eslint-disable complexity */
import styled from '@emotion/styled';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Button, message} from '@panda-design/components';
import {Empty} from 'antd';
import AGGrid from '@/components/AGGrid';
import {IconRefresh} from '@/icons-icode/actions';
import {apiPostRebuildDataset} from '@/api/icode/dataset';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {useOffsetAndLimit, usePageAndPageSize} from '@/hooks/icode/paging';
import {enableAGGrid} from '@/flags/temporary';
import {AGColDef} from '@/types/agGrid/agGrid';
import EmptySvg from '@/assets/icode/empty/emptyData.svg';
import {resetFileTotalRows, setFileTotalRows, useFileTotalRows} from '@/regions/icode/dataset/fileTotalRowsRegion';
import {MAX_ROW_COUNT} from '@/constants/comatestack/agGrid';
import {transformRowsType} from '@/utils/dataset/datasetFormatRow';
import {useFileRenderProperties} from '@/regions/icode/dataset/fileRenderPropertiesRegion';
import {useFileEdit} from '@/hooks/dataset/useFileEdit';
import {usePreviewData} from './usePreviewData';
import {getColumns} from './getColumns';
import SearchTable from './SearchTable';
import BrowseTable from './BrowseTable';
import {usePreviewColumns} from './usePreviewColumns';

const emptyStyle = {height: 220, margin: '12px auto 16px'};

const EmptyWrapper = styled.div`
    height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .ant-5-empty-image {
        height: 180px;
    }
`;

const GridContainer = styled.div`
    height: 100%;
    margin-top: 10px;
`;

// 支持解析的文件类型
const SUPPORTED_PREVIEW_FILE_TYPES = ['json', 'jsonl', 'csv', 'xlsx'];

interface Props {
    path: string;
    oid: string;
    searchKeyword?: string;
    isDraft?: boolean;
    repoName?: string;
    version?: string;
    editable?: boolean;
    draftSessionId?: string;
}

const PreviewGrid = ({
    path,
    oid,
    searchKeyword = '',
    isDraft = false,
    repoName,
    version = '',
    editable = false,
    draftSessionId = '',
}: Props) => {
    const currentRepoName = useCurrentRepoName();
    const dataset = repoName || currentRepoName;
    const {page, pageSize, onChange} = usePageAndPageSize();
    const {offset, limit, onChangeOffsetLimit} = useOffsetAndLimit();
    const [columns, setColumns] = useState<AGColDef[]>([]);
    const {fetchDatasetTable, loading, data, error} = usePreviewData({
        dataset, filePath: path, oid, page, pageSize, offset, limit, searchKeyword, isDraft, draftSessionId,
    });

    const totalRows = useFileTotalRows({
        path,
        oid,
    });

    const renderProperties = useFileRenderProperties({
        dataset,
        path,
        oid,
        uuid: isDraft ? draftSessionId : version,
    });

    const gridRef = useRef(null);
    const gridId = path + version;

    useEffect(
        () => {
            if (data?.totalRows) {
                setFileTotalRows({
                    path,
                    oid,
                }, data?.totalRows ?? 0);
            }
        },
        [data, path, oid]
    );

    useEffect(
        () => {
            resetFileTotalRows({
                path,
                oid,
            });
        },
        [path, oid]
    );

    useEffect(
        () => {
            if (!editable) {
                gridRef.current?.api?.stopEditing();
            }
        },
        [editable]
    );

    // searchKeyword 变化时，则重置到第1页
    useEffect(
        () => {
            onChange(1, pageSize);
        },
        [onChange, pageSize, searchKeyword]
    );

    const antTableColumns = usePreviewColumns({data, searchKeyword});

    // 只在第一页时渲染列名，避免重复渲染
    useEffect(
        () => {
            if (data?.features?.length > 0 && page === 1) {
                setColumns(getColumns({data}));
            }
        },
        [data, page]
    );

    // 自动滚动到顶部并收起菜单
    useEffect(
        () => {
            const element = document.getElementById(gridId);
            if (element) {
                element.scrollIntoView({behavior: 'smooth'});
            }
        },
        [gridId]
    );

    // 判断文件是否支持解析
    const isFileTypeSupported = useMemo(
        () => {
            // 当没有扩展名时，暂判断为是临时文件，支持预览
            const pathSplits = path.split('.');
            if (pathSplits.length === 1) {
                return true;
            }

            const fileExtension = pathSplits.pop()?.toLowerCase();
            return fileExtension ? SUPPORTED_PREVIEW_FILE_TYPES.includes(fileExtension) : false;
        },
        [path]
    );

    const handleRebuild = useCallback(
        async () => {
            try {
                message.info('正在解析中，请稍候...');
                // 可以直接通过dataset和文件信息拼接出重建的uuid参数
                await apiPostRebuildDataset([dataset + '.-@-.' + path + '.-@-.' + oid]);
                setTimeout(() => {
                    fetchDatasetTable();
                }, 3000);
            }
            catch (e) {
                message.error('发起失败，请重试');
            }
        },
        [fetchDatasetTable, dataset, path, oid]
    );

    const {
        handleUpdateRenderProperties,
        handleCellValueChanged,
        handleColumnMoved,
        handleRowAdded,
        handleColAdded,
        handleRowRemoved,
        handleColRemoved,
        handleSaveHeaderName,
    } = useFileEdit({
        gridRef,
        dataset,
        path,
        oid,
        version: isDraft ? '' : version,
        draftSessionId: isDraft ? draftSessionId : '',
    });

    if (!isFileTypeSupported) {
        return (
            <EmptyWrapper>
                <Empty
                    image={EmptySvg}
                    style={emptyStyle}
                    description="当前格式的文件暂不支持表格预览，表格预览仅支持 JSON、JSONL、CSV 和 Excel 等格式"
                />
            </EmptyWrapper>
        );
    }

    if (error && (error as any)?.status === 'NOT_FOUND') {
        if (isDraft) {
            return (
                <EmptyWrapper>
                    <Empty image={EmptySvg} style={emptyStyle} description="暂无草稿内容" />
                </EmptyWrapper>
            );
        }

        return (
            <EmptyWrapper>
                <Empty image={EmptySvg} style={emptyStyle} description="文件解析失败" />
                <Button icon={<IconRefresh />} type="default" onClick={handleRebuild}>重新解析</Button>
            </EmptyWrapper>
        );
    }

    // 数据小于等于 MAX_ROW_COUNT 条数据时，使用新版表格
    if (enableAGGrid && totalRows <= MAX_ROW_COUNT) {
        return (
            <GridContainer>
                <AGGrid
                    gridRef={gridRef}
                    gridId={gridId}
                    rowIdName="__SYSTEM_INTERNAL_ROW_ID"
                    loading={loading}
                    rowData={transformRowsType(data?.rows ?? [])}
                    columns={columns}
                    pagination={{
                        pageNumber: page,
                        pageSize: pageSize,
                        totalRows: data?.totalRows,
                        pageChangeMode: isDraft ? 'offset' : 'page',
                        offset,
                        limit,
                        onPageChange: (pageNumber, pageSize) => {
                            onChange(pageNumber, pageSize);
                        },
                        onOffsetChange: (offset, limit) => {
                            onChangeOffsetLimit(offset, limit);
                        },
                    }}
                    editable={editable}
                    renderProperties={renderProperties}
                    onUpdateRenderProperties={handleUpdateRenderProperties}
                    onCellValueChanged={handleCellValueChanged}
                    onColumnMoved={handleColumnMoved}
                    onRowAdded={handleRowAdded}
                    onColAdded={handleColAdded}
                    onRowRemoved={handleRowRemoved}
                    onColRemoved={handleColRemoved}
                    onSaveHeaderName={handleSaveHeaderName}
                />
            </GridContainer>
        );
    }
    else if (searchKeyword) {
        return (
            <SearchTable
                subset={path}
                data={data}
                columns={antTableColumns ?? []}
                loading={loading}
            />
        );
    }

    return (
        <BrowseTable
            subset={path}
            page={page}
            data={data}
            pageSize={pageSize}
            columns={antTableColumns ?? []}
            loading={loading}
            setPage={onChange}
        />
    );
};

export default PreviewGrid;
