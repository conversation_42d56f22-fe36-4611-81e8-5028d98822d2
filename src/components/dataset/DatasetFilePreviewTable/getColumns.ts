import {DatasetTableData} from '@/api/icode/dataset';
import {AGColDef} from '@/types/agGrid/agGrid';
import {HIDE_PREVIEW_COLUMNS} from './utils';

interface Params {
    data?: DatasetTableData;
}

export const getColumns = ({data}: Params) => {
    return (data?.features ?? []).filter(item => !HIDE_PREVIEW_COLUMNS.includes(item.name)).map(item => {
        const {name, dtype} = item;
        return {
            headerName: name,
            field: name,
            columnType: dtype,
        } as AGColDef;
    });
};
