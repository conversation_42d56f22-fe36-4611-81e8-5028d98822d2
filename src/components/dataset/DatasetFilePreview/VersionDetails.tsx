/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Divider, Input, Space, Tabs, Tooltip} from 'antd';
import {useCallback, useMemo, useState} from 'react';
import {css} from '@emotion/css';
import {Button, marginRight, message, Modal, Text} from '@panda-design/components';
import {DeleteOutlined, SearchOutlined} from '@ant-design/icons';
import {bigFileSizeTransform} from '@baidu/devops-components/es/DatasetFileTreeSelect/bigFileSizeTransform';
import {IconCreationDate} from '@/icons-icode/dataset';
import {resetFileEditDraft} from '@/regions/icode/dataset/fileEditCommonRegion';
import {apiPostDeleteDatasetFileDraft} from '@/api/icode/datasetFileEdit';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {loadFileEditStatus, useFileEditStatus} from '@/regions/icode/dataset/fileEditStatusRegion';
import {FileNode} from '@/types/icode/repo';
import {PublishedVersion} from '@/types/icode/dataset';
import {AnyEvent} from '@/types/icode/common';
import FileDownloadButton from '@/components/icode/DatasetFileOperation/FileDownloadButton';
import {apiPostRollbackVersion} from '@/api/icode/dataset';
import {useFileTotalRows} from '@/regions/icode/dataset/fileTotalRowsRegion';
import PreviewGrid from '../DatasetFilePreviewTable/PreviewGrid';
import FilePointerContent from './FilePointerContent';

const DetailsHeader = styled.div`
    display: flex;
    flex-direction:column;
    justify-content: center;
    border-bottom: 1px solid #e8e8e8;
    position: relative;
    height: 47px;
`;

const textCss = css`
    font-size: 12px !important;
    color: gray;
`;

const descCss = css`
    font-size: 12px !important;
    color: gray !important;
    max-width: 200px !important;
`;

const tabClass = css`
    flex: 1;
    overflow: hidden;
`;

const TimeContainer = styled.div`
    width: 70px;
    display: flex;
    align-items: center;
    gap: 5px;
`;

const ButtonContainer = styled(Space)`
    position: absolute;
    top: 8px;
    right: 10px;
    gap: 10px;
`;

interface Props {
    publishedVersion: PublishedVersion;
    file: FileNode;
    latestVersion: string;
    onVersionChange?: () => Promise<void>;
}

// eslint-disable-next-line complexity
export const VersionDetails = ({publishedVersion, file, latestVersion, onVersionChange}: Props) => {
    const {path, oid, description, commitId, version, createdAt, isDraft, size} = publishedVersion ?? {};
    const {commitDate, comment} = file ?? {};
    const [activeKey, setActiveKey] = useState('filePreview');
    const [searchKeyword, setSearchKeyword] = useState('');
    const repo = useCurrentRepoName();
    const {draftSessionId, enableEdit} = useFileEditStatus(path);
    const isVersionV0 = version === 'V0';
    const currentDescription = isVersionV0 ? comment : description;
    const totalRows = useFileTotalRows({path, oid});

    const formatTimeAgo = useCallback(
        (date: string) => {
            if (!date) {
                return '暂无时间';
            }
            const now = new Date();
            const targetDate = new Date(date);
            const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);

            if (diffInSeconds < 60) {
                return '刚刚';
            }
            if (diffInSeconds < 3600) {
                return `${Math.floor(diffInSeconds / 60)}分钟前`;
            }
            if (diffInSeconds < 86400) {
                return `${Math.floor(diffInSeconds / 3600)}小时前`;
            }
            if (diffInSeconds < 2592000) {
                return `${Math.floor(diffInSeconds / 86400)}天前`;
            }
            if (diffInSeconds < 31536000) {
                return `${Math.floor(diffInSeconds / 2592000)}个月前`;
            }
            return `${Math.floor(diffInSeconds / 31536000)}年前`;
        },
        []
    );

    const handleSearchChange = useCallback(
        (e: AnyEvent) => {
            const value = e.target.value;
            setSearchKeyword(value);
        },
        [setSearchKeyword]
    );

    const handleDeleteDraft = useCallback(
        () => {
            Modal.confirm({
                content: (
                    <>
                        <Text>草稿删除后无法恢复，请谨慎操作。</Text>
                        <p>
                            <Text>确定删除草稿吗？</Text>
                        </p>
                    </>
                ),
                async onOk() {
                    const params = {
                        dataset: repo,
                        path,
                        oid,
                        timestamp: Date.now(),
                        sessionId: draftSessionId,
                    };

                    try {
                        await apiPostDeleteDatasetFileDraft(params);
                        // 删除完草稿后，更新一下最新的草稿状态
                        setTimeout(async () => {
                            loadFileEditStatus({
                                dataset: repo,
                                path,
                                oid,
                            });
                        }, 500);

                        resetFileEditDraft(path);
                        message.success('草稿删除成功');
                    }
                    catch (e) {
                        console.error(e);
                        message.error('草稿删除失败');
                    }
                },
            });
        },
        [draftSessionId, oid, path, repo]
    );

    const handleRollback = useCallback(
        async () => {
            Modal.confirm({
                title: '回滚确认',
                content: (
                    <>
                        <Text>此操作将以{version}文件内容生成新版本，确认回滚？</Text>
                    </>
                ),
                async onOk() {
                    try {
                        const params = {
                            datasetRepo: repo,
                            branch: 'master', // 使用默认分支
                            rollbackVersion: version,
                            filePath: path,
                        };
                        const success = await apiPostRollbackVersion(params);
                        if (success) {
                            message.success('回滚成功');
                            // 通知父组件更新状态
                            await onVersionChange?.();
                        }
                        else {
                            message.error('回滚失败');
                        }
                    }
                    catch (e) {
                        message.error('回滚失败');
                    }
                },
            });
        },
        [repo, version, path, onVersionChange]
    );

    const tabItems = useMemo(
        () => [
            {
                key: 'filePreview',
                label: '文件预览',
                children: <PreviewGrid
                    path={path}
                    oid={oid}
                    searchKeyword={searchKeyword}
                    isDraft={isDraft}
                    version={version}
                />,
            },
            {
                key: 'filePointer',
                label: '文件指针',
                children: <FilePointerContent filePath={path} commitRefName={commitId} />,
            },
        ],
        [path, oid, searchKeyword, isDraft, version, commitId]
    );

    return (
        <>
            <DetailsHeader>
                <Space size="middle" className={textCss}>
                    <TimeContainer>
                        <IconCreationDate />
                        <Tooltip title={`${isDraft ? '更新时间' : '发布时间'}: ${isVersionV0 ? commitDate : createdAt}`}>
                            {formatTimeAgo(isVersionV0 ? commitDate : createdAt)}
                        </Tooltip>
                    </TimeContainer>
                    {
                        !isDraft && (
                            <Text
                                ellipsis={{
                                    tooltip: currentDescription,
                                }}
                                className={descCss}
                            >
                                {currentDescription}
                            </Text>
                        )
                    }
                    <Divider type="vertical" style={{margin: '0px'}} />
                    <div>
                        {size && bigFileSizeTransform(size)}
                        {size && totalRows && ' / '}
                        {totalRows && `${totalRows} 行`}
                    </div>
                    <ButtonContainer>
                        {publishedVersion && isDraft !== true && (
                            <>
                                <FileDownloadButton oid={oid} path={path} name={path.split('/').pop() || ''} />
                                <Divider type="vertical" style={{marginRight: '10px'}} />
                            </>
                        )}
                        {isDraft && enableEdit && (
                            <>
                                <Button
                                    className={marginRight(-10)}
                                    type="link"
                                    style={{color: 'black'}}
                                    icon={<DeleteOutlined />}
                                    onClick={handleDeleteDraft}
                                >
                                    删除
                                </Button>
                                <Divider type="vertical" style={{marginRight: '10px'}} />
                            </>
                        )}
                        {/* 回滚按钮：仅在非草稿、非最新版本、非最旧版本 时才显示 */}
                        {!isDraft && version !== 'V0' && version !== latestVersion && (
                            <Button onClick={handleRollback} gradient>回滚到此版本</Button>
                        )}
                    </ButtonContainer>
                </Space>
            </DetailsHeader>
            <Tabs
                className={tabClass}
                activeKey={activeKey}
                items={tabItems}
                onChange={setActiveKey}
                style={{paddingRight: '10px'}}
                tabBarExtraContent={activeKey === 'filePreview' && (
                    <Input
                        size="small"
                        prefix={<SearchOutlined />}
                        allowClear
                        disabled={!path}
                        placeholder="关键字搜索"
                        style={{width: 240, marginTop: '15px'}}
                        value={searchKeyword}
                        onChange={handleSearchChange}
                    />
                )}
            />
        </>
    );
};
