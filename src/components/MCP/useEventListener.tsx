/**
 * created by zulu
 */
import {useRef} from 'react';

/**
 * 事件发布订阅机制
 *
 * 使用示例：
 *
 * // 在组件中使用
 * const { publish, subscribe } = useMCPEditFormValidationInteraction();
 *
 * // 订阅事件
 * useEffect(() => {
 *   const subscription = subscribe('validationError', (payload) => {
 *     console.log('收到验证错误:', payload);
 *   });
 *
 *   return () => subscription.unsubscribe();
 * }, []);
 *
 * // 发布事件
 * publish('validationError', { field: 'username', message: '不能为空' });
 */
interface EventSubscription {
  unsubscribe: () => void;
}

export const useEventListener = () => {
    // 存储所有订阅者的引用
    const eventListeners = useRef<Record<string, Set<(payload?: any) => void>>>({});

    /**
   * 发布事件
   * @param eventType 事件类型
   * @param payload 事件数据
   */
    const publish = (eventType: string, payload?: any) => {
        const listeners = eventListeners.current[eventType];
        if (listeners) {
            listeners.forEach(listener => listener(payload));
        }
    };

    /**
   * 订阅事件
   * @param eventType 事件类型
   * @param callback 回调函数
   * @returns 取消订阅的方法
   *
   * 注意：需要在组件中使用useEffect来管理订阅的生命周期
   * 示例：
   * useEffect(() => {
   *   const subscription = subscribe('eventType', callback);
   *   return () => subscription.unsubscribe();
   * }, []);
   */
    const subscribe = (eventType: string, callback: (payload?: any) => void): EventSubscription => {
        if (!eventListeners.current[eventType]) {
            eventListeners.current[eventType] = new Set();
        }

        eventListeners.current[eventType].add(callback);

        return {
            unsubscribe: () => {
                eventListeners.current[eventType]?.delete(callback);
            },
        };
    };

    return {publish, subscribe};
};
