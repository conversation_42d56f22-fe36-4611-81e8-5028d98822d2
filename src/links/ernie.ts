/* eslint-disable max-len */
import {createLink, createErnieLink} from './createLink';

interface ParamsErnieLink {
    projectUuid: string;
    ernieProjectId?: number;
}

export const ErnieManageEntranceLink = createErnieLink<ParamsErnieLink>('/manage/projectList?comateStackProject={projectUuid}');

export const ErnieLabelEntranceLink = createErnieLink<ParamsErnieLink>('/mark/markList?comateStackProject={projectUuid}');

export const ErnieManageProjectLink = createErnieLink<ParamsErnieLink>('/manage/dataManage?projectId={ernieProjectId}&comateStackProject={projectUuid}');

export const ErnieManageDocLink = createLink('https://ailabel.baidu.com/doc/manage/gettingStarted/');

export const ErnieLabelDocLink = createLink('https://ailabel.baidu.com/doc/mark/gettingStarted/Mark/');
