import {createLink, createLinkNoIcon} from './createLink';

// 上车单关联iCafe卡片操作文档
export const DatasetRelateICafeCardLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/mcV1ZM02Cz/N30UarFGJOu7b8');

// iCode值班表
export const DutyRosterLink = createLink('http://zhiban.baidu.com/duty/get?id=5d3670ea7eaeb018eed2dfe4');

// iDataset值班表
export const DutyRosterDatasetLink = createLink('https://zhiban.baidu-int.com/detail?id=71739');

// Comate Stack值班表
export const DutyRosterComateStackLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/B8wSneaLSC/j5MDEU-tLPI7NY');

// 文心一言-厂内业务iCafe空间
export const ICafeYiyanLink = createLink('https://console.cloud.baidu-int.com/devops/icafe/space/aiip-req/planbox/935483/issue?viewId=138905');

// 千帆用户部署模型服务调用
export const QianfanDeployModelLink = createLink('https://cloud.baidu.com/doc/WENXINWORKSHOP/s/0lrk4cwx8');

export const BannerLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/B8wSneaLSC/5rbIpJiCa1TpqJ');

interface ParamsQianFanPublishLink {
    qianfanJobId: string;
}

export const QianFanPublishLink = createLink<ParamsQianFanPublishLink>('https://console.bce.baidu.com/qianfan/train/sft/{qianfanJobId}/list');

export const QianFanPayLink = createLink('https://console.bce.baidu.com/qianfan/ais/console/onlineService?tab=service');

export const SecLevelHelpLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/BMaLudR2QF/vas6vAT7x_/DCHsBcHC3A1wrZ');

// 规则标注操作手册
export const RuleMarkHelpLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/B8wSneaLSC/MIBHWV2fKYoGdy');

export const LandingPageInstructionLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/t7n0qKWNJW/7urCLykvXO/RLwGLAp41-rnFG');

// 预标注操作文档
export const PreMarkHelpLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/B8wSneaLSC/ImLR0X5UtM2M5t');

// 徽章文档
export const MedalDocLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/B8wSneaLSC/v5yJMHFG1LerYR');

// 视频标注指南
export const VideoAnnotationGuideLink = createLinkNoIcon('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/z1TN_5BM97/sSl6WYud8-RGi0');

// 标注模版低代码标注手册
export const LowCodeLabelTemplateManualLink = createLinkNoIcon('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/z1TN_5BM97/Rm4Nwgo9lgVIWo');

// 多模标注说明文档
export const MultimodeGuideLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/z1TN_5BM97/hiTwhP7zYzzVSS');

// 修改标注模版手册
export const UpdataLabelTemplateManualLink = createLinkNoIcon('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/z1TN_5BM97/Wq1181K8maywxY');

// 任务分配使用手册
export const AssignTaskManualLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/z1TN_5BM97/uByj-MwRbQDRHP');

// 数据回流使用手册
export const DataReflowManualLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/z1TN_5BM97/PMTajRpvLkXRVD');
