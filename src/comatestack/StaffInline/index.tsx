import styled from '@emotion/styled';
import {Flex} from 'antd';
import {CloseOutlined} from '@ant-design/icons';
import {useCallback, useEffect, useRef, useState} from 'react';
import Draggable from 'react-draggable';
import {StaffTypeProvider} from '@/components/Chat/Provider/StaffTypeProvider';
import StaffAvatar from '@/components/Chat/StaffAvatar';
import {useCurrentChat} from '@/regions/staff/chat';
import {staffColors} from '@/constants/colors/staff';
import {
    setCurrentChatOpen,
    useCurrentChatOpen,
} from '@/regions/staff/chatSdk';
import {useNotificationWebsocketEffect} from '@/components/Chat/hooks';
import {loadAgentList} from '@/regions/staff/agent';
import {loadCurrentUser} from '@/regions/user/currentUser';
import background from '@/assets/staff/staffInlineBackground.png';
import ChatPanel from './ChatPanel';
import NotificationBubbles from './BubblePanel';

const Container = styled.div`
    position: fixed;
    right: 16px;
    border-radius: 12px;
    z-index: 9999;
`;

const AvatarContainer = styled.div`
    position: fixed;
    right: 16px;
    bottom: 78px;
    border-radius: 12px;
    z-index: 9999;
    cursor: move;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
`;

const AvatarBackground = styled(Flex)`
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: url(${background}) no-repeat center center/contain;
`;

const Block = styled(Flex)`
    width: 40px;
    height: 40px;
    border-radius: 38px;
    background: ${staffColors.gradientBg};
`;

export const StaffInline = () => {
    const [containerBottom, setContainerBottom] = useState(80);
    const isDraggingRef = useRef(false);
    const ref = useRef(null);
    const open = useCurrentChatOpen();
    const {agentName = '数字员工', agentId} = useCurrentChat();
    useNotificationWebsocketEffect();
    useEffect(
        () => {
            Promise.all([loadCurrentUser(), loadAgentList()]);
        },
        []
    );

    const onDragStart = () => {
        isDraggingRef.current = true;
    };

    const onDragEnd = () => {
        const avatarBottom = ref?.current?.getBoundingClientRect()?.bottom || 0;
        const bottomNum = Number(window.innerHeight - avatarBottom + 55);
        setContainerBottom(bottomNum);
        setTimeout(() => {
            isDraggingRef.current = false;
        }, 0);
    };

    const handleClick = useCallback(
        () => {
            if (!isDraggingRef.current) {
                setCurrentChatOpen(v => !v);
            }
        },
        []
    );

    return (
        <StaffTypeProvider type="inline">
            <Container
                id="staffWindowContainer"
                style={{bottom: containerBottom}}
            >
                {open ? <ChatPanel /> : <NotificationBubbles />}
            </Container>
            <Draggable
                onStop={onDragEnd}
                onDrag={onDragStart}
                axis="y"
                bounds={{top: -90, bottom: 50}}
            >
                <AvatarContainer ref={ref} onClick={handleClick}>
                    {open ? (
                        <Block align="center" justify="center">
                            <CloseOutlined />
                        </Block>
                    ) : (
                        <AvatarBackground align="center" justify="center">
                            <StaffAvatar
                                agentId={agentId}
                                username={agentName}
                                iconSize={40}
                                isLogo={!agentId}
                            />
                        </AvatarBackground>

                    )}
                </AvatarContainer>
            </Draggable>
        </StaffTypeProvider>
    );
};
