import {Menu} from 'antd';
import {useSearchParams, useSearchParamsUpdate} from '@panda-design/router';
import styled from '@emotion/styled';
import {ResizeLayout} from '@/design/ResizeLayout';
import {MenuContainer} from '@/design/Menu';
import {documentMenuItems} from '@/label-view/document/documentMenuItems';
import {getNodeDocument} from '@/label-view/document/getNodeDocument';
import {APP_HEADER_HEIGHT} from '@/constants/app';

const DocumentContainer = styled.div`
    height: calc(100vh - ${APP_HEADER_HEIGHT});
    overflow-y: auto;
    padding: 0 20px 20px 20px;
`;

const LabelDocument = () => {
    const {name = 'Introduction'} = useSearchParams();
    const replace = useSearchParamsUpdate();
    const Document = getNodeDocument(name);
    return (
        <ResizeLayout
            left={(
                <MenuContainer title="标注组件文档">
                    <Menu
                        inlineIndent={12}
                        selectedKeys={[name]}
                        onClick={({key}) => replace({name: key})}
                        mode="inline"
                        items={documentMenuItems}
                    />
                </MenuContainer>
            )}
            leftProps={{defaultSize: 15}}
            right={
                <DocumentContainer>
                    <Document />
                </DocumentContainer>
            }
        />
    );
};

export default LabelDocument;
