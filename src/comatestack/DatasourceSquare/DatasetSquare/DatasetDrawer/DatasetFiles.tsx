import styled from '@emotion/styled';
import {marginLeft} from '@panda-design/components';
import {useEffect, useState} from 'react';
import {DatasetInfo} from '@/types/comatestack/datasetSquare';
import {ResizeLayout} from '@/design/ResizeLayout';
import {useFileNode} from '@/regions/icode/fileTree';
import PreviewGrid from '@/components/dataset/DatasetFilePreviewTable/PreviewGrid';
import {useDatasetBranch} from '@/regions/home/<USER>';
import {PageEmpty} from '@/design/PandaEmpty/PageEmpty';
import {useBranchType} from './useBranchType';
import {DatasetFileTree} from './DatasetFileTree';

const RightContainer = styled.div`
    height: 70vh;
    .ant-5-table-wrapper {
        margin: 0;
`;

const EmptyWrapper = styled.div`
    display: flex;
    height: 300px;
`;

interface Props {
    dataset: DatasetInfo;
}

export const DatasetFiles = ({dataset}: Props) => {
    const refName = useDatasetBranch(dataset?.repoName) ?? 'master';
    const {repoName} = dataset ?? {};
    const fileNode = useFileNode({repo: repoName, commit: refName, path: ''});
    const [selectedFileNode, setSelectedFileNode] = useState(null);
    const {lfsOid, path, type} = selectedFileNode ?? {};
    const showEmpty = type === 'TREE' || !selectedFileNode;
    const branchType = useBranchType(repoName, refName);

    useEffect(
        () => {
            setSelectedFileNode(null);
        },
        [repoName]
    );

    return (
        <ResizeLayout
            left={(
                <DatasetFileTree
                    repoName={repoName}
                    refName={refName}
                    branchType={branchType}
                    fileNode={fileNode}
                    selectedFileNode={selectedFileNode}
                    onSelect={setSelectedFileNode}
                />
            )}
            right={(
                <RightContainer>
                    {showEmpty ? (
                        <EmptyWrapper>
                            <PageEmpty tip="暂无数据" />
                        </EmptyWrapper>
                    ) : (
                        <PreviewGrid key={path} path={path ?? ''} oid={lfsOid ?? ''} repoName={repoName} />
                    )}
                </RightContainer>
            )}
            leftProps={{defaultSize: 25}}
            rightProps={{className: marginLeft(16)}}
        />
    );
};
