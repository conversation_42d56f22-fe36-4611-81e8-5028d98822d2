import styled from '@emotion/styled';
import {FormProvider} from '@panda-design/path-form';
import {useToggle} from 'huse';
import {useCurrentUsername} from '@/regions/user/currentUser';
import {CreateProjectFields} from '@/components/comatestack/Modals/CreateProject/CreateProjectFields';
import FormTaskContent from '@/components/comatestack/Reflow/FormTaskContent';
import ReflowScenario from '@/components/comatestack/Reflow/ReflowScenario';
import FormDataSettings from '@/components/comatestack/Reflow/FormDataSettings';
import Footer from './Footer';

const Layout = styled.div`
    max-width: 1200px;
    min-width: 1200px;
    margin: 0 auto;
    padding: 20px;
`;

export function CreateReflowForm() {
    const [_, toggleHiddenAdvancedParams] = useToggle(true);
    const currentUsername = useCurrentUsername();

    return (
        <Layout>
            <FormProvider initialValues={() => (
                {
                    taskArchiveTime: 'WEEK',
                    taskFileName: 'data_flywheel',
                    taskTargetBranch: 'master',
                    visibleType: 'PRIVATE',
                    secLevel: 'L3',
                    createWorkspace: true,
                    taskTransferEncryption: false,
                    admins: [{name: currentUsername, type: 'USER'}],
                    taskTransferTime: ['HOUR', 6],
                    taskTargetPath: 'flywheel',
                    dataSourceType: 'DEFAULT',
                    fileSuffix: 'jsonl',
                }
            )}
            >
                <ReflowScenario />
                <CreateProjectFields />
                <FormDataSettings />
                <FormTaskContent
                    toggleHiddenAdvancedParams={toggleHiddenAdvancedParams}
                    disabledValue={false}
                />
                <Footer />
            </FormProvider>
        </Layout>
    );
}

