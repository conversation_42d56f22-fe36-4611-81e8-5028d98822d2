import styled from '@emotion/styled';
import {useCallback} from 'react';
import {Button, message} from '@panda-design/components';
import {useFormSubmit, useFormSubmitting} from '@panda-design/path-form';
import {useNavigate} from 'react-router-dom';
import {ReflowLink} from '@/links/comatestack';
import {apiPostCreateReflowProject} from '@/api/datareflow';
import {apiPutCreateProject} from '@/api/project';
import {ProjectCreate} from '@/types/comatestack/project';
import {SubmitPropsType} from '@/types/comatestack/dataReflowInit';


const Wrapper = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
`;


function Footer() {
    const loading = useFormSubmitting();
    const navigate = useNavigate();
    const handleOk = useCallback(
        async (values: SubmitPropsType) => {
            try {
                const {
                    workspaceUuid,
                    accountId,
                    name,
                    uuid,
                    projectDesc,
                    secLevel,
                    admins,
                    reflowScenario,
                    visibleType,
                    createWorkspace,
                } = values;

                const paramsProject: ProjectCreate = {
                    workspaceUuid,
                    name,
                    uuid,
                    description: projectDesc,
                    secLevel,
                    accountId,
                    admins,
                    visibleType,
                    createWorkspace,
                };

                const newProject = await apiPutCreateProject(paramsProject);
                const {workspaceUuid: currWorkspaceUuid} = newProject;

                const {
                    clientId,
                    taskName,
                    taskFileName,
                    taskTransferTime,
                    taskTargetPath,
                    taskArchiveTime,
                    taskTargetBranch,
                    taskTransferEncryption,
                    transferDesensitizations,
                    dataSourceType,
                    dataPathList,
                    accessKey,
                    secretKey,
                    remoteAddr,
                    taskDelayTime,
                    fileSuffix,
                } = values;

                const params = {
                    flywheelProjectName: reflowScenario,
                    stackSpace: currWorkspaceUuid,
                    stackProject: uuid,
                    clientId: clientId || uuid,
                    taskName,
                    taskFileName,
                    taskTransferTime: taskTransferTime?.join(''),
                    taskTargetPath,
                    taskArchiveTime,
                    taskTargetBranch,
                    taskTransferEncryption,
                    transferDesensitizations,
                    dataSourceType,
                    dataPathList,
                    accessKey,
                    secretKey,
                    remoteAddr,
                    taskDelayTime,
                    fileSuffix,
                };

                await apiPostCreateReflowProject(params);
                navigate(ReflowLink.toUrl());
                message.success('新建成功');
            }
            catch (error) {
                message.error(error.message);
            }
        },
        [navigate]
    );

    const handleValidateAndSubmit = useFormSubmit(
        handleOk,
        () => {
            message.error('请检查表单配置');
        }
    );

    return (
        <Wrapper>
            <Button
                type="primary"
                onClick={handleValidateAndSubmit}
                loading={loading}
            >
                新建
            </Button>
            <Button
                type="default"
                onClick={() => navigate(ReflowLink.toUrl())}
            >
                取消
            </Button>
        </Wrapper>
    );
}

export default Footer;
