/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {useEffect, useMemo} from 'react';
import {Divider, Space, Typography} from 'antd';
import {css} from '@emotion/css';
import {colors} from '@/constants/colors';
import {PageLayout, PageLayoutHeader} from '@/design/Layouts/PageLayout';
import {loadDmProject, useDmProject} from '@/regions/projectLabelTask/dmProject';
import {labelTaskDetailAsyncEffect} from '@/regions/projectLabelTask/labelAsyncEffect';
import {useLabelParams} from '@/hooks/label/useLabelParams';
import {getLabelBatchAllTaskIds, loadLabelBatchTaskIds} from '@/regions/labelBatchTask/allTaskIds';
import {useBatchMarkTaskIndex} from '@/regions/labelBatchTask/batchMarkTaskIndex';
import {loadDmViews, useCurrentViewId} from '@/regions/projectLabelTask/dmViews';
import {useLabelBatchTaskSelectIds} from '@/hooks/label/useLabelBatchTaskParams';
import {loadLabelTaskTableData} from '@/regions/projectLabelTask/projectLabelTaskData';
import {useLabelTaskSelectedCondition} from '@/hooks/label/useGetLabelCondition';
import FilterConditionTooltip from '@/components/Label/FilterConditionTooltip';
import NoLabelPermissionView from '@/components/comatestack/Permission/NoLabelPermissionView';
import {LabelTaskDetailModal} from '@/components/Label/LabelTaskDetailModal';
import {usePermissions} from '@/hooks/permission/permission';
import {useCodeTemplate} from '@/hooks/label/useCodeTemplate';
import {useLabelTaskId} from '@/hooks/label/useLabelTaskId';
import {LabelViewProvider} from '@/label-view/providers/LabelViewProvider';
import {useCurrentLabelTaskDetail} from '@/regions/projectLabelTask/labelTaskDetail';
import {useCurrentProjectPermissionCodes} from '@/regions/members/permission';
import {APP_IS_EXTERNAL} from '@/constants/app';
import {loadReviewResultItem, useIsShowReviewTag} from '@/regions/projectLabelTask/LabelTaskReview';
import UserAvatar from '../LabelProject/UserAvatar';
import {CheckButton} from '../LabelProject/CheckButton';
import {TaskAssign} from '../LabelProject/TaskAssign';
import TopActions from './TopActions';
import {useHandleBack} from './useHandleBack';
import LabelTaskResizeLayout from './LabelTaskResizeLayout';

const ContentContainer = styled.div`
    border: 1px solid ${colors['gray-4']};
    border-radius: 10px;
    overflow: hidden;
    height: calc(100vh - ${APP_IS_EXTERNAL ? '80px' : '138px'});
    display: flex;
    flex-direction: column;
`;

const NoPermissionContainer = styled.div`
    margin: 50px auto;
`;

const headerClassName = css`
    position: absolute;
    right: 20px;
`;

function LabelNormalTask() {
    const {labelStudioProjectId} = useLabelParams();
    const currentIndex = useBatchMarkTaskIndex(labelStudioProjectId);
    const selectedTaskIds = useLabelBatchTaskSelectIds();
    const viewId = useCurrentViewId();
    const handleBack = useHandleBack();
    const {hasSelectedTasks, hasFilteredTasks} = useLabelTaskSelectedCondition();
    const hasViewLabelTask = usePermissions(['LABEL_TASK_READ']);
    const permissionCodes = useCurrentProjectPermissionCodes();
    const dmProject = useDmProject(labelStudioProjectId);
    const code = useCodeTemplate(labelStudioProjectId);
    const labelTaskDetail = useCurrentLabelTaskDetail();
    const data = labelTaskDetail?.data;
    const taskId = useLabelTaskId();
    const isShowReviewTag = useIsShowReviewTag(labelStudioProjectId);

    if (APP_IS_EXTERNAL && permissionCodes?.length === 0) {
        return (
            <>
                <PageLayoutHeader
                    className={headerClassName}
                    extra={<UserAvatar />}
                />
                <NoPermissionContainer>
                    <NoLabelPermissionView />
                </NoPermissionContainer>
            </>
        );
    }

    useEffect(
        () => {
            loadDmViews({project: labelStudioProjectId});
            loadDmProject({project: labelStudioProjectId});
            loadLabelTaskTableData(
                {
                    page: 1,
                    page_size: 10,
                    view: viewId,
                    project: labelStudioProjectId,
                }
            );
        },
        [labelStudioProjectId, viewId]
    );

    useEffect(
        () => {
            const asyncEffect = async () => {
                if (!selectedTaskIds) {
                    const allTaskIds = getLabelBatchAllTaskIds(labelStudioProjectId);
                    // 如果 index + 1 位置的 taskId 不存在，自动请求一页
                    const nextIndex = currentIndex + 1;
                    const nextTaskId = allTaskIds[nextIndex];
                    if (nextTaskId) {
                        loadLabelBatchTaskIds({
                            page: Math.floor(nextIndex / 10) + 1,
                            page_size: 10,
                            view: viewId,
                            project: labelStudioProjectId,
                        });
                    }
                    // 如果 taskId 不存在，则自动请求 taskId 的一页（跳的时候可能发生）
                    const currentTaskId = allTaskIds[currentIndex];
                    if (!currentTaskId) {
                        await loadLabelBatchTaskIds({
                            page: Math.floor(currentIndex / 10) + 1,
                            page_size: 10,
                            view: viewId,
                            project: labelStudioProjectId,
                        });
                    }
                }

                const allTaskIds = getLabelBatchAllTaskIds(labelStudioProjectId);

                const taskId = selectedTaskIds ? selectedTaskIds[currentIndex] : allTaskIds[currentIndex];

                // 有了当前的 taskId 之后，请求下面的详情
                labelTaskDetailAsyncEffect({taskId, labelStudioProjectId});
                taskId && loadReviewResultItem({taskId});
            };

            asyncEffect();
        },
        [labelStudioProjectId, currentIndex, viewId, selectedTaskIds]
    );

    const batchLabelText = useMemo(
        () => {
            const suffixText = hasSelectedTasks ? '勾选项' : (hasFilteredTasks ? '筛选项' : '');
            return (
                <Space size={2}>
                    <Typography.Title level={3} style={{maxWidth: '230px'}} ellipsis={{tooltip: true}}>
                        {dmProject?.title ?? '标注任务'}
                        {suffixText}
                        {
                            !hasSelectedTasks && hasFilteredTasks && <FilterConditionTooltip />
                        }
                    </Typography.Title>
                    <Divider type="vertical" />
                    <TaskAssign />
                    {isShowReviewTag && <CheckButton />}
                </Space>
            );
        }
        ,
        [hasSelectedTasks, hasFilteredTasks, dmProject?.title, isShowReviewTag]
    );

    return (
        <PageLayout>
            <PageLayoutHeader title={batchLabelText} onBack={handleBack} />
            {
                hasViewLabelTask ? (
                    <ContentContainer>
                        <LabelViewProvider
                            code={code}
                            originData={data}
                            key={taskId}
                        >
                            <TopActions />
                            <LabelTaskResizeLayout />
                        </LabelViewProvider>
                        <LabelTaskDetailModal />
                    </ContentContainer>
                ) : <NoLabelPermissionView />
            }
        </PageLayout>
    );
}

export default LabelNormalTask;
