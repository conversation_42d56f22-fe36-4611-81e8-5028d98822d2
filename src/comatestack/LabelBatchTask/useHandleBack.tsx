import {useNavigate, useParams} from 'react-router-dom';
import {useCallback} from 'react';
import {LabelProjectLink} from '@/links/label';
import {resetBatchMarkTaskIndex} from '@/regions/labelBatchTask/batchMarkTaskIndex';
import {useLabelParams} from '@/hooks/label/useLabelParams';
import {resetLabelMode} from '@/regions/label/labelTask';

export const useHandleBack = () => {
    const navigate = useNavigate();
    const {labelProjectId, labelStudioProjectId} = useLabelParams();
    const {projectUuid} = useParams();

    const handleBack = useCallback(
        () => {
            resetBatchMarkTaskIndex(labelStudioProjectId);
            resetLabelMode();
            navigate(
                LabelProjectLink.toUrl({
                    projectUuid,
                    labelProjectId,
                    labelStudioProjectId,
                })
            );
        },
        [labelProjectId, labelStudioProjectId, navigate, projectUuid]
    );
    return handleBack;
};
