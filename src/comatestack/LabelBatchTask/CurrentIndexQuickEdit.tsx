import {QuickEdit, QuickEditEditProps, width} from '@panda-design/components';
import {InputNumber} from 'antd';
import {css} from '@emotion/css';
import {setBatchMarkTaskIndex, useBatchMarkTaskIndex} from '@/regions/labelBatchTask/batchMarkTaskIndex';
import {useBatchTaskTotal} from '@/hooks/label/useLabelBatchTaskParams';
import {useLabelParams} from '@/hooks/label/useLabelParams';

function Edit({value, onChange, handleConfirm}: QuickEditEditProps<number>) {
    const total = useBatchTaskTotal();

    return (
        <>
            <InputNumber
                autoFocus
                size="small"
                className={width(50)}
                value={value}
                onChange={onChange}
                onPressEnter={handleConfirm}
                onBlur={handleConfirm}
                controls={false}
                max={total}
                min={1}
            />
            {`/${total}`}
        </>
    );
}

const displayCss = css`
    svg {
        display: none;
    }
`;

export const CurrentIndexQuickEdit = () => {
    const {labelStudioProjectId} = useLabelParams();
    const currentIndex = useBatchMarkTaskIndex(labelStudioProjectId);
    const total = useBatchTaskTotal();

    return (
        <QuickEdit<number>
            value={currentIndex + 1}
            displayClassName={displayCss}
            onConfirm={v => setBatchMarkTaskIndex(labelStudioProjectId, v - 1)}
            renderValue={value => `${value}/${total}`}
            Edit={Edit}
        />
    );
};
