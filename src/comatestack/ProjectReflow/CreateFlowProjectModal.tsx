import {FormProvider} from '@panda-design/path-form';
import {ResultProjectReflowTask} from '@/types/comatestack/dataReflow';
import {InitialValuesType} from '@/types/comatestack/dataReflowInit';
import {useCurrentProject} from '@/regions/project/project';
import {useCreateFlowProjectModalOpen} from './createFlowProject';
import {FormContent} from './FormContent';

interface Props {
    projectTaskLists: ResultProjectReflowTask[];
}

export const CreateFlowProjectModal = ({projectTaskLists}: Props) => {
    const open = useCreateFlowProjectModalOpen();
    const project = useCurrentProject();
    const {
        workspaceUuid,
        accountId,
        name,
        uuid,
        description,
        visibleType,
        secLevel,
        admins,
    } = project;

    const initialValues: InitialValuesType = {
        reflowScenario: (projectTaskLists && projectTaskLists.length > 0)
            ? projectTaskLists[0].flywheelProjectName || ''
            : '',
        workspaceUuid,
        accountId,
        name,
        uuid,
        description,
        visibleType,
        secLevel,
        createWorkspace: false,
        admins,
        taskTransferTime: ['HOUR', 6],
        taskTargetPath: 'flywheel',
        taskArchiveTime: 'WEEK',
        taskTargetBranch: 'master',
        taskFileName: 'data_flywheel',
        taskTransferEncryption: false,
        dataSourceType: 'DEFAULT',
        fileSuffix: 'jsonl',
    };

    if (!open) {
        return null;
    }

    return (
        <FormProvider initialValues={initialValues}>
            <FormContent />
        </FormProvider>
    );
};
