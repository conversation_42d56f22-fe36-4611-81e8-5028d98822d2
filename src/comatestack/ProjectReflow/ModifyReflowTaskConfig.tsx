import {Form<PERSON><PERSON>ider} from '@panda-design/path-form';
import {ParamsModifyReflowTaskConfig} from '@/types/comatestack/dataReflow';
import {useModifyReflowTaskModalOpen} from './createFlowProject';
import {useTask} from './createFlowProject';
import {ModifyReflowTaskModal} from './components/ModifyReflowTaskModal';

function convertToKeyValue(time: string) {
    const mapping: {[key: string]: [string, number]} = {
        HOUR1: ['HOUR', 1],
        HOUR2: ['HOUR', 2],
        HOUR3: ['HOUR', 3],
        HOUR4: ['HOUR', 4],
        HOUR6: ['HOUR', 6],
        HOUR8: ['HOUR', 8],
        HOUR12: ['HOUR', 12],
        DAY1: ['DAY', 1],
        DAY2: ['DAY', 2],
        DAY3: ['DAY', 3],
        DAY4: ['DAY', 4],
        DAY5: ['DAY', 5],
        DAY6: ['DAY', 6],
        WEEK1: ['WEEK', 1],
    };

    return mapping[time] || null;
}

export const ModifyReflowTaskConfig = () => {
    const open = useModifyReflowTaskModalOpen();
    const task = useTask();
    const {
        source,
        taskName,
        taskFileName,
        taskArchiveTime,
        taskTransferTime,
        taskTargetPath,
        transferDesensitizations,
        taskId,
        dataSourceType,
        dataPathList,
        accessKey,
        secretKey,
        remoteAddr,
        clientId,
        fileSuffix,
    } = task || {};

    const convertedStrings = convertToKeyValue(taskTransferTime);

    const initialValues: ParamsModifyReflowTaskConfig = {
        clientId: clientId || source?.name,
        taskName: taskName,
        taskFileName: taskFileName,
        taskTransferTime: convertedStrings,
        taskTargetPath,
        taskArchiveTime: taskArchiveTime,
        transferDesensitizations,
        stackSpace: '',
        stackProject: '',
        taskId: 0,
        taskTargetBranch: 'master',
        taskTransferEncryption: false,
        dataSourceType,
        dataPathList,
        accessKey,
        secretKey,
        remoteAddr,
        fileSuffix: fileSuffix || 'jsonl',
    };

    if (!open) {
        return null;
    }

    return (
        <FormProvider initialValues={initialValues}>
            <ModifyReflowTaskModal initialValues={initialValues} taskId={taskId} />
        </FormProvider>
    );
};

