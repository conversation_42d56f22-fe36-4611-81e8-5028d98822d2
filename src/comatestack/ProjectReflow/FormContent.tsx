import {message, Modal} from '@panda-design/components';
import {FieldDefaultPropsProvider, useFormSubmit, useFormSubmitting} from '@panda-design/path-form';
import {useToggle} from 'huse';
import {commonSubmitFail} from '@/utils/form';
import {CreateTaskType} from '@/types/comatestack/dataReflowInit';
import {apiPostCreateReflowProject} from '@/api/datareflow';
import {loadProjectReflowTaskList} from '@/regions/dataReflow/projectReflowTaskList';
import {CreateProjectFields} from '@/components/comatestack/Modals/CreateProject/CreateProjectFields';
import FormTaskContent from '@/components/comatestack/Reflow/FormTaskContent';
import ReflowScenario from '@/components/comatestack/Reflow/ReflowScenario';
import FormDataSettings from '@/components/comatestack/Reflow/FormDataSettings';
import {closeCreateFlowProjectModal} from './createFlowProject';

export const FormContent = () => {
    const loading = useFormSubmitting();
    const [_, toggleHiddenAdvancedParams] = useToggle(true);

    const handleSubmit = useFormSubmit(
        async (values: CreateTaskType) => {
            try {
                const {
                    workspaceUuid,
                    uuid,
                    reflowScenario,
                } = values;

                const {
                    clientId,
                    taskName,
                    taskFileName,
                    taskTransferTime,
                    taskTargetPath,
                    taskArchiveTime,
                    taskTargetBranch,
                    taskTransferEncryption,
                    transferDesensitizations,
                    dataSourceType,
                    dataPathList,
                    accessKey,
                    secretKey,
                    remoteAddr,
                    taskDelayTime,
                    fileSuffix,
                } = values;

                const params = {
                    flywheelProjectName: reflowScenario,
                    stackSpace: workspaceUuid,
                    stackProject: uuid,
                    clientId: clientId || uuid,
                    taskName,
                    taskFileName,
                    taskTransferTime: taskTransferTime?.join(''),
                    taskTargetPath,
                    ...(taskArchiveTime ? {taskArchiveTime} : {}),
                    taskTargetBranch,
                    taskTransferEncryption,
                    transferDesensitizations,
                    dataSourceType,
                    dataPathList,
                    accessKey,
                    secretKey,
                    remoteAddr,
                    taskDelayTime,
                    fileSuffix,
                };

                await apiPostCreateReflowProject(params);
                loadProjectReflowTaskList({
                    stackSpace: workspaceUuid,
                    stackProject: uuid,
                });

                message.success('新建成功');
                closeCreateFlowProjectModal();
            }
            catch (error) {
                message.error(error.message);
            }
        },
        commonSubmitFail
    );

    return (
        <Modal
            open
            title="创建数据回流任务"
            width={800}
            onCancel={closeCreateFlowProjectModal}
            onOk={handleSubmit}
            okButtonProps={{loading}}
            maskClosable={false}
        >
            <ReflowScenario />
            <FieldDefaultPropsProvider disabled>
                <CreateProjectFields />
            </FieldDefaultPropsProvider>
            <FormDataSettings />
            <FormTaskContent toggleHiddenAdvancedParams={toggleHiddenAdvancedParams} disabledValue={false} />
        </Modal>
    );
};
