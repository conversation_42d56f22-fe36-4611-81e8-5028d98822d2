import {message, Modal} from '@panda-design/components';
import {useFormSubmit, useFormSubmitting} from '@panda-design/path-form';
import {useEffect, useRef} from 'react';
import {isEqual} from 'lodash';
import {useParams} from 'react-router-dom';
import {useToggle} from 'huse';
import {commonSubmitFail} from '@/utils/form';
import {apiPostModifyReflowTaskConfig} from '@/api/datareflow';
import {ParamsModifyReflowTaskConfig} from '@/types/comatestack/dataReflow';
import {loadProjectReflowTaskList} from '@/regions/dataReflow/projectReflowTaskList';
import {useCurrentProject} from '@/regions/project/project';
import FormDataSettings from '@/components/comatestack/Reflow/FormDataSettings';
import FormTaskContent from '@/components/comatestack/Reflow/FormTaskContent';
import {closeModifyReflowTaskModal} from '../createFlowProject';

interface Props {
    initialValues: ParamsModifyReflowTaskConfig;
    taskId: number;
}

export const ModifyReflowTaskModal = ({initialValues, taskId}: Props) => {
    const loading = useFormSubmitting();
    const [_, toggleHiddenAdvancedParams] = useToggle(true);
    const initialValuesRef = useRef(null);
    const {projectUuid} = useParams();
    const {workspaceUuid} = useCurrentProject();

    useEffect(
        () => {
            initialValuesRef.current = JSON.parse(JSON.stringify(initialValues));
        },
        [initialValues]
    );

    const getChangedValue = (key: string, value: ParamsModifyReflowTaskConfig) => {
        if (key === 'taskTransferTime' && Array.isArray(value)) {
            return value.join('');
        }

        return value;
    };

    const handleSubmit = useFormSubmit(
        async (values: ParamsModifyReflowTaskConfig) => {
            try {
                const initialValues = initialValuesRef.current;
                const changedValues: ParamsModifyReflowTaskConfig = {
                    stackSpace: workspaceUuid,
                    stackProject: projectUuid,
                    taskId: taskId,
                };

                for (const key in values) {
                    if (!isEqual(values[key], initialValues[key])) {
                        changedValues[key] = getChangedValue(key, values[key]);
                    }
                }

                const paramsToSend: ParamsModifyReflowTaskConfig = {
                    ...{
                        stackSpace: workspaceUuid,
                        stackProject: projectUuid,
                        taskId,
                    },
                    ...changedValues,
                };

                await apiPostModifyReflowTaskConfig(paramsToSend);
                message.success('修改成功');
                closeModifyReflowTaskModal();
                loadProjectReflowTaskList({
                    stackSpace: workspaceUuid,
                    stackProject: projectUuid,
                });
            }
            catch (e) {
                message.error('修改失败');
            }
        },
        commonSubmitFail
    );

    return (
        <Modal
            open
            title="修改数据回流任务配置"
            width={800}
            onCancel={closeModifyReflowTaskModal}
            onOk={handleSubmit}
            okButtonProps={{loading}}
            maskClosable={false}
        >
            <FormDataSettings disabled />
            <FormTaskContent toggleHiddenAdvancedParams={toggleHiddenAdvancedParams} disabledValue />
        </Modal>
    );
};
