/* eslint-disable max-lines */
/* eslint-disable max-len */

import {<PERSON><PERSON>, Modal} from '@panda-design/components';
import {useBoolean, useRequestCallback} from 'huse';
import {Flex, Input, Row, Table} from 'antd';
import {ChangeEvent, useCallback, useEffect, useState} from 'react';
import DescriptionItem from '@/design/MCP/MCPDescriptionItem';
import {MCPToolItem} from '@/types/mcp/mcp';
import {apiGetMCPTool, apiPutMCPToolResponseTemplate} from '@/api/mcp';
import {IconDetail} from '@/icons/mcp';
import {useMCPApplicationId} from '@/components/MCP/hooks';
import J<PERSON><PERSON>Viewer from '@/components/MCP/JSONViewer';

const columns = [
    {
        title: '参数名称',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
    },
    {
        title: '关联参数',
        dataIndex: 'refParam',
        key: 'refParam',
    },
    {
        title: '类型',
        dataIndex: 'dataType',
        key: 'dataType',
        width: 60,
    },
    {
        title: '是否必填',
        dataIndex: 'required',
        key: 'required',
        render: (required: boolean) => (required ? '是' : '否'),
        width: 70,
    },
    {
        title: '示例',
        dataIndex: 'exampleValue',
        key: 'exampleValue',
        render: (exampleValue: any) => (exampleValue
            ? (typeof exampleValue === 'object' ? JSON.stringify(exampleValue) : exampleValue)
            : undefined),
    },
];

interface Props {
    serverId: number;
    tool: MCPToolItem;
}

export default function ServerToolDetailButton({serverId, tool}: Props) {
    const applicationId = useMCPApplicationId();
    const [request, {data}] = useRequestCallback(apiGetMCPTool, {mcpServerId: serverId, toolId: tool.id, appId: applicationId});
    const [open, {on, off}] = useBoolean(false);
    const [apiOpen, {on: apiOn, off: apiOff}] = useBoolean(false);
    const [isEdit, {on: editOn, off: editOff}] = useBoolean(false);
    const [responseTemplate, setResponseTemplate] = useState('');

    const handleClick = useCallback(
        () => {
            on();
            request();
        },
        [request, on]
    );

    const handleViewAPI = useCallback(
        () => {
            apiOn();
        },
        [apiOn]
    );

    const handleChange = useCallback(
        (e: ChangeEvent<HTMLTextAreaElement>) => {
            setResponseTemplate(e.target.value);
        },
        []
    );

    const saveResponseTemplate = useCallback(
        async () => {
            await apiPutMCPToolResponseTemplate({serverId, toolId: tool.id, applicationId, responseTemplate});
            editOff();
        },
        [applicationId, editOff, responseTemplate, serverId, tool.id]
    );

    useEffect(
        () => {
            setResponseTemplate(data?.toolConf?.responseTemplate);
        },
        [data?.toolConf?.responseTemplate]
    );

    return (
        <>
            <Button icon={<IconDetail />} type="text" onClick={handleClick}>
                详情
            </Button>
            <Modal
                width={1024}
                title="工具详情"
                open={open}
                onCancel={() => {
                    off();
                    editOff();
                    setResponseTemplate(data?.toolConf?.responseTemplate);
                }}
                footer={null}
            >
                <Flex vertical gap={16}>
                    <DescriptionItem gap={32} label="工具名称">
                        {data?.name}
                    </DescriptionItem>
                    <DescriptionItem gap={32} label="工具描述">
                        {data?.description}
                    </DescriptionItem>
                    <DescriptionItem gap={32} label="参数列表" labelStyle={{marginTop: 10}}>
                        <Table
                            columns={columns}
                            dataSource={[
                                ...(data?.toolConf?.apiDefinition?.systemParams ?? []),
                                ...(data?.toolParams?.toolParams ?? []),
                            ]}
                            style={{width: '100%'}}
                            pagination={{hideOnSinglePage: true}}
                        />
                    </DescriptionItem>
                    <DescriptionItem
                        gap={32}
                        label={<span>响应模板</span>}
                        labelStyle={{marginTop: 10}}
                    >
                        <Row justify="end" style={{marginTop: 10, marginBottom: 10}}>
                            {
                                isEdit ? (
                                    <>
                                        <Button style={{marginRight: 10}} onClick={editOff}>取消</Button>
                                        <Button type="primary" style={{marginRight: 10}} onClick={saveResponseTemplate}>
                                            保存
                                        </Button>
                                    </>
                                ) : (
                                    <Button type="primary" style={{marginRight: 10}} onClick={editOn}>
                                        编辑
                                    </Button>
                                )
                            }
                            <Button onClick={handleViewAPI}>查看API响应示例</Button>
                        </Row>
                        <Input.TextArea
                            disabled={!isEdit}
                            rows={6}
                            value={responseTemplate}
                            onChange={handleChange}
                        />
                    </DescriptionItem>
                    <Modal
                        title="API响应示例"
                        footer={null}
                        open={apiOpen}
                        onCancel={apiOff}
                    >
                        <JSONViewer json={JSON.parse(data?.toolConf?.openapiConf?.responseExamples?.[0]?.data)} />
                    </Modal>
                </Flex>
            </Modal>
        </>
    );
}
