import {ReactNode} from 'react';
import {StepData} from './types';
import {
    <PERSON><PERSON>ard,
    ProcessHeader,
    ProcessTitle,
    ProcessSteps,
    ProcessStep,
    StepIndicator,
    StepContent,
    StepTitle,
    StepDescription,
} from './styles';

export const ProcessStepsList = ({steps}: {steps: StepData[]}) => (
    <ProcessSteps>
        {steps.map((step, index) => (
            <ProcessStep key={index}>
                <StepIndicator />
                <StepContent>
                    <StepTitle>{step.title}</StepTitle>
                    <StepDescription>{step.description}</StepDescription>
                </StepContent>
            </ProcessStep>
        ))}
    </ProcessSteps>
);

interface ProcessCardProps {
    title: string;
    steps: StepData[];
    detailLink: ReactNode;
}

export const ProcessCardComponent = ({title, steps, detailLink}: ProcessCardProps) => {
    return (
        <ProcessCard>
            <ProcessHeader>
                <ProcessTitle>{title}</ProcessTitle>
                {detailLink}
            </ProcessHeader>
            <ProcessStepsList steps={steps} />
        </ProcessCard>
    );
};
