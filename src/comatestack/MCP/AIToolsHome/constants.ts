import {IconAiTools1, IconAiTools2, IconAiTools3, IconAiTools4} from '@/icons/mcp';
import {FeatureData, StepData} from './types';

export const features: FeatureData[] = [
    {
        title: '降低双边成本',
        content: '为供需双方降本，实现开发、接入、调用成本优化',
        icon: IconAiTools1,
    },
    {
        title: '协议无缝转换',
        content: '支持OpenAPI、脚本快速转换为MCP',
        icon: IconAiTools2,
    },
    {
        title: '工具即用即调',
        content: '一键体验单个工具，或者多mcp组合调试',
        icon: IconAiTools3,
    },
    {
        title: '多维部署与发布',
        content: '一键部署至 MCP 网关/CFC，多渠道分发（Comate 等）',
        icon: IconAiTools4,
    },
];

export const producerSteps: StepData[] = [
    {
        title: '便捷注册',
        description: '快速将已有MCP/OpenAPI/脚本封装为MCP Server，完成接入',
    },
    {
        title: '灵活配置',
        description: '定义工具参数与响应模板，确保模型能高效调用',
    },
    {
        title: '在线调试',
        description: '配置后可在线调试验证，查看返回结果，确保可靠',
    },
    {
        title: '发布共享',
        description: '将MCP Server发布至广场，共建共享MCP工具新生态',
    },
];

export const consumerSteps: StepData[] = [
    {
        title: '广场发现',
        description: '在广场多维度筛选MCP Server，精准匹配业务需求',
    },
    {
        title: '即开即用',
        description: '在Playground试用MCP Server，告别复杂的服务配置，真正意义上的“开箱即用”！',
    },
    {
        title: '一键订阅',
        description: '按需创建应用并订阅MCP，精准选择需要的工具',
    },
    {
        title: '集成调用',
        description: '在Comate和Cursor中直接使用MCP Server，一键安装，简单如插拔U盘',
    },
];
