/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Flex} from 'antd';
import {Button} from '@panda-design/components';
import mcpAIToolsBg from '@/assets/mcp/mcpAIToolsBg.png';

export const Container = styled.div`
    width: 100%;
    min-height: calc(100vh - 48px);
    position: relative;
    background-image: url(${mcpAIToolsBg});
    background-repeat: no-repeat;
    background-position: top center;
    overflow: hidden;
`;

export const TopNavArea = styled(Flex)`
    position: absolute;
    top: 20px;
    right: 56px;
`;

export const NavLink = styled.a`
    font-size: 14px;
    line-height: 22px;
    color: #181818;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    padding: 5px 16px;
    svg {
        margin-left: 8px;
    }
    &:hover {
        color: #0080FF;
        svg {
            color: #0080FF;
            path {
                fill: #0080FF !important;
            }
        }
    }
`;

export const MainTitleContainer = styled.div`
    position: absolute;
    margin-top: 64px;
    margin-left: 56px;
`;

export const Description = styled.div`
    font-size: 14px;
    line-height: 22px;
    color: #8D8D8D;
    margin: 4px 0 0 0;
`;

export const ButtonArea = styled(Flex)`
    margin-top: 40px;
    gap: 16px;
`;

export const FeaturesContainer = styled(Flex)`
    margin-top: 359px;
    margin-left: 56px;
    margin-right: 56px;
    justify-content: space-between;
`;

export const FeatureCard = styled.div`
    flex: 1;
    height: 144px;
    border: 2px solid #FFFFFF;
    border-radius: 8px;
    padding: 24px 16px;
    background: #fff;
    margin-right: 16px;
    position: relative;
    background: linear-gradient(
        178.56deg,
        rgba(204, 229, 255, 0.8) -44.84%,
        rgba(230, 242, 255, 0.8) -5.04%,
        rgba(242, 249, 255, 0.8) 24.57%,
        rgba(255, 255, 255, 0.8) 56.11%
    );
    backdrop-filter: blur(40px);
    box-shadow: 0px 0px 8px 0px #1B1B1B1A;

    &:last-child {
        margin-right: 0;
    }

    &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
`;

export const FeatureTitle = styled.div`
    font-weight: 500;
    font-size: 20px;
    line-height: 36px;
    color: #181818;
    margin-bottom: 8px;
`;

export const FeatureContent = styled.div`

    font-size: 14px;
    line-height: 20px;
    color: #8F8F8F;
`;

export const IconWrapper = styled.div`
    position: absolute;
    right: 16px;
    top: -16px;
`;
export const ProcessContainer = styled(Flex)`
    margin: 40px 56px 0 56px;
    gap: 16px;
`;

export const ProcessCard = styled.div`
    flex: 1;
    border-radius: 8px;
    box-shadow: 0px 0px 8px 0px #00000014;
`;

export const ProcessHeader = styled(Flex)`
    width: 100%;
    height: 66px;
    background: linear-gradient(88.32deg, #E5F2FF 1.61%, rgba(255, 254, 240, 0.8) 99.57%);
    align-items: center;
    justify-content: space-between;
    border-radius: 8px 8px 0 0;
    position: relative;
`;

export const ProcessTitle = styled.div`
    font-weight: 500;
    font-size: 20px;
    line-height: 36px;
    font-style: Medium;
    margin-left: 62px;
`;

export const ProcessSteps = styled.div`
    padding: 41px 24px 41px 64px;
`;

export const ProcessStep = styled.div`
    display: flex;
    margin-bottom: 22px;
    position: relative;

    &:last-child {
        margin-bottom: 0;
    }

    &:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 8px;
        top: 24px;
        width: 0.5px;
        height: 100%;
        border-left: 0.5px dashed #BFBFBF;
        border-left-style: dashed;
        border-left-width: 0.5px;
    }
`;

export const StepIndicator = styled.div`
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #D9D9D9;
    margin-right: 32px;
    margin-top: 4px;
    flex-shrink: 0;
    position: relative;

    &::after {
        content: '';
        position: absolute;
        width: 9px;
        height: 9px;
        border-radius: 50%;
        background-color: #FFFFFF;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
`;

export const StepContent = styled.div`
    flex: 1;
`;

export const StepTitle = styled.div`
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #0080FF;
    margin: 0 0 8px 0;
`;

export const StepDescription = styled.p`
    color: #8F8F8F;
    line-height: 22px;
`;

export const ProcessDetailButton = styled(Button)`
    position: absolute;
    top: 50%;
    right: 40px;
    transform: translateY(-50%);
    padding: 5px 16px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    line-height: 22px;
    color: #0080FF;
    svg {
        color: #0080FF;
        path {
            fill: #0080FF;
        }
    }
    &:hover {
        svg {
            color: #000;
            path {
                fill: #000;
            }
        }
        background: none !important;
    }
`;
