/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import {Flex, Form, Input, Select, Space, TableColumnsType} from 'antd';
import {useCallback} from 'react';
import {Path} from '@panda-design/path-form';
import {IconAdd} from '@/icons/lucide';
import {IconSubtract} from '@/icons/mcp';
import {RequiredTitle, StyledTable} from '../MCPEdit/ToolsContent/ParamList';

const StyledButton = styled(Button)`
    color: #317ff5 !important;
    position: relative;
    &:hover{
        color: #317ff5 !important;
    }
`;

const typeSelectOptions = [
    {label: 'String', value: 'string'},
    {label: 'Number', value: 'number'},
    {label: 'Boolean', value: 'boolean'},
    {label: 'Array', value: 'array'},
    {label: 'Object', value: 'object'},
    {label: 'Date', value: 'date'},
];

const requiredOptions = [
    {label: '必需', value: true},
    {label: '可选', value: false},
];

interface Param {
    name: string;
    description?: string;
    dataType: string;
    required: boolean;
}

interface Props {
    value?: Param[];
    onChange?: (value: Param[]) => void;
    path: Path;
    rowKey?: string;
}


const GlobalVariableField = ({value, onChange, path, rowKey = 'key'}: Props) => {
    const getNewName = useCallback(
        () => {
            const names = value?.map(item => item.name);
            let index = 1;
            let name = `key${index}`;
            while (names?.includes(name)) {
                index += 1;
                name = `key${index}`;
            }
            return name;
        },
        [value]
    );
    const onAdd = useCallback(
        () => {
            const name = getNewName();
            // 这里必须要有key,key只能用index，不然和serverConfig里的值对不上
            // @ts-ignore
            onChange?.([...(value || []), {name, key: value?.length ?? 0, dataType: 'string', required: false}]);
        },
        [onChange, value, getNewName]
    );

    const onDelete = useCallback(
        (index: number) => {
            onChange?.([...value.slice(0, index), ...value.slice(index + 1)]);
        },
        [onChange, value]
    );

    const columns: TableColumnsType<Param> = [
        {
            title: <RequiredTitle>变量名称</RequiredTitle>,
            dataIndex: 'name',
            width: 200,
            render: (_, record, index) => (
                <Space>
                    <Button icon={<IconSubtract />} tooltip="删除" type="text" onClick={() => onDelete(index)} />
                    <Form.Item
                        style={{marginBottom: 0}}
                        name={[...path, index, 'name']}
                        rules={[{required: true, message: '必填项，不可为空'}]}
                    >
                        <Input placeholder="请输入变量名称" />
                    </Form.Item>
                </Space>
            ),
        },
        {
            title: <RequiredTitle>是否必须</RequiredTitle>,
            dataIndex: 'required',
            width: 100,
            render: (_, record, index) => (
                <Form.Item
                    style={{marginBottom: 0}}
                    name={[...path, index, 'required']}
                    rules={[{required: true, message: '必填项，不可为空'}]}
                >
                    <Select options={requiredOptions} allowClear placeholder="请选择" />
                </Form.Item>
            ),
        },
        {
            title: <RequiredTitle>类型</RequiredTitle>,
            dataIndex: 'dataType',
            width: 120,
            render: (_, record, index) => (
                <Form.Item
                    style={{marginBottom: 0}}
                    name={[...path, index, 'dataType']}
                    rules={[{required: true, message: '必填项，不可为空'}]}
                >
                    <Select options={typeSelectOptions} allowClear placeholder="请选择" />
                </Form.Item>
            ),
        },
        {
            title: '描述',
            dataIndex: 'description',
            render: (_, record, index) => (
                <Form.Item
                    style={{marginBottom: 0}}
                    name={[...path, index, 'description']}
                    rules={[{max: 50, message: '最长为50字符'}]}
                >
                    <Input maxLength={50} placeholder="请输入描述" />
                </Form.Item>
            ),
        },

    ];
    return (
        <Space direction="vertical" style={{width: '100%'}}>
            <Flex justify="space-between" align="center">
                <StyledButton type="text" icon={<IconAdd />} onClick={onAdd}>添加变量</StyledButton>
                <span style={{color: '#2D2D2D', fontSize: 12}}>调用该MCP的工具时需传入的全局变量，不会暴露给模型，以在脚本中以环境变量形式引用</span>
            </Flex>
            <StyledTable
                rowKey={rowKey}
                dataSource={value}
                pagination={{hideOnSinglePage: true}}
                columns={columns}
            />
        </Space>
    );
};

export default GlobalVariableField;

