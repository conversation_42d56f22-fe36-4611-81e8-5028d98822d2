/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Form, Upload} from 'antd';
import {RcFile, UploadChangeParam, UploadFile} from 'antd/lib/upload';
import {useCallback} from 'react';
import {message} from '@panda-design/components';
import {Path} from '@panda-design/path-form';
import {IconImport} from '@/icons/mcp';
import {apiPostOpenApiUpload} from '@/api/mcp';
import {ApiDefinition} from '@/types/mcp/mcp';

const UploadWrapper = styled(Upload.Dragger)`
    .ant-5-upload-drag {
        border: 1px solid #e4e4e4 !important;
        padding: 60px 0;
        background-color: #fff;
    }
`;

interface Props {
    onChange?: (file: UploadFile[]) => void; // 解析后的数据回调
    value?: UploadFile[];
    path: Path;
}

const ImportByOpenApiWidthFileUpload = ({onChange, value, path}: Props) => {
    const {setFieldValue, getFieldValue} = Form.useFormInstance();

    const afterParse = useCallback(
        async (parsedData: any, type: string, fileName: string) => {
            const content = await apiPostOpenApiUpload({content: parsedData, apiType: type});
            const originResult = getFieldValue([...path, 'import', 'result']);
            setFieldValue(
                [...path, 'import', 'result'],
                [
                    ...originResult ?? [],
                    ...content.map(api => ({...api, fileName})),
                ]
            );
        },
        [getFieldValue, path, setFieldValue]
    );

    const handleFileChange = useCallback(
        async (info: UploadChangeParam<UploadFile<any>>) => {
            const {file, fileList} = info;
            if (value?.some(item => item.name === file.name)) {
                return;
            }
            if (file.size > 1024 * 256) {
                return message.warning('文件大小不能超过256KB!');
            }
            onChange?.(fileList);
        },
        [onChange, value]
    );

    const onRemove = useCallback(
        (file: UploadFile) => {
            const originResult: ApiDefinition[] = getFieldValue([...path, 'import', 'result']);
            setFieldValue(
                [...path, 'import', 'result'],
                originResult?.filter(item => item.fileName !== file.name)
            );
            onChange?.(
                value?.filter((f: UploadFile) => f.uid !== file.uid)
            );
            return true;
        },
        [getFieldValue, onChange, path, setFieldValue, value]
    );
    const beforeUpload = useCallback(
        (file: RcFile) => {
            if (value?.some(item => item.name === file.name)) {
                return message.warning('文件已存在!');
            }
            if (file.size > 1024 * 256) {
                return message.warning('文件大小不能超过256KB!');
            }
            const reader = new FileReader();
            reader.onload = async e => {
                try {
                    const fileExtension = file.name.split('.').pop()?.toLowerCase();
                    await afterParse(e.target?.result, fileExtension, file.name);
                } catch (error) {
                    message.error('Failed to parse file content');
                }
            };
            reader.onerror = () => {
                message.error('Failed to read file content');
            };
            reader.readAsText(file);
            return false; // 阻止文件上传
        },
        [afterParse, value]
    );

    return (
        <div style={{width: '100%'}}>
            <UploadWrapper
                accept=".json,.yaml,.yml"
                fileList={value}
                // @ts-ignore
                beforeUpload={beforeUpload}
                customRequest={({onSuccess}) => {
                    onSuccess?.('ok');
                }}
                onRemove={onRemove}
                onChange={handleFileChange}
            >
                <p style={{marginBottom: 12}}>
                    <IconImport />
                </p>
                <p>选择或者拖入.json/.yaml文件，秒速解析接口！</p>
            </UploadWrapper>
        </div>
    );
};

export default ImportByOpenApiWidthFileUpload;
