import constate from 'constate';
import {useEventListener} from '@/components/MCP/useEventListener';
import {ApiParamsValidationError} from '../ToolsContent/hooks';

const errorType = 'MCPEditFormValidationInteraction';

export const [MCPEditFormValidationInteractionProvider, useMCPEditFormValidationListener] = constate(() => {
    const {publish, subscribe} = useEventListener();

    const publishValidationError = (error: ApiParamsValidationError) => {
        publish(errorType, error);
    };

    const subscribeValidationError = (callback: (error: ApiParamsValidationError) => void) => {
        return subscribe(errorType, callback);
    };

    return {
        publishValidationError,
        subscribeValidationError,
    };
});
