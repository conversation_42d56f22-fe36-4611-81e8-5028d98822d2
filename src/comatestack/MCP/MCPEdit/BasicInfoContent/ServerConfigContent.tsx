import {Path} from '@panda-design/path-form';
import {Input} from 'antd';
import {useCallback, useEffect, useRef} from 'react';
import {useMCPServerId} from '@/components/MCP/hooks';
import {useFillServerParamsByServerConfig} from './hooks';

interface Props {
    value?: string;
    onChange?: (value?: string) => void;
    onBlur?: () => void;
    basePath: Path;
}

const useCompactStringRef = (value: string) => {
    const ref = useRef(value);
    useEffect(
        () => {
            ref.current = ref.current?.replaceAll(/\s+/g, '') ?? '';
        },
        [ref]
    );
    return ref;
};

/**
 * SSE类MCP的配置里只能有headers，stdio的配置只能有env
 */
const ServerConfigContent = ({value, onBlur, onChange}: Props) => {
    const preValue = useCompactStringRef(value);
    const fillServerParamsByServerConfig = useFillServerParamsByServerConfig();
    const mcpServerId = useMCPServerId();
    const handleChange = (e: any) => {
        onChange?.(e.target.value);
    };
    const handleBlur = useCallback(
        (e: any) => {
            const compactStr = e.target.value.replaceAll(/\s+/g, '');
            if (preValue.current === compactStr) {
                return;
            }
            preValue.current = compactStr;
            try {
                const value = e.target.value;
                onChange?.(JSON.stringify(JSON.parse(value), null, 4));
                if (mcpServerId) {
                    fillServerParamsByServerConfig();
                }
            } catch (e) {
                console.error(e);
                return;
            } finally {
                onBlur?.();
            }

        },
        [fillServerParamsByServerConfig, mcpServerId, onBlur, onChange, preValue]
    );

    return (
        <Input.TextArea
            onBlur={handleBlur}
            value={value}
            rows={6}
            onChange={handleChange}
            placeholder="请输入服务器配置(JSON格式)"
        />
    );
};

export default ServerConfigContent;

