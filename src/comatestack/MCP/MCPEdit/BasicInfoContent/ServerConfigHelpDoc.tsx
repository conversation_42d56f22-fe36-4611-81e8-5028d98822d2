/* eslint-disable max-len */
import {Typo<PERSON>, Modal} from 'antd';
import {useBoolean} from 'huse';
import {useMemo} from 'react';
import J<PERSON><PERSON>Viewer from '@/components/MCP/JSONViewer';
import {Help} from '@/design/Help';

const SSEServerConfigExample = () => {
    const example = {
        'mcpServers': {
            'iCafeCodingAgent': {
                'timeout': 30,
                'url': 'http://mcp-proxy.baidu-int.com/server/iCafeCodingAgent/mcp-plugin-center/sse',
                'headers': {
                    'spaceId': '<-spaceId->',
                    'u': '<-u->',
                    'pw': '<-pw->',
                },
            },
        },
    };
    return (
        <div>
            <Typography.Title level={4} style={{marginBottom: 16}}>url为必须字段</Typography.Title>
            <JSONViewer json={example} />
        </div>
    );
};

const STDIOServerConfigExample = () => {
    const example = {
        'mcpServers': {
            'github': {
                'command': 'docker',
                'args': [
                    'run',
                    '-i',
                    '--rm',
                    '-e',
                    'GITHUB_PERSONAL_ACCESS_TOKEN',
                    '-e',
                    'GITHUB_TOOLSETS',
                    '-e',
                    'GITHUB_READ_ONLY',
                    'ghcr.io/github/github-mcp-server',
                ],
                'env': {
                    'GITHUB_PERSONAL_ACCESS_TOKEN': 'GitHub Personal Access Token',
                    'GITHUB_TOOLSETS': 'GitHub Toolsets (optional)',
                    'GITHUB_READ_ONLY': '',
                },
            },
        },
    };
    return (
        <div>
            <Typography.Title level={4} style={{marginBottom: 16}}>command为必须字段</Typography.Title>
            <JSONViewer json={example} />
        </div>
    );
};

const StreamableHttpServerConfigExample = () => {
    const example = {
        'mcpServers': {
            'streamable': {
                'transportType': 'streamableHttp',
                // url 字段需填写服务器提供的端点路径，该端点需同时支持 POST 和 GET 方法，又被称为 MCP 端点
                'url': 'http://localhost:3000/mcp',
                'disabled': false,
            },
        },
    };
    return (
        <div>
            <Typography.Title level={4} style={{marginBottom: 16}}>transportType和url必须字段</Typography.Title>
            <JSONViewer json={example} />
        </div>
    );
};

export const ServerConfigHelpDoc = ({serverProtocolType}: {serverProtocolType: 'STDIO' | 'SSE' | 'Streamable_HTTP'}) => {
    const [open, {toggle}] = useBoolean(false);
    const exampleDoc = useMemo(
        () => {
            switch (serverProtocolType) {
                case 'SSE':
                    return <SSEServerConfigExample />;
                case 'STDIO':
                    return <STDIOServerConfigExample />;
                case 'Streamable_HTTP':
                    return <StreamableHttpServerConfigExample />;
                default:
                    return <></>;
            }
        },
        [serverProtocolType]
    );
    return (
        <>
            <Help onClick={toggle}>点击查看示例</Help>
            <Modal open={open} onCancel={toggle} title="服务器配置示例" width={800} footer={null}>
                {exampleDoc}
            </Modal>
        </>
    );
};
