/* eslint-disable max-len */
import {Button} from '@panda-design/components';
import {Form, Row} from 'antd';
import {useCallback} from 'react';
import type {JSONSchema7} from 'json-schema';
import {SwapOutlined} from '@ant-design/icons';
import schemaToJson from '@/utils/mcp/schemaToJson';
import mockRules from '@/utils/mcp/json-schema/constant';
import J<PERSON><PERSON><PERSON>iewer from '@/components/MCP/JSONViewer';
import {useActiveTool} from '../hooks';

interface Props {
    data: string | null;
    schema: JSONSchema7 | null;
}

const ResponseExample = (props: Props) => {
    const {data, schema} = props;
    const {activeToolIndex} = useActiveTool();
    const {setFieldValue} = Form.useFormInstance();

    const updateJson = useCallback(
        (json: string) => {
            setFieldValue(['tools', activeToolIndex, 'toolConf', 'openapiConf', 'responseExamples', 0, 'data'], json);
        },
        [activeToolIndex, setFieldValue]
    );

    return (
        <>
            <Row style={{display: 'flex', justifyContent: 'flex-end', marginBottom: 10}}>
                {
                    schema && <Button type="text" icon={<SwapOutlined />} onClick={() => updateJson(JSON.stringify(schemaToJson(schema, {}, mockRules), null, 2))}>一键生成</Button>
                }
            </Row>
            <Row>
                <Form.Item
                    name={['tools', activeToolIndex, 'toolConf', 'openapiConf', 'responseExamples', 0, 'data']}
                    rules={[{required: true, message: '请输入或生成响应示例'}]}
                    style={{flex: 1}}
                >
                    <JSONViewer
                        json={JSON.parse(data ? data : '{}')}
                        onAdd={add => updateJson(JSON.stringify(add.updated_src, null, 2))}
                        onDelete={del => updateJson(JSON.stringify(del.updated_src, null, 2))}
                        onEdit={edit => updateJson(JSON.stringify(edit.updated_src, null, 2))}
                    />
                </Form.Item>
            </Row>
        </>
    );
};

export default ResponseExample;
