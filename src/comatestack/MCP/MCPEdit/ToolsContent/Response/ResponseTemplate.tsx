import {Form, Input} from 'antd';
import {useActiveTool} from '../hooks';

const ResponseTemplate = (props: {template: string | null}) => {
    const {template} = props;
    const {activeToolIndex} = useActiveTool();

    const placeholder = `
    # User Information
        {{- with(index .results 0)}}
        - **Name**: {{.name.first}} {{.name.last}}
        - **Email**: {{.email}}
        - **Location**: {{.location.city}},
    {{.location.country}}
        - **Phone**: {{.phone}}
        {{- end }}
    `;
    return (
        <>
            <Form.Item
                name={['tools', activeToolIndex, 'toolConf', 'responseTemplate']}
                style={{flex: 1}}
            >
                <Input.TextArea rows={6} defaultValue={template} placeholder={placeholder} />
            </Form.Item>
        </>
    );
};

export default ResponseTemplate;
