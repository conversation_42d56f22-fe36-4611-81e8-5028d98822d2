/* eslint-disable max-lines */
import {Flex, Form} from 'antd';
import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import {useEffect, useMemo} from 'react';
import {IconUnfold} from '@/icons/mcp';
import GlobalVariable from './GlobalVariable';
import ParamsConfig from './ParamsConfig';
import EndPoint from './EndPoint';
import {StyledCollapse, StyledTitle} from './ToolsDefine';
import {useActiveTool, useHandleRelateParamTreeData} from './hooks';
import ResponseConfiguration from './Response/ResponseConfiguration';
import {useTreeDataProviderContext} from '.';
import ResponseExample from './Response/ResponseExample';

const ShowMoreIconWithStatus = styled(IconUnfold) <{showApiDefine: boolean}>`
    width: 16px;
    height: 16px;
    transform: ${props => (props.showApiDefine ? 'rotate(-180deg)' : 'rotate(0deg)')};
`;

const APIShowButtonWrapper = styled.div`
    position: absolute;
    right: 0;
    top: 72px;
`;

const APIShowButton = styled(Button)`
    justify-content: center !important;
    align-items: center !important;
    background-color: #E5F2FF !important;
    color: #0080FF !important;
    height: 240px !important;
    font-weight: 500 !important;
    border-radius: 6px 0 0 6px !important;
    border-color: #0080FF !important;
`;

const Content = styled.div`
    max-height: calc(100vh - 376px);
    overflow-y: auto;
`;

interface Props {
    showApiDefine: boolean;
    show: () => void;
    hide: () => void;
}

const ApiDefine = ({show, hide, showApiDefine}: Props) => {
    const {activeToolIndex} = useActiveTool();
    const serverParams = Form.useWatch(['tools', activeToolIndex, 'toolParams', 'serverParams']);
    const responses = Form.useWatch(['tools', activeToolIndex, 'toolConf', 'openapiConf', 'responses']);
    const responseExamples = Form.useWatch(['tools', activeToolIndex, 'toolConf', 'openapiConf', 'responseExamples']);
    const treeData = useHandleRelateParamTreeData({canSelectRoot: false, titleWidthRoot: true});
    const {setTreeData} = useTreeDataProviderContext();
    useEffect(
        () => {
            setTreeData?.(treeData);
        },
        [setTreeData, treeData]
    );
    const items = useMemo(
        () => [
            ...(serverParams?.length ? [{
                key: 'variable',
                children: (
                    <Form.Item name={['tools', activeToolIndex, 'toolParams', 'serverParams']}>
                        <GlobalVariable path={['tools', activeToolIndex, 'toolParams', 'serverParams']} />
                    </Form.Item>
                ),
                label: '全局变量',
            }] : []),
            {
                key: 'endPoint',
                children: <EndPoint />,
                label: 'EndPoint',
            },
            {
                key: 'paramsConfig',
                children: <ParamsConfig />,
                label: '参数配置',
            },
            {
                key: 'responseConfiguration',
                children: <ResponseConfiguration schema={responses ? responses?.[0]?.jsonSchema : null} />,
                label: '响应结构',
            },
            {
                key: 'responseExample',
                children: <ResponseExample
                    data={responseExamples ? responseExamples?.[0]?.data : null}
                    schema={responses ? responses[0]?.jsonSchema : null}
                />,
                label: '响应示例',
            },
        ],
        [activeToolIndex, responseExamples, responses, serverParams?.length]
    );
    if (!showApiDefine) {
        return (
            <APIShowButtonWrapper>
                <APIShowButton
                    style={{writingMode: 'vertical-rl', textOrientation: 'upright', height: 'auto'}}
                    type="text"
                    onClick={showApiDefine ? hide : show}
                    icon={<ShowMoreIconWithStatus showApiDefine={showApiDefine} />}
                >
                    展开API定义
                </APIShowButton>
            </APIShowButtonWrapper>
        );
    }
    return (
        <div style={{width: 'calc(50% - 12px)'}}>
            <Flex align="center" justify="space-between">
                <StyledTitle>API 定义</StyledTitle>
                <Button
                    type="text"
                    tooltip="收起API定义"
                    onClick={showApiDefine ? hide : show}
                    icon={<ShowMoreIconWithStatus showApiDefine={showApiDefine} />}
                />
            </Flex>
            <Content>
                <StyledCollapse
                    // eslint-disable-next-line max-len
                    defaultActiveKey={['variable', 'paramsConfig', 'endPoint', 'responseExample', 'responseConfiguration']}
                    items={items}
                    bordered={false}
                />
            </Content>
        </div>
    );
};

export default ApiDefine;
