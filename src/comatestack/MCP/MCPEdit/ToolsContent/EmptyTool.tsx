import {Button} from '@panda-design/components';
import {Form} from 'antd';
import {useBoolean} from 'huse';
import EmptyBlock from '@/design/icode/EmptyBlock';
import {IconAdd} from '@/icons/lucide';
import ToolAddModal from './ToolAddModal';
import {useAddTool} from './ToolList/hooks';

const EmptyTool = ({noAction}: {noAction: boolean}) => {
    const [open, {on: show, off: hide}] = useBoolean();
    const serverSourceType = Form.useWatch('serverSourceType');
    const addTool = useAddTool();
    return (
        <>
            <ToolAddModal isLocal={serverSourceType === 'script'} open={open} onCancel={hide} onAdd={addTool} />
            <EmptyBlock
                description="您还没有添加工具"
                action={
                    !noAction && <Button type="text" onClick={show} icon={<IconAdd />}>添加工具</Button>
                }
            />
        </>
    );
};

export default EmptyTool;
