/* eslint-disable max-lines */
import {memo, useCallback, useEffect, useState} from 'react';
import {Form} from 'antd';
import styled from '@emotion/styled';
import {css} from '@emotion/css';
import {useSearchParams} from '@panda-design/router';
import {useBlocker} from 'react-router-dom';
import {Gap} from '@/design/iplayground/Gap';
import {useMCPServerId} from '@/components/MCP/hooks';
import {loadMCPServer, useMCPServer} from '@/regions/mcp/mcpServer';
import bg from '@/assets/mcp/pageBg.png';
import vipbg from '@/assets/mcp/pageVipBg.png';
import MCPTabs from '@/design/MCP/MCPTabs';
import {useSearchReplace} from '@/hooks/icode/common/useSearchParams';
import ToolsContent from './ToolsContent';
import MCPHeaderWidthBasicInfo from './MCPHeaderWidthBasicInfo';
import BasicInfoContent from './BasicInfoContent';
import ActionButtons from './ActionButtons';
import {useTouchedBasePath} from './regions';
import LeavePageConfirmModal from './LeavePageConfirmModal';
import {onValueChange} from './hooks';
import {MCPEditFormItemProvider} from './Providers/MCPEditFormItemProvider';
import {tapIndexAsKey} from './utils';
import {MCPEditFormValidationInteractionProvider} from './Providers/MCPEditFormValidationInteractionProvider';

const FormWrapper = styled(Form) <{official?: boolean}>`
    height: calc(100vh - 48px);
    width: 100%;
    display: flex;
    flex-direction: column;
    background: url(${({official}) => (official ? vipbg : bg)}) no-repeat;
`;

const Content = styled.div`
    margin: 0 24px !important;
    padding: 16px 0;
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    border-radius: 6px;
    `;

const HeaderWrapper = styled.div`
    padding: 24px 24px 0px;
`;

const items = [
    {
        key: 'serverInfo',
        label: '基本信息',
    },
    {
        key: 'tools',
        label: '工具',
    },
];

const tabCss = css`
    margin: 2px 0;
`;

export const errorRowCss = css`
    td {
        &: first-child {
            border-left: 2px solid #e62c4b !important;
        }
    }
`;

const disabledName = [/^tools-[0-9]+-name$/];

const MCPEdit = memo(() => {
    const {activeTab} = useSearchParams();
    const replace = useSearchReplace();
    const setActiveTab = (tab: string) => {
        replace({activeTab: tab});
    };
    const [form] = Form.useForm();
    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);
    const touchedBasePath = useTouchedBasePath();
    const [nextLocation, setNextLocation] = useState<string>();
    const [leaveConfirmType, setLeaveConfirmType] = useState<'notSaved' | 'invalidForm'>('notSaved');
    const blocker = useBlocker(
        transition => {
            const leavePageNotConfirm = localStorage.getItem('leavePageNotConfirm');
            if (leavePageNotConfirm !== 'true') {
                const {pathname, search} = transition.nextLocation;
                if (touchedBasePath?.[activeTab]) {
                    form.validateFields().then(
                        () => {
                            setLeaveConfirmType('notSaved');
                            setNextLocation(pathname + search);
                        }
                    ).catch(
                        () => {
                            setLeaveConfirmType('invalidForm');
                            setNextLocation(pathname + search);
                        }
                    );
                }
                return touchedBasePath?.[activeTab];
            }
            return false;
        }
    );
    useEffect(
        () => {
            loadMCPServer({mcpServerId});
        },
        [mcpServerId]
    );
    const resetForm = useCallback(
        () => {
            if (mcpServer) {
                form.setFieldValue(
                    'serverInfo',
                    {
                        ...mcpServer,
                        serverConf: {
                            ...mcpServer.serverConf,
                            serverConfig: mcpServer.serverConf?.serverConfig
                                ? JSON.stringify(mcpServer.serverConf.serverConfig, null, 4) : '',
                        },
                        serverParams: tapIndexAsKey(mcpServer.serverParams, 'index'),
                        labels: mcpServer.labels.map(({id}) => id),
                    }
                );
                form.setFieldValue('serverSourceType', mcpServer.serverSourceType);
            } else {
                form.resetFields();
            }
        },
        [form, mcpServer]
    );
    useEffect(
        () => {
            if (mcpServer) {
                // 用这个方法来初始化表单数据
                resetForm();
            }
        },
        [resetForm, mcpServer]
    );

    return (
        <MCPEditFormValidationInteractionProvider>
            <MCPEditFormItemProvider
                serverSourceType={mcpServer?.serverSourceType}
                activeTab={activeTab}
                disableExclude={disabledName}
            >
                <FormWrapper
                    official={mcpServer?.official}
                    form={form}
                    colon={false}
                    labelCol={{flex: '100px'}}
                    labelAlign="left"
                    onFieldsChange={
                        field => onValueChange(field, form)
                    }
                >
                    <HeaderWrapper>
                        <MCPHeaderWidthBasicInfo />
                        <MCPTabs
                            className={tabCss}
                            items={items}
                            activeKey={activeTab}
                            onChange={setActiveTab}
                            tabBarExtraContent={<ActionButtons activeTab={activeTab} />}
                        />
                    </HeaderWrapper>
                    <Gap />
                    <Content style={{border: activeTab === 'tools' ? 'none' : '1px solid #E8E8E8'}}>
                        {activeTab === 'serverInfo' && <BasicInfoContent />}
                        {activeTab === 'tools' && <ToolsContent />}
                    </Content>
                    <Form.Item name="serverSourceType" hidden />
                    <Form.Item name="tools" hidden />
                    {nextLocation && blocker.state === 'blocked' && (
                        <LeavePageConfirmModal
                            nextLocation={nextLocation}
                            onCancel={() => setNextLocation('')}
                            type={leaveConfirmType}
                            resetForm={resetForm}
                        />
                    )}
                </FormWrapper>
            </MCPEditFormItemProvider>
        </MCPEditFormValidationInteractionProvider>
    );
});

export default MCPEdit;
