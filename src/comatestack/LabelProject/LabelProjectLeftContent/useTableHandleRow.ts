import {useCallback} from 'react';
import {useNavigate, useParams} from 'react-router-dom';
import {useLabelParams} from '@/hooks/label/useLabelParams';
import {LabelTaskData} from '@/types/label/labelTaskData';
import {setBatchMarkTaskIndex} from '@/regions/labelBatchTask/batchMarkTaskIndex';
import {setLabelMode} from '@/regions/label/labelTask';
import {LabelNormalTaskLink} from '@/links/label';
import {useTablePagination} from './useTablePagination';

export const useTableHandleRow = () => {
    const {labelStudioProjectId, labelProjectId} = useLabelParams();
    const navigate = useNavigate();
    const {projectUuid} = useParams();
    const {current, pageSize} = useTablePagination();

    const handleRow = useCallback(
        (taskData: LabelTaskData, index: number) => {
            return {
                onClick: () => {
                    const taskIndex = (current - 1) * pageSize + index;
                    setBatchMarkTaskIndex(labelStudioProjectId, taskIndex);
                    setLabelMode('Normal');
                    navigate(
                        LabelNormalTaskLink.toUrl({
                            projectUuid,
                            labelProjectId,
                            labelStudioProjectId,
                        }),
                        {state: {selectedTaskIds: undefined}}
                    );
                },
            };
        },
        [current, labelProjectId, labelStudioProjectId, navigate, pageSize, projectUuid]
    );

    return handleRow;
};
