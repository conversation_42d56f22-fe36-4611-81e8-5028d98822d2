import {Flex, Form, Input, Tag, Tooltip} from 'antd';
import {debounce, isEqual, isNil} from 'lodash';
import {ChangeEvent, useCallback, useEffect, useRef, useState} from 'react';
import styled from '@emotion/styled';
import {
    apiCaseEvaluateUpsert,
    apiEvaluateRecordUpdate,
    UpdateCaseEvaluateScoreItem,
    UpdateDiffItem,
} from '@/api/ievalue/case';
import {CaseHistoryTypeEnum} from '@/constants/ievalue/case';
import {useCaseStageType, useTaskTaskID} from '@/hooks/ievalue/task';
import {getScoreItemByMetric} from '@/ievalue/TaskDetail/utils';
import {ScoreGroupProps} from '@/ievalue/TaskDetail/CaseProcessPanel/ModelResult/ScoreGroup';
import {TaskStageEnum} from '@/constants/ievalue/task';

const StyleFlex = styled(Flex)`
    .ant-5-form-css-var {
        --ant-5-form-item-margin-bottom: 0px !important;
    }
    .ant-5-form-item {
        font-weight: 500 !important;
        .ant-5-row > .ant-5-col > label {
            color: black !important;
        }
    }
    width: 100%;
`;

const ScoreInput = ({
    metricItem,
    recordItem,
    groupCaseInfo,
    needUpdateRecord,
    blockHandle,
    handleRefresh,
}: ScoreGroupProps) => {
    const originScoreItem = getScoreItemByMetric(
        metricItem.metric,
        recordItem.origin
    );
    const diffScoreItem = getScoreItemByMetric(
        metricItem.metric,
        recordItem?.diff
    );
    const [value, setValue] = useState(originScoreItem?.scoreInput ?? '');
    const taskID = useTaskTaskID();
    const stageType = useCaseStageType();

    const handleChange = useCallback(
        async (e: ChangeEvent<HTMLTextAreaElement>) => {
            const inputValue = e.target.value.trim();
            if (blockHandle) {
                return;
            }
            const diff: UpdateDiffItem[] = [
                {
                    type: CaseHistoryTypeEnum.RECORD,
                    caseID: groupCaseInfo.caseID,
                    groupID: groupCaseInfo.groupID,
                    recordID: recordItem.origin.predictRecordID,
                    field: metricItem.metric,
                    origin: `${value}`,
                    turnTo: `${inputValue}`,
                },
            ];

            const score: UpdateCaseEvaluateScoreItem[] = [
                {
                    metric: metricItem.metric,
                    scoreName: inputValue,
                    score: Number(inputValue) || 0,
                    scoreInput: inputValue,
                    notRequired: !!metricItem?.notRequired,
                },
            ];

            setValue(inputValue);

            try {
                const apiCall = needUpdateRecord
                    ? apiEvaluateRecordUpdate({
                        ID: recordItem.origin.ID,
                        predictRecordID: recordItem.origin.predictRecordID,
                        evaluateCaseID: recordItem.origin.evaluateCaseID,
                        taskID,
                        score,
                        diff,
                    })
                    : apiCaseEvaluateUpsert({
                        accepted: 0,
                        predictRecordID: recordItem.origin.predictRecordID,
                        stageID: groupCaseInfo.stageID,
                        groupID: groupCaseInfo.groupID,
                        caseID: groupCaseInfo.caseID,
                        taskID,
                        score,
                        diff,
                    });

                await apiCall;
            } catch (error) {
                console.error('Update failed:', error);
            } finally {
                handleRefresh?.();
            }
        },
        [
            blockHandle,
            groupCaseInfo.caseID,
            groupCaseInfo.groupID,
            groupCaseInfo.stageID,
            handleRefresh,
            metricItem.metric,
            metricItem?.notRequired,
            needUpdateRecord,
            recordItem.origin.ID,
            recordItem.origin.evaluateCaseID,
            recordItem.origin.predictRecordID,
            taskID,
            value,
        ]
    );

    const debouncedChange = useRef(debounce(handleChange, 500));

    useEffect(
        () => {
            debouncedChange.current = debounce(handleChange, 500);
            return () => debouncedChange.current?.cancel();
        },
        [handleChange]
    );

    return (
        <StyleFlex align="start" vertical gap={4}>
            <Form.Item
                label={metricItem.desc}
                required={!metricItem?.notRequired}
                style={{width: '100%'}}
            >
                <Input.TextArea
                    size="small"
                    rows={1}
                    defaultValue={
                        stageType === TaskStageEnum.AUDITING_FORWARD
                            ? diffScoreItem?.scoreInput ?? value
                            : value
                    }
                    draggable={false}
                    placeholder="请输入"
                    onChange={debouncedChange.current}
                    onBlur={debouncedChange.current}
                    disabled={blockHandle}
                />
            </Form.Item>
            {stageType === TaskStageEnum.AUDITING_FORWARD
                && !isNil(diffScoreItem?.scoreInput)
                && !isEqual(
                    diffScoreItem?.scoreInput,
                    originScoreItem?.scoreInput
                ) && (
                <Tooltip title="上一轮得分" placement="bottom">
                    <Tag
                        style={{
                            wordWrap: 'break-word',
                            whiteSpace: 'pre-wrap',
                        }}
                    >
                        {originScoreItem?.scoreInput}
                    </Tag>
                </Tooltip>
            )}
        </StyleFlex>
    );
};

export default ScoreInput;
