import {Space} from 'antd';
import {Button, message} from '@panda-design/components';
import {head} from 'lodash';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {apiEvaluateCaseUpsert} from '@/api/ievalue/case';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {CaseStatusEnum} from '@/constants/ievalue/case';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {ModelName} from '@/components/Evaluate/ModelName';
import {
    useCaseEvaluateList,
    useCaseStageType,
    useGroupCaseID,
    useGroupID,
    useGroupStageInfo,
    useTaskStageID,
    useTaskTaskID,
} from '@/hooks/ievalue/task';
import {GSBSortDefaultTag} from './GSBSortDefaultTag';

export const useGSBOperatorMap = () => {
    const {caseEvaluateRecords, caseEvaluateRankMap} = useCaseEvaluateList();
    const [firstModel, secondModel] = caseEvaluateRecords?.map(
        (record, index) => ({
            id: record.origin.predictRecordID,
            realName: record.origin.model,
            pretendName: `结果${index + 1}`,
        })
    );

    return {
        operatorMap: {
            '>': (
                <Space align="center">
                    <ModelName
                        realName={firstModel?.realName ?? ''}
                        pretendName={firstModel?.pretendName ?? ''}
                    />
                    <div>|</div>
                    更好
                </Space>
            ),
            '<': (
                <Space align="center">
                    <ModelName
                        realName={secondModel?.realName ?? ''}
                        pretendName={secondModel?.pretendName ?? ''}
                    />
                    <div>|</div>
                    更好
                </Space>
            ),
            '=': '两者基本相同',
            '~': '两者都不好',
        },
        caseEvaluateRankMap,
        firstModel,
        secondModel,
    };
};

const GSBSort = () => {
    const [, , groupInfo] = useGroupStageInfo();
    const stageType = useCaseStageType();
    const caseID = useGroupCaseID();
    const taskID = useTaskTaskID();
    const groupID = useGroupID();
    const stageID = useTaskStageID();
    const {operatorMap, caseEvaluateRankMap, firstModel, secondModel} =
        useGSBOperatorMap();
    const [operator, setOperator] = useState(
        head(caseEvaluateRankMap?.origin?.operators)
    );
    const blockHandle = useMemo(
        () => {
            return (
                [
                    CaseStatusEnum.FINISH,
                    CaseStatusEnum.REJECTED,
                    CaseStatusEnum.RESOLVE_DISPUTE,
                ].includes(groupInfo.status) || stageType === TaskStageEnum.FEEDBACK
            );
        },
        [groupInfo.status, stageType]
    );

    useEffect(
        () => {
            if (caseEvaluateRankMap?.origin?.operators) {
                setOperator(head(caseEvaluateRankMap?.origin?.operators));
            }
        },
        [caseEvaluateRankMap?.origin?.operators]
    );

    const handleClick = useCallback(
        (currentOperator: string) => async () => {
            if (blockHandle) {
                return;
            }
            try {
                await apiEvaluateCaseUpsert({
                    taskID,
                    stageID,
                    caseID,
                    operators: [currentOperator],
                    ranking: [firstModel?.id, secondModel?.id],
                    groupID,
                    diff: [
                        {
                            type: 'record',
                            caseID,
                            groupID,
                            field: 'operators',
                            origin: operator,
                            turnTo: currentOperator,
                        },
                    ],
                });
                setOperator(currentOperator);
            } catch (error) {
                if (error instanceof Error) {
                    message.error(error.message);
                }
            }
        },
        [
            blockHandle,
            caseID,
            firstModel?.id,
            groupID,
            operator,
            secondModel?.id,
            stageID,
            taskID,
        ]
    );

    return (
        <FlexLayout style={{width: '100%'}} justify="space-around">
            <Button
                type={operator === '>' ? 'primary' : undefined}
                onClick={handleClick('>')}
            >
                {operatorMap['>']}
            </Button>
            <Button
                type={operator === '=' ? 'primary' : undefined}
                onClick={handleClick('=')}
            >
                {operatorMap['=']}
            </Button>
            <Button
                type={operator === '~' ? 'primary' : undefined}
                onClick={handleClick('~')}
            >
                {operatorMap['~']}
            </Button>
            <Button
                type={operator === '<' ? 'primary' : undefined}
                onClick={handleClick('<')}
            >
                {operatorMap['<']}
            </Button>
            <GSBSortDefaultTag />
        </FlexLayout>
    );
};

export default GSBSort;
