import {Card, Typography, Tag} from 'antd';
import JsonOrMarkdownOrDiff from '@/components/ievalue/JsonOrMarkdownOrDiff';
import {ScratchWordsBaseProvider} from '@/components/ievalue/ScratchWordsMarkdown/ScratchWordsBaseProvider';

const {Text, Title} = Typography;

interface ContentSectionProps {
    type: 'answer' | 'note' | 'tags';
    activeTab: string;
    content?: string;
    tags?: string[];
    predictRecordID?: number;
}

export const ContentSection = ({
    type,
    activeTab,
    content,
    tags,
    predictRecordID,
}: ContentSectionProps) => {
    const titleMap = {
        answer: `${activeTab}分回答`,
        note: `${activeTab}分备注`,
        tags: `${activeTab}分标签`,
    };

    return (
        <div style={{flex: 1, display: 'flex', flexDirection: 'column'}}>
            <div style={{
                padding: '8px 12px',
                backgroundColor: '#f5f5f5',
                borderRadius: '4px 4px 0 0',
            }}
            >
                <Title level={5} style={{margin: 0}}>{titleMap[type]}</Title>
            </div>
            <Card style={{height: '100%'}}>
                {type === 'answer' && (
                    <ScratchWordsBaseProvider
                        disabled
                        predictRecordID={predictRecordID}
                        containerId={'answerContainerId'}
                    >
                        <JsonOrMarkdownOrDiff content={content} />
                    </ScratchWordsBaseProvider>
                )}
                {type === 'note' && <Text>{content || ''}</Text>}
                {type === 'tags' && (
                    <div style={{display: 'flex', flexWrap: 'wrap', gap: '8px'}}>
                        {tags?.map(tag => (
                            tag.length > 0 && (
                                <Tag key={tag} color="blue">
                                    {tag}
                                </Tag>
                            )
                        ))}
                    </div>
                )}
            </Card>
        </div>
    );
};
