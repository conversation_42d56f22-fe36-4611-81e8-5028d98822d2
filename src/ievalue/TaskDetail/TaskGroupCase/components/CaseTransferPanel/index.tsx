/* eslint-disable max-lines */
/* eslint-disable react/jsx-no-bind */
import {FileSearchOutlined} from '@ant-design/icons';
import {Button} from '@panda-design/components';
import {Select, Space, Table, Typography} from 'antd';
import {ColumnProps} from 'antd/es/table';
import {head} from 'lodash';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {useSearchParams} from '@panda-design/router';
import {useNavigate, useParams} from 'react-router-dom';
import {CaseItem} from '@/api/ievalue/case';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {TableMDPopover} from '@/components/ievalue/TableMDPopover';
import {EditNodeProvider} from '@/components/ievalue/TableMDPopover/TableMDProvider';
import {
    TaskEvaluateModeEnum,
    TaskStageEnum,
    TaskStagMap,
    TaskStatusEnum,
    TaskStatusMap,
    TaskStatusOptions,
} from '@/constants/ievalue/task';
import {useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {
    useIsChatbot,
    useTaskCaseListPagination,
    useTaskCaseStats,
    useTaskCaseUsernames,
    useTaskInfo,
    useTaskStrategyInfo,
    useTaskTaskID,
} from '@/hooks/ievalue/task';
import {TaskUrl} from '@/links/ievalue/task';
import {getModelScoreColumn} from '@/components/Evaluate/TaskDetailUtils/util';
import NoTask from '../../NoTask';
import {Container, StyledLink} from '../GroupCaseTable';
import CaseAuthBatchUpdateButton from '../CaseAuthBatchUpdateButton';
import CaseAuditForwardBatchFinishButton from '../CaseAuditForwardBatchFinishButton';
import CaseAcceptBatchFinishButton from '../CaseAcceptBatchFinishButton';
import {CaseTransferFinishButton} from './CaseTransferFinishButton';

export const CaseTransferPanel = () => {
    const {user: searchUserName} = useSearchParams();
    const spaceCode = useSpaceCodeSafe();
    const taskID = useTaskTaskID();
    const paramData = useParams();
    const [taskInfo] = useTaskInfo();
    const [data] = useTaskCaseStats();
    const isChatbot = useIsChatbot();
    const [userList, userListRefresh] = useTaskCaseUsernames();
    const strategyInfo = useTaskStrategyInfo();
    const navigate = useNavigate();
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);

    const curentStageID = useMemo(
        () => {
            return paramData?.stageID === 'all' ? null : Number(paramData?.stageID);
        },
        [paramData?.stageID]
    );

    const stageOptions = useMemo(
        () => {
            return [
                {label: '全部', value: null},
                ...Object.values(data ?? {}).map(item => ({
                    label: item.stageName,
                    value: item.stageID,
                })),
            ];
        },
        [data]
    );
    const userListOptions = useMemo(
        () =>
            userList.map(item => ({
                label: item,
                value: item,
            })),

        [userList]
    );

    const {
        stageID,
        setStageID,
        username,
        setUsername,
        stageStatus,
        setStageStatus,
        dataSource,
        pagination,
        pending,
        refresh,
    } = useTaskCaseListPagination({
        stageID: curentStageID,
        username: searchUserName,
    });

    useEffect(
        () => {
            setStageID(curentStageID);
        },
        [curentStageID, setStageID]
    );

    const handleStageChange = useCallback(
        (value: number) => {
            navigate(
                TaskUrl.taskGroupDetail.toUrl({
                    spaceCode,
                    taskID,
                    groupID: 0,
                    stageID: value ?? 'all',
                })
            );
        },
        [navigate, spaceCode, taskID]
    );

    const handleAuthBatchUpdate = useCallback(
        () => {
            setSelectedRowKeys([]);
            refresh();
            userListRefresh();
        },
        [refresh, userListRefresh]
    );

    const columns: Array<ColumnProps<CaseItem>> = useMemo(
        () => {
            return [
                {
                    title: 'Query',
                    dataIndex: 'query',
                    key: 'query',
                    ellipsis: {
                        showTitle: false,
                    },
                    render: (_: any, record: CaseItem) => {
                        return (
                            <TableMDPopover
                                content={record.query}
                                editKey="input"
                                datasetID={taskInfo.datasetID}
                                onRefresh={refresh}
                                titleName={'Query'}
                                caseID={record.caseID}
                                groupID={record.groupID}
                                stageID={record.stageID}
                            />
                        );
                    },
                },
                {
                    title: '预期结果',
                    dataIndex: 'referenceOutput',
                    key: 'referenceOutput',
                    ellipsis: {
                        showTitle: false,
                    },
                    render: (_: any, record: CaseItem) => {
                        return (
                            <TableMDPopover
                                content={record.referenceOutput}
                                editKey="referenceOutput"
                                onRefresh={refresh}
                                titleName={'预期结果'}
                                datasetID={taskInfo.datasetID}
                                caseID={record.caseID}
                                groupID={record.groupID}
                                stageID={record.stageID}
                            />
                        );
                    },
                },
                ...getModelScoreColumn(
                    dataSource,
                    stageID ?? head(Object.values(data))?.stageID,
                    taskInfo?.datasetID,
                    strategyInfo,
                    refresh,
                    false,
                    taskInfo?.evaluateMode === TaskEvaluateModeEnum.AUTO
                ),
                {
                    title: '执行阶段',
                    dataIndex: 'stageName',
                    key: 'stageName',
                    width: 80,
                },
                {
                    title: '执行状态',
                    dataIndex: 'stageStatus',
                    key: 'stageStatus',
                    render: (stageStatus: TaskStatusEnum) => {
                        return TaskStatusMap[stageStatus];
                    },
                    width: 80,
                },
                {
                    title: '操作',
                    dataIndex: 'option',
                    key: 'option',
                    width: 80,
                    render: (_: any, record: CaseItem) => {
                        const {groupID, stageID, caseID, stageName} = record;
                        const caseProcess = isChatbot
                            ? TaskUrl.taskChatbotCaseProcess
                            : TaskUrl.taskCaseProcess;
                        if (stageName === TaskStagMap[TaskStageEnum.PREDICTING]) {
                            return <Typography.Text>暂无操作</Typography.Text>;
                        }
                        return (
                            <StyledLink
                                to={`${caseProcess.toUrl({
                                    spaceCode,
                                    taskID,
                                    groupID,
                                    stageID,
                                    caseID,
                                })}?dispatchType=case`}
                                target="_blank"
                            >
                                <Button type="text" icon={<FileSearchOutlined />}>
                                    详情
                                </Button>
                            </StyledLink>
                        );
                        /* <DeleteCaseButton caseID={caseID} onRefresh={refresh} /> */
                    },
                },
            ];
        },
        [
            data,
            dataSource,
            isChatbot,
            refresh,
            spaceCode,
            stageID,
            strategyInfo,
            taskID,
            taskInfo?.datasetID,
            taskInfo?.evaluateMode,
        ]
    );

    return (
        <FlexLayout direction="column" gap={12} style={{width: '100%'}}>
            <Typography.Title level={4}>Case列表</Typography.Title>
            <FlexLayout
                gap={8}
                style={{width: '100%'}}
                justify="space-between"
            >
                <Space>
                    <Typography.Text>执行阶段：</Typography.Text>
                    <Select
                        value={curentStageID}
                        onChange={handleStageChange}
                        style={{width: 100}}
                        options={stageOptions}
                        allowClear
                    />
                    <Typography.Text>执行状态：</Typography.Text>
                    <Select
                        value={stageStatus}
                        style={{width: 120}}
                        placeholder="请选择状态"
                        options={TaskStatusOptions}
                        onChange={setStageStatus}
                        allowClear
                    />
                    <Typography.Text>负责人：</Typography.Text>
                    <Select
                        value={username}
                        onChange={setUsername}
                        placeholder="请选择负责人"
                        style={{width: 160}}
                        options={userListOptions}
                        allowClear
                    />
                    <CaseAuthBatchUpdateButton
                        type="link"
                        onFinish={handleAuthBatchUpdate}
                        caseIDs={selectedRowKeys}
                    />
                    <CaseAuditForwardBatchFinishButton
                        type="link"
                        onFinish={handleAuthBatchUpdate}
                        caseIDs={selectedRowKeys}
                    />
                    <CaseAcceptBatchFinishButton
                        type="link"
                        onFinish={handleAuthBatchUpdate}
                        caseIDs={selectedRowKeys}
                    />
                </Space>
                <CaseTransferFinishButton onRefresh={refresh} />
            </FlexLayout>
            <Container>
                {dataSource.length === 0 && !pending ? (
                    <NoTask />
                ) : (
                    <EditNodeProvider>
                        <Table<CaseItem>
                            rowKey="caseID"
                            columns={columns}
                            dataSource={dataSource}
                            loading={pending}
                            pagination={pagination}
                            rowSelection={{
                                selectedRowKeys,
                                onChange: setSelectedRowKeys,
                            }}
                            scroll={{x: true}}
                            size="small"
                        />
                    </EditNodeProvider>
                )}
            </Container>
        </FlexLayout>
    );
};
