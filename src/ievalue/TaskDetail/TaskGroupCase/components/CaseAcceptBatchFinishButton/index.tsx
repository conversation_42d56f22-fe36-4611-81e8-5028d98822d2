import {Button, message} from '@panda-design/components';
import {ComponentProps, useCallback} from 'react';
import {useBoolean} from 'huse';
import {apiAcceptanceBatchFinishByCaseID} from '@/api/ievalue/case';
import {useTaskTaskID} from '@/hooks/ievalue/task';

export interface CaseAuthBatchUpdateProps {
    caseIDs: number[];
    onFinish: () => void;
}

type CaseAcceptBatchFinishButtonProps = ComponentProps<typeof Button> &
    CaseAuthBatchUpdateProps;

const CaseAcceptBatchFinishButton = ({
    onFinish,
    caseIDs,
    ...props
}: CaseAcceptBatchFinishButtonProps) => {
    const taskID = useTaskTaskID();
    const [loading, {on: startLoading, off: stopLoading}] = useBoolean(false);

    const handleClick = useCallback(
        async () => {
            startLoading();
            try {
                const result = await apiAcceptanceBatchFinishByCaseID({
                    taskID,
                    caseIDs,
                });
                const details = [
                    result.stageErrorCaseIDS.length > 0
                    && `${result.stageErrorCaseIDS.length}条case不在验收阶段`,
                    result.unAuthorizedCaseID.length > 0
                    && `${result.unAuthorizedCaseID.length}条case没有权限处理`,
                    result.finishCaseIDS.length > 0
                    && `${result.finishCaseIDS.length}条case状态已经完成不用处理`,
                    result.unProcessedCaseIDS.length > 0
                    && `${result.unProcessedCaseIDS.length}条case不满足批量处理条件`,
                ]
                    .filter(Boolean)
                    .join(',');
                message.success({
                    content: `批量完成验收成功，其中有${
                        result.successedCaseIDS.length
                    }条case处理成功,${
                        caseIDs.length - result.successedCaseIDS.length
                    }条case不满足批量处理条件已自动跳过${
                        details ? `（${details}）` : ''
                    }`,
                    duration: 10,
                });
            } catch (e) {
            /* empty */
            } finally {
                onFinish();
                stopLoading();
            }
        },
        [caseIDs, onFinish, startLoading, stopLoading, taskID]
    );

    return caseIDs?.length > 0 ? (
        <Button loading={loading} {...props} onClick={handleClick}>
            批量完成验收
        </Button>
    ) : (
        <></>
    );
};

export default CaseAcceptBatchFinishButton;
