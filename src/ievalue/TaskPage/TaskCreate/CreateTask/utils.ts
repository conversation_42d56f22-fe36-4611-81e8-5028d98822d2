/* eslint-disable complexity */
import {uniqBy} from 'lodash';
import {
    AutoComputeMetricItem,
    MetricFormItemParamItem,
    StagetegyItem,
} from '@/api/ievalue/case';
import {TaskInfoItem, TaskTemplate} from '@/api/ievalue/task';
import {
    GSB_MANUAL_POLICY_ID,
    StrategyGenreEnum,
    StrategyMetricSelectTypeEnum,
} from '@/constants/ievalue/evaluate';
import {TaskPredictTypeEnum, TaskStageEnum} from '@/constants/ievalue/task';
import {ModelOptionItem} from '@/types/ievalue/task';
import {PromptWithVersionModel} from '@/api/ievalue/prompt';
import {CreateTaskDatasetTypeEnum} from './Components/DatasetConfig/DatasetTypeFormItem';

// 一言业务方自动化评估策略得评估维度默认不全选
export const YIYAN_AUTO_POLICY_ID = 285;

export const getDefaultMetricData = (
    currentStrategy: StagetegyItem,
    checked = true,
    attrName: 'metric' | 'autoComputeMetric' = 'metric'
) => {
    return (
        currentStrategy?.[attrName]?.map(item => ({
            ...item,
            checked,
            models: undefined,
        })) ?? []
    );
};

export const convertMetricDataToForm = (
    allStagetegyList: StagetegyItem[],
    metricData?: MetricFormItemParamItem[] | AutoComputeMetricItem[],
    policyID?: number,
    attrName: 'metric' | 'autoComputeMetric' = 'metric'
) => {
    if (!metricData || !policyID) {
        return [];
    }
    const currentStrategy = allStagetegyList.find(
        item => item.ID === policyID
    );
    const defaultMetricData = getDefaultMetricData(
        currentStrategy,
        false,
        attrName
    );
    return defaultMetricData?.reduce((result, item) => {
        const metricItem =
            metricData.find(x => x.metric === item.metric) || {};
        result.push({...item, ...metricItem});
        return result;
    }, []);
};

export const getDefaultBlindParamsData = (templateData: TaskTemplate) => {
    return templateData?.stages?.reduce((resultList, item) => {
        if (
            ![TaskStageEnum.NEW, TaskStageEnum.PREDICTING].includes(
                item.stageType as TaskStageEnum
            )
        ) {
            resultList.push({
                sequence: item?.sequence,
                stageName: item?.stageName,
                stageType: item?.stageType,
                admin: 0,
                creator: 0,
                editor: 0,
            });
        }
        return resultList;
    }, []);
};

export const getDefaultSamplingRateParamsData = (
    templateData: TaskTemplate,
    defaultValue?: number
) => {
    return templateData?.stages?.reduce((resultList, item) => {
        if (
            [TaskStageEnum.AUDITING_FORWARD].includes(
                item.stageType as TaskStageEnum
            )
        ) {
            resultList.push({
                sequence: item?.sequence,
                stageName: item?.stageName,
                stageType: item?.stageType,
                samplingRatio: defaultValue || 100,
            });
        }
        return resultList;
    }, []);
};

export const getDefaultRejectRateParamsData = (
    templateData: TaskTemplate,
    defaultValue?: number
) => {
    return templateData?.stages?.reduce((resultList, item) => {
        if (
            [TaskStageEnum.AUDITING_FORWARD].includes(
                item.stageType as TaskStageEnum
            )
        ) {
            resultList.push({
                sequence: item?.sequence,
                stageName: item?.stageName,
                stageType: item?.stageType,
                rejectRatio: defaultValue || 100,
            });
        }
        return resultList;
    }, []);
};

export const createTaskDataConvertFormData = (
    copyTaskData: TaskInfoItem,
    promptList: PromptWithVersionModel[],
    modelsOptions: ModelOptionItem[],
    strategyList: StagetegyItem[],
    templateData: TaskTemplate,
    isHistorySpace: boolean
) => {
    const modelSelects = uniqBy(copyTaskData?.modelParams, 'modelID')?.map(
        item => {
            const modelItem = modelsOptions.find(
                x => x.value === item.modelID
            );
            return {
                config: {
                    ...modelItem,
                    ...item,
                    // 给模型的debugParams添加默认值
                    debugParams: modelItem?.debugParams?.map(baseParam => {
                        const debugParam = item.debugParams?.find(
                            x => x.id === baseParam.id
                        );
                        return {
                            ...baseParam,
                            value: baseParam?.default,
                            ...debugParam,
                            inUse: !!debugParam?.inUse,
                            inUseDisplay: baseParam?.inUseDisplay,
                        };
                    }),
                },
            };
        }
    );
    const promptHistoryIDs = promptList?.reduce((result, x) => {
        const list = (x?.promptVersions || [])
            ?.filter(i =>
                copyTaskData?.promptVersionIDs?.includes(i.promptVersionID)
            )
            ?.map(i => [x.promptID, i.promptVersionID]);
        return result.concat(list);
    }, []);
    const createData = {
        name: copyTaskData?.name,
        note: copyTaskData?.note ?? '',
        trainTaskID: copyTaskData?.trainTaskID,
        promptHistoryIDs,
        isAutoTransfer: !!copyTaskData?.isAutoTransfer,
        datasetID: copyTaskData?.datasetID || '',
        mapTemplateID: copyTaskData?.mapTemplateID,
        datasetName: copyTaskData?.datasetName,
        predictRound: copyTaskData?.predictRound,
        priority: copyTaskData?.priority,
        evaluateMode: copyTaskData?.evaluateMode,
        cooperateType: copyTaskData?.cooperateType,
        blind: !!copyTaskData?.blind,
        blindParams:
            copyTaskData?.blindParams
            ?? getDefaultBlindParamsData(templateData),
        showMethod: copyTaskData?.showMethod,
        reportFormat: copyTaskData?.reportFormat,
        isRoundScoring: !!copyTaskData?.isRoundScoring,
        isSessionAutoScore: !!copyTaskData?.isSessionAutoScore,
        isSampling: !!copyTaskData?.isSampling,
        samplingRateParams:
            copyTaskData?.samplingRateParams
            ?? getDefaultSamplingRateParamsData(
                templateData,
                (copyTaskData?.samplingRatio ?? 1) * 100
            ),
        autoSkip: !!copyTaskData?.autoSkip,
        withReject: !!copyTaskData?.withReject,
        partner: copyTaskData?.partner ?? '',
        rejectRateParams:
            copyTaskData?.rejectRateParams
            ?? getDefaultRejectRateParamsData(templateData, 100),
        proportion: !!copyTaskData?.proportion,
        proportionParams: copyTaskData?.proportionParams ?? [],
        datasetFormat: copyTaskData?.datasetFormat,
        dispatchType: !!copyTaskData?.dispatchType,
        autoMetric: convertMetricDataToForm(
            strategyList,
            copyTaskData?.autoMetric,
            copyTaskData?.autoPolicy
        ),
        autoPolicy: copyTaskData?.autoPolicy,
        manualMetric: convertMetricDataToForm(
            strategyList,
            copyTaskData?.manualMetric,
            copyTaskData?.manualPolicy
        ),
        manualPolicy: copyTaskData?.manualPolicy,
        manualAutoComputeMetric: convertMetricDataToForm(
            strategyList,
            copyTaskData?.manualAutoComputeMetric,
            copyTaskData?.manualPolicy,
            'autoComputeMetric'
        ),
        ordermadeParam: copyTaskData?.ordermadeParam,
        predictParam: copyTaskData?.predictParam,
        hasFeedback: !!copyTaskData?.hasFeedback,
        evaluateParam: JSON.stringify(copyTaskData?.evaluateParam ?? {}),
        datasetType:
            copyTaskData?.trainTaskID || isHistorySpace
                ? CreateTaskDatasetTypeEnum.UPLOADED
                : CreateTaskDatasetTypeEnum.IDATASET,
        modelSelects,
        isMultiEvaluatePredict:
            copyTaskData?.predictType !== TaskPredictTypeEnum.OFFLINE,
        files: copyTaskData?.files || [],
        dirID: copyTaskData?.dirID,
    };
    return createData;
};

export const isStagetegyMetricHaveCheckboxOrInput = (item: StagetegyItem) => {
    return (
        item.genre === StrategyGenreEnum.MANUAL
        && item?.metric?.some(item =>
            [
                StrategyMetricSelectTypeEnum.CHECKBOX,
                StrategyMetricSelectTypeEnum.INPUT,
            ].includes(item.selectType)
        )
    );
};

export const isStrategyGSB = (item: StagetegyItem) => {
    return (
        item.ID === GSB_MANUAL_POLICY_ID
        && item.genre === StrategyGenreEnum.MANUAL
    );
};

export const movePolicyList = (
    allPolicylist: StagetegyItem[],
    topPolicyIDList?: number[]
) => {
    if (!topPolicyIDList?.length) {
        return allPolicylist;
    }
    const {topPolicyList, otherPolicyList} = allPolicylist?.reduce(
        (result, item) => {
            if (topPolicyIDList.includes(item.ID)) {
                result.topPolicyList.push(item);
            } else {
                result.otherPolicyList.push(item);
            }
            return result;
        },
        {
            topPolicyList: [] as StagetegyItem[],
            otherPolicyList: [] as StagetegyItem[],
        }
    );
    return [...topPolicyList, ...otherPolicyList];
};
