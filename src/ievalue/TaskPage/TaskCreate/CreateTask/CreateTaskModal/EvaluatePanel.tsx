import {Form} from 'antd';
import {ModelSelectFormItem} from '../Components/BasicConfig/ModelSelectFormItem';
import {AutoMetricFormItem} from '../Components/EvaluateConfig/AutoMetricFormItem';
import {AutoPolicyFormItem} from '../Components/EvaluateConfig/AutoPolicyFormItem';
import {BlindFormItem} from '../Components/EvaluateConfig/BlindFormItem';
import {BlindParamFormItem} from '../Components/EvaluateConfig/BlindParamFormItem';
import {ComateStackTaskIDFormItem} from '../Components/EvaluateConfig/ComateStackTaskIDFormItem';
import {CooperateTypeFormItem} from '../Components/EvaluateConfig/CooperateTypeFormItem';
import {EvaluateParamFormItem} from '../Components/EvaluateConfig/EvaluateParamFormItem';
import {FeedbackFormItem} from '../Components/EvaluateConfig/FeedbackFormItem';
import ManualAutoComputeFormItem from '../Components/EvaluateConfig/ManualAutoComputeFormItem';
import {ManualMetricFormItem} from '../Components/EvaluateConfig/ManualMetricFormItem';
import {ManualPolicyFormItem} from '../Components/EvaluateConfig/ManualPolicyFormItem';
import {OrdermadeParamFormItem} from '../Components/EvaluateConfig/OrdermadeParamFormItem';
import {ProportionFormItem} from '../Components/EvaluateConfig/ProportionFormItem';
import {ReportFormatFormItem} from '../Components/EvaluateConfig/ReportFormatFormItem';
import {SamplingAuditFormItem} from '../Components/EvaluateConfig/SamplingAuditFormItem';
import {ShowMethodFormItem} from '../Components/EvaluateConfig/ShowMethodFormItem';
import {WithRejectAuditFormItem} from '../Components/EvaluateConfig/WithRejectAuditFormItem';
import {TaskPartnerFormItem} from '../Components/EvaluateConfig/TaskPartnerFormItem';
import {DatasetConfig} from './DatasetConfig';

const EvaluatePanel = ({current}: {current: number}) => {
    return (
        <Form.Item noStyle hidden={current > 1}>
            <ModelSelectFormItem />
            <ShowMethodFormItem />
            <ComateStackTaskIDFormItem />
            {/* <EvaluateModeFormItem /> */}
            <BlindFormItem />
            <BlindParamFormItem />
            <ProportionFormItem />
            <CooperateTypeFormItem />
            <SamplingAuditFormItem />
            <WithRejectAuditFormItem />
            <AutoPolicyFormItem />
            <OrdermadeParamFormItem />
            <AutoMetricFormItem />
            <FeedbackFormItem />
            <ManualPolicyFormItem />
            <ManualMetricFormItem />
            <ManualAutoComputeFormItem />
            <ReportFormatFormItem />
            <EvaluateParamFormItem />
            <DatasetConfig />
            <TaskPartnerFormItem />
        </Form.Item>
    );
};

export default EvaluatePanel;
