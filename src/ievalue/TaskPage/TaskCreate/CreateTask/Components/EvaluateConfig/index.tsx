import {StageConfig} from '../../../StepDetail/Components/EmptyItemWrapper';
import {useCreateTaskIsOnlyPREDICTING} from '../CreateTaskProvider/DatasetProvider';
import {AutoMetricFormItem} from './AutoMetricFormItem';
import {AutoPolicyFormItem} from './AutoPolicyFormItem';
import {BlindFormItem} from './BlindFormItem';
import {BlindParamFormItem} from './BlindParamFormItem';
import {ComateStackTaskIDFormItem} from './ComateStackTaskIDFormItem';
import {CooperateTypeFormItem} from './CooperateTypeFormItem';
import {EvaluateModeFormItem} from './EvaluateModeFormItem';
import {EvaluateParamFormItem} from './EvaluateParamFormItem';
import {FeedbackFormItem} from './FeedbackFormItem';
import {GroupConfigFormItem} from './GroupConfigFormItem';
import ManualAutoComputeFormItem from './ManualAutoComputeFormItem';
import {ManualMetricFormItem} from './ManualMetricFormItem';
import {ManualPolicyFormItem} from './ManualPolicyFormItem';
import {OrdermadeParamFormItem} from './OrdermadeParamFormItem';
import {ProportionFormItem} from './ProportionFormItem';
import {ReportFormatFormItem} from './ReportFormatFormItem';
import {SamplingAuditFormItem} from './SamplingAuditFormItem';
import {ShowMethodFormItem} from './ShowMethodFormItem';
import {TaskPartnerFormItem} from './TaskPartnerFormItem';
import {WithRejectAuditFormItem} from './WithRejectAuditFormItem';

export const EvaluateConfig = () => {
    const onlyPREDICTING = useCreateTaskIsOnlyPREDICTING();
    return (
        <StageConfig title={onlyPREDICTING ? '' : '评估配置'} ID="part-4">
            <GroupConfigFormItem />
            <ShowMethodFormItem />
            <ComateStackTaskIDFormItem />
            <EvaluateModeFormItem />
            <BlindFormItem />
            <BlindParamFormItem />
            <ProportionFormItem />
            <CooperateTypeFormItem />
            <SamplingAuditFormItem />
            <WithRejectAuditFormItem />
            <AutoPolicyFormItem />
            <OrdermadeParamFormItem />
            <AutoMetricFormItem />
            <FeedbackFormItem />
            <ManualPolicyFormItem />
            <ManualMetricFormItem />
            <ManualAutoComputeFormItem />
            <ReportFormatFormItem />
            <EvaluateParamFormItem />
            <TaskPartnerFormItem />
        </StageConfig>
    );
};
