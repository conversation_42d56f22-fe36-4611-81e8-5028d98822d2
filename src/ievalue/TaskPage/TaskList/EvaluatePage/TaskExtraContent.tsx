import {Space} from 'antd';
import {Button} from '@panda-design/components';
import {Link, useNavigate} from 'react-router-dom';
import {CreateCompareReport} from '@/components/ievalue/CreateCompareReport/CreateCompareReport';
import {useIsYiYanSpace, useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {TaskUrl} from '@/links/ievalue/task';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {useDirTreeContext} from '@/components/DirTree/DirTreeProvider';
import {isRuntimeOnline} from '@/utils/ievalue';
import {CreateSummaryReportButton} from '../CreateSummaryReportButton';
import RandomTaskJumpByStageButton from '../RandomTaskJumpByStageButton';
import {TemplateCreateTaskButton} from '../../TaskCreate/StepTemplateSelect/TemplateCreateTaskButton';
import ExportTaskEvaluateDetailButton from '../ExportTaskEvaluateDetailButton';
import {TaskMoveTo} from './TaskList/TaskMoveTo';
import {TaskMoveModal} from './TaskList/TaskMoveTo/TaskMoveModal';
import {ReportDownloadButton} from './ReportDownloadButton';

export const TaskExtraContent = () => {
    const spaceCode = useSpaceCodeSafe();
    const isYiYangSpace = useIsYiYanSpace();
    const navigate = useNavigate();
    const {currentDir} = useDirTreeContext();

    const handleNavigateToStatistics = () => {
        navigate(TaskUrl.statistics.toUrl({spaceCode, dirID: currentDir?.id}));
    };

    return (
        <FlexLayout style={{margin: '10px 0', flexWrap: 'wrap'}} justify="space-between">
            <Space>
                {!isYiYangSpace && (
                    <>
                        <CreateCompareReport />
                        <CreateSummaryReportButton />
                    </>
                )}
                {(['demo1227'].includes(spaceCode) || isYiYangSpace) && (
                    <Link
                        to={TaskUrl.taskQueueList.toUrl({spaceCode})}
                        target="_blank"
                    >
                        <Button type="link">查看排队列表</Button>
                    </Link>
                )}
                <TemplateCreateTaskButton />
                <Button onClick={handleNavigateToStatistics}>统计分析</Button>
                <ReportDownloadButton />
                {!isYiYangSpace && (
                    <Link
                        to={TaskUrl.templateList.toUrl({spaceCode})}
                        target="_blank"
                    >
                        <Button type="link">查看任务模板</Button>
                    </Link>
                )}
                <TaskMoveTo />
                {!isRuntimeOnline() && <ExportTaskEvaluateDetailButton />}
            </Space>
            <Space>
                <RandomTaskJumpByStageButton stage={TaskStageEnum.EVALUATING} />
                <RandomTaskJumpByStageButton stage={TaskStageEnum.AUDITING_FORWARD} />
            </Space>
            <TaskMoveModal />
        </FlexLayout>
    );
};
