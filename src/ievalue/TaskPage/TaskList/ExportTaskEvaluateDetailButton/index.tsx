import {useCallback} from 'react';
import {message} from '@panda-design/components';
import {DownloadButton} from '@/components/ievalue/DownloadButton';
import {apiTasksZipFileGenerate} from '@/api/ievalue/evaluate';
import {useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {
    resetSelectedRows,
    useSelectedRows,
} from '@/regions/ievalue/task/selectedRows';

const ExportTaskEvaluateDetailButton = () => {
    const selectedTasks = useSelectedRows();
    const spaceCode = useSpaceCodeSafe();
    const getFileInfo = useCallback(
        async () => {
            if (selectedTasks.length > 10 || selectedTasks.length < 2) {
                throw new Error('批量导出当前仅支持单次导出2-10个任务');
            }
            const url = await apiTasksZipFileGenerate({
                taskIDs: selectedTasks,
                spaceCode,
            });
            if (!url) {
                throw new Error('批量导出任务详情失败');
            }
            message.success('批量导出任务详情成功');
            resetSelectedRows();
            return {url};
        },
        [selectedTasks, spaceCode]
    );

    if (!selectedTasks.length) {
        return null;
    }

    return (
        <DownloadButton
            fileUrl={''}
            title="批量导出任务详情"
            getFileInfo={getFileInfo}
            type="link"
        />
    );
};

export default ExportTaskEvaluateDetailButton;
