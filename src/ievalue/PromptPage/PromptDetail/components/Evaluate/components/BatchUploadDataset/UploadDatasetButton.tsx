
import {CloudUploadOutlined, PaperClipOutlined, DeleteOutlined} from '@ant-design/icons';
import {Space, Tooltip} from 'antd';
import {useMemo} from 'react';
import {Button} from '@panda-design/components';
export const UploadDatasetButton = (props: any) => {
    const {selectedVersions, onDelete, on, hasTestModel, datasetName} = props;
    const disabledTip = useMemo(
        () => {
            if (selectedVersions.length === 0) {
                return '请选择历史版本';
            }
            if (hasTestModel) {
                return '试用模型不支持推理，请添加自己的模型';
            }
            return undefined;
        },
        [hasTestModel, selectedVersions.length]
    );

    return (
        <Space>
            <Tooltip title={disabledTip}>
                <Button
                    icon={<CloudUploadOutlined />}
                    type="primary"
                    onClick={() => {
                        on();
                    }}
                    disabled={!!disabledTip || props.disabled}
                >
                    上传评估集
                </Button>
            </Tooltip>
            {datasetName && (
                <Button type="text" icon={<PaperClipOutlined />}>
                    <Space size="small">
                        {datasetName}
                        {!props.disabled && (
                            <DeleteOutlined onClick={onDelete} />
                        )}
                    </Space>
                </Button>
            )}
        </Space>
    );
};
