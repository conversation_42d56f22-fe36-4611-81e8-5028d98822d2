import {Form, FormInstance, Input, Radio, Select, Space, Switch} from 'antd';
import {Button} from '@panda-design/components';
import {useCallback, useEffect, useMemo} from 'react';
import {DeleteOutlined, PlusOutlined} from '@ant-design/icons';
import {uniq} from 'lodash';
import {css} from '@emotion/css';
import {StagetegyItem} from '@/api/ievalue/case';
import {
    EvaluateAccessWayEnum,
    StrategyGenreEnum,
    StrategyRunningModeEnum,
    StrategyRunningModeMap,
} from '@/constants/ievalue/evaluate';
import {useEvaluateEvaluatorList} from '@/hooks/ievalue/settings';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {isRuntimeOnline} from '@/utils/ievalue';
import {CustomTagFormItem} from '../components/CustomTagFormItem';
import {MixMetricFormItem} from '../components/MixMetricFormItem';
import {SimpleMetricFormItem} from '../components/SimpleMetricFormItem';
import CanModifyInUseFormItem from '../components/CanModifyInUseFormItem';
import AutoComputeMetric from '../components/AutoComputeMetric';

interface UpdateStrategyFormProps {
    strategyItem?: StagetegyItem;
    form: FormInstance;
    genre: StrategyGenreEnum;
    handleFinish: (formData: StagetegyItem) => void;
}

export const UpdateStrategyForm = ({
    strategyItem,
    handleFinish,
    form,
    genre,
}: UpdateStrategyFormProps) => {
    const [evaluatorList] = useEvaluateEvaluatorList();
    const runningMode = Form.useWatch('runningMode', form);

    const evaluatorListOptions = useMemo(
        () => {
            return evaluatorList
                .filter(
                    item => item.accessWay !== EvaluateAccessWayEnum.ALGORITHM
                )
                .map(item => ({
                    label: item.name,
                    value: item.ID,
                }));
        },
        [evaluatorList]
    );

    const handleChangeValue = useCallback(
        (changedValues: StagetegyItem) => {
            const {evaluatorID, runningMode} = changedValues;
            if (runningMode) {
                form.setFieldsValue({
                    evaluatorID: '',
                    metric: [],
                });
            }
            if (evaluatorID) {
                form.setFieldValue('metric', []);
            }
        },
        [form]
    );

    useEffect(
        () => {
            form.resetFields();
        },
        [strategyItem, form]
    );
    return (
        <Form<StagetegyItem>
            form={form}
            className={css`
                .ant-5-form-css-var {
                    --ant-5-form-item-margin-bottom: 10px !important;
                }
            `}
            initialValues={strategyItem}
            onFinish={handleFinish}
            labelCol={{flex: '125px'}}
            onValuesChange={handleChangeValue}
            labelAlign="left"
        >
            {strategyItem?.ID && (
                <>
                    <Form.Item name="ID" hidden>
                        <Input />
                    </Form.Item>
                    <Form.Item name="status" hidden>
                        <Input />
                    </Form.Item>
                </>
            )}
            <Form.Item
                name="name"
                label="策略名称"
                rules={[{required: true}]}
            >
                <Input placeholder="请输入策略名称" />
            </Form.Item>
            <Form.Item
                name="runningMode"
                label="策略类型"
                rules={[{required: true}]}
            >
                <Radio.Group disabled={!!strategyItem?.ID}>
                    {genre === StrategyGenreEnum.MANUAL ? (
                        <Radio value={StrategyRunningModeEnum.MANUAL}>
                            {StrategyRunningModeMap.MANUAL}
                        </Radio>
                    ) : (
                        <>
                            {!isRuntimeOnline() && !!strategyItem?.ID && (
                                <Radio
                                    value={StrategyRunningModeEnum.YIYANSAMPLE}
                                >
                                    {StrategyRunningModeMap.YIYANSAMPLE}
                                </Radio>
                            )}
                            <Radio value={StrategyRunningModeEnum.MIX}>
                                {StrategyRunningModeMap.MIX}
                            </Radio>
                            <Radio value={StrategyRunningModeEnum.YIYANDIY}>
                                {StrategyRunningModeMap.YIYANDIY}
                            </Radio>
                            <Radio value={StrategyRunningModeEnum.AUTO}>
                                {StrategyRunningModeMap.AUTO}
                            </Radio>
                        </>
                    )}
                </Radio.Group>
            </Form.Item>
            {runningMode === StrategyRunningModeEnum.AUTO && (
                <Form.Item
                    label="评估服务"
                    name="evaluatorID"
                    rules={[{required: true}]}
                >
                    <Select
                        placeholder="请选择评估服务"
                        options={evaluatorListOptions}
                    />
                </Form.Item>
            )}
            {runningMode === StrategyRunningModeEnum.MANUAL && (
                <Form.Item
                    name="needSort"
                    label="排序"
                    tooltip="排序打开时，平台默认使用第一个评估维度进行打分排序"
                >
                    <Switch />
                </Form.Item>
            )}
            <Form.Item name="sortMetric" label="排序字段" hidden>
                <Input />
            </Form.Item>
            {[
                StrategyRunningModeEnum.MIX,
                StrategyRunningModeEnum.MANUAL,
                StrategyRunningModeEnum.YIYANDIY,
                StrategyRunningModeEnum.YIYANSAMPLE,
            ].includes(runningMode)
                ? (
                    <MixMetricFormItem
                        runningMode={runningMode}
                        strategyGenre={genre}
                    />
                ) : (
                    <SimpleMetricFormItem runningMode={runningMode} />
                )}
            <AutoComputeMetric />
            <Form.Item
                label="其他备注"
                hidden={runningMode !== StrategyRunningModeEnum.MANUAL}
            >
                <Form.List
                    name="noteNames"
                    rules={[
                        {
                            validator: (_, values) => {
                                if (
                                    values
                                    && uniq(values)?.length !== values?.length
                                ) {
                                    return Promise.reject(
                                        new Error('名称不能重复')
                                    );
                                }
                                return Promise.resolve();
                            },
                        },
                    ]}
                >
                    {(fields, {add, remove}, {errors}) => (
                        <FlexLayout direction="column" gap={2}>
                            {fields.map(field => (
                                <Space
                                    key={field.key}
                                    size={4}
                                    align="baseline"
                                >
                                    <Form.Item
                                        name={[field.name]}
                                        rules={[
                                            {
                                                required: true,
                                                message: '请输入名称',
                                            },
                                        ]}
                                    >
                                        <Input style={{width: 300}} />
                                    </Form.Item>
                                    <DeleteOutlined
                                        onClick={() => {
                                            remove(field.name);
                                        }}
                                    />
                                </Space>
                            ))}
                            <Button
                                type="text"
                                onClick={() => add()}
                                icon={<PlusOutlined />}
                            >
                                新增
                            </Button>
                            <Form.ErrorList errors={errors} />
                        </FlexLayout>
                    )}
                </Form.List>
            </Form.Item>
            <Form.Item
                name="descript"
                label="评估规则"
                hidden={runningMode !== StrategyRunningModeEnum.MANUAL}
                rules={[
                    {
                        required:
                            runningMode === StrategyRunningModeEnum.MANUAL,
                    },
                ]}
            >
                <Input.TextArea placeholder="请输入评估规则" />
            </Form.Item>
            <Form.Item
                name="tags"
                label="标签"
                help="多个换行分割"
                hidden={runningMode !== StrategyRunningModeEnum.MANUAL}
            >
                <Input.TextArea placeholder="请输入标签，换行分割" rows={5} />
            </Form.Item>
            <CanModifyInUseFormItem />
            <CustomTagFormItem />
        </Form>
    );
};
