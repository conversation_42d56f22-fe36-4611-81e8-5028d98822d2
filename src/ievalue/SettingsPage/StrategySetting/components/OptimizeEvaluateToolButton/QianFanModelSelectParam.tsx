import {Divider, Select} from 'antd';
import {useRequest} from 'huse';
import {useMemo} from 'react';
import {apiModelListGet} from '@/api/ievalue/model';
import {useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {convertModelOption} from '@/utils/ievalue/task';
import {NewModelButton} from '@/ievalue/SettingsPage/CreateModelButton';
import {ModelSpaceCodeEnum, ModelTypeEnum} from '@/constants/ievalue/model';
import {FormItemParamProps} from '@/types/common/form';

interface QianFanModelSelectParamProps extends FormItemParamProps<number> {
    filterFun?: (item?: any) => boolean;
}

const QianFanModelSelectParam = ({
    value,
    onChange,
    filterFun = () => true,
}: QianFanModelSelectParamProps) => {
    const spaceCode = useSpaceCodeSafe();
    const {data: result, refresh} = useRequest(apiModelListGet, {
        pn: 1,
        size: 1000,
        modelType: ModelTypeEnum.ChatGLM,
        scenarioes: 'ALL,EVALUATE',
        spaceCode: `${spaceCode},${ModelSpaceCodeEnum.Public_fee}`,
    });

    const modelsOptions = useMemo(
        () => {
            return result?.list?.filter(filterFun)?.map(convertModelOption);
        },
        [filterFun, result?.list]
    );

    return (
        <Select
            placeholder="请选择用于评估策略好坏的模型"
            dropdownRender={menu => (
                <>
                    {menu}
                    <Divider style={{margin: '8px 0'}} />
                    <NewModelButton
                        type="link"
                        onRefresh={refresh}
                        initValue={{modelType: ModelTypeEnum.ChatGLM}}
                    />
                </>
            )}
            options={modelsOptions}
            onChange={onChange}
            value={value}
        />
    );
};

export default QianFanModelSelectParam;
