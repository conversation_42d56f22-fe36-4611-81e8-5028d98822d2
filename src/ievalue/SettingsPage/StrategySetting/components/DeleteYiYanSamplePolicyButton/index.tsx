import {Button, message} from '@panda-design/components';
import {Popconfirm} from 'antd';
import {DeleteOutlined} from '@ant-design/icons';
import {useCallback, useState} from 'react';
import {apiEvaluatePolicyInfo, apiUpdatePolicyPost} from '@/api/ievalue/model';

export interface DeleteYiYanSamplePolicyButtonProps {
    onFinish: () => Promise<void>;
    policyID: number;
    currentMetric: string;
}

const DeleteYiYanSamplePolicyButton = ({
    onFinish,
    policyID,
    currentMetric,
}: DeleteYiYanSamplePolicyButtonProps) => {
    const [btnLoading, setBtnLoading] = useState(false);

    const handleFinish = useCallback(
        async () => {
            try {
                setBtnLoading(true);
                const policyInfo = await apiEvaluatePolicyInfo({
                    ID: policyID,
                });
                const metric = policyInfo.metric.filter(item => item.metric !== currentMetric);
                await apiUpdatePolicyPost({...policyInfo, metric});
                await onFinish();
                message.success('维度删除成功');
            } catch (error) {
                console.error('get apiEvaluatePolicyInfo fail');
            } finally {
                setBtnLoading(false);
            }
        },
        [currentMetric, onFinish, policyID]
    );

    return (
        <Popconfirm
            title="删除维度"
            placement="topRight"
            description="确定删除维度吗？"
            okText="删除"
            cancelText="取消"
            onConfirm={handleFinish}
        >
            <Button loading={btnLoading} type="text" icon={<DeleteOutlined />}>
                删除
            </Button>
        </Popconfirm>
    );
};

export default DeleteYiYanSamplePolicyButton;
