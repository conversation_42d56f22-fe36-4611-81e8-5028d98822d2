/* eslint-disable complexity */
/* eslint-disable max-lines */
import {EditOutlined, PlusOutlined} from '@ant-design/icons';
import {css} from '@emotion/css';
import {Form, FormInstance, Popconfirm, Radio, Space} from 'antd';
import {Button, message} from '@panda-design/components';
import {useBoolean} from 'huse';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {MetricItem} from '@/api/ievalue/case';
import {
    apiEvaluateYiYanToolCreate,
    apiEvaluateYiYanToolQuery,
    apiEvaluateYiYanToolUpdate,
    EvaluateYiYanToolItem,
    EvaluatorMetricItem,
} from '@/api/ievalue/evaluate';
import {CustomBoundary} from '@/components/ievalue/Boundary';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {
    EvaluateGranularityEnum,
    StrategyMetricGenreEnum,
    StrategyMetricGenreMap,
    StrategyMetricSelectTypeEnum,
    StrategyRunningModeEnum,
} from '@/constants/ievalue/evaluate';
import {useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {FormOnFinish} from '@/utils/ievalue/typing';
import {
    useEvaluateEvaluatorList,
    useEvaluateYiYanToolList,
} from '@/hooks/ievalue/settings';
import {formatPolicyTags} from '../UpdateStrategyButton';
import ChoicesFormItem from './ChoicesFormItem';
import EvaluateMetricsFormItem from './EvaluateMetricsFormItem';
import CategoryFormItem from './MetricFormItem/CategoryFormItem';
import DefinitionFormItem from './MetricFormItem/DefinitionFormItem';
import DescFormItem from './MetricFormItem/DescFormItem';
import {EvaluateIndicatorFormItem} from './MetricFormItem/EvaluateIndicatorFormItem';
import ExplainFormItem from './MetricFormItem/ExplainFormItem';
import InputTemplateFormItem from './MetricFormItem/InputTemplateFormItem';
import Jinja2FormItem from './MetricFormItem/Jinja2FormItem';
import MetricFormItem from './MetricFormItem/MetricFormItem';
import ParamsFormItem from './MetricFormItem/ParamsFormItem';
import {PromptVersionFormItem} from './MetricFormItem/PromptFormItem';
import ScoreStandardFormItem from './MetricFormItem/ScoreStandardFormItem';
import SelectTypeFormItem from './MetricFormItem/SelectTypeFormItem';
import SubMetricFormItem from './MetricFormItem/SubMetricFormItem';
import {SystemModelFormItem} from './MetricFormItem/SystemModelFormItem';
import TriggerValueFormItem from './MetricFormItem/TriggerValueFormItem';
import {VarColFormItem} from './MetricFormItem/VarColFormItem';
import YiYanToolIDFormItem from './MetricFormItem/YiYanToolIDFormItem';
import GbAccFormItem from './MetricFormItem/ExclusiveParamFormItem';
import MetricListFormItem from './MetricFormItem/MetricListFormItem';
import CreateNewPromptForm from './MetricFormItem/CreateNewPromptForm';
import NoScoringFormItem from './MetricFormItem/NeedScoreFormItem';
import MetricNoteFormItem from './MetricFormItem/MetricNoteFormItem';
import MetricTagFormItem from './MetricFormItem/MetricTagFormItem';
import NotRequiredFormItem from './MetricFormItem/NotRequiredFormItem';

interface UpdateMetricButtonProps {
    metricItem?: MetricItem;
    runningMode: StrategyRunningModeEnum;
    onFinish: (value: MetricItem, updateMetric?: string) => void;
}

interface UpdateMetricContentProps extends UpdateMetricButtonProps {
    onClose: () => void;
    form: FormInstance;
    isCreateNewPrompt: boolean;
    setIsCreateNewPrompt: (value: boolean) => void;
}

const DefalutInitValue: any = {
    promptVersionID: null,
    evaluatorID: null,
    template: '',
    desc: '',
    metric: '',
    explain: '',
    choices: [],
    metrics: [],
    metricList: [],
    params: JSON.stringify({}, null, 2),
    varColData: null,
    category: '',
    definition: '',
    scoreStandard: `根据【评价维度】中每个维度的评估结果，按以下规则进行打分：
    1.如果存在"0"分则输出"0"。
    2.如果不存在"0"分则统计"1"分和"2"分的数量，如果"1"分数量比"2"分更多则输出"1"，否则如果数量相等或者如果"2"分数量更多则输出"2"。`,
    dimensions: JSON.stringify(
        [
            {
                名称: '',
                定义: '',
                评价结果取值: '',
            },
        ],
        null,
        2
    ),
    inputTemplate: {
        问题: 'query',
        标准答案: 'ground_truth',
        待验证答案: 'response',
    },
    hasTag: false,
    tags: '',
    hasNote: false,
    isNoteRequired: false,
    noScoring: false,
    notRequired: false,
};

export const UpdateMetricContent = ({
    metricItem,
    runningMode,
    onFinish,
    onClose,
    form,
    isCreateNewPrompt,
    setIsCreateNewPrompt,
}: UpdateMetricContentProps) => {
    const spaceCode = useSpaceCodeSafe();
    const [, {refresh: yiYanToolListRefresh}] = useEvaluateYiYanToolList();
    const [evaluatorList] = useEvaluateEvaluatorList({spaceCode: 'PUBLIC'});

    const isEdit = useMemo(
        () => {
            return !!metricItem;
        },
        [metricItem]
    );

    const handleFinish: FormOnFinish<any> = useCallback(
        async formData => {
            try {
                let resultData = {...formData};
                let yiyanToolInfo = {} as EvaluateYiYanToolItem;
                if (runningMode === StrategyRunningModeEnum.YIYANDIY) {
                    resultData = {
                        ...formData,
                        dimensions: JSON.parse(formData.dimensions),
                        category: formData.metric,
                    };
                    const YiYanToolData = {
                        definition: resultData?.definition,
                        dimensions: resultData?.dimensions,
                        inputTemplate: resultData?.inputTemplate,
                        name: resultData?.metric,
                        category: resultData?.metric,
                        scoreStandard: resultData?.scoreStandard,
                        spaceCode,
                    };
                    if (isEdit) {
                        yiyanToolInfo = await apiEvaluateYiYanToolUpdate({
                            ...YiYanToolData,
                            ID: resultData?.ID,
                        });
                    } else {
                        yiyanToolInfo = await apiEvaluateYiYanToolCreate(
                            YiYanToolData
                        );
                    }
                    resultData.ID = yiyanToolInfo?.ID;
                    await yiYanToolListRefresh?.();
                }
                if (
                    runningMode === StrategyRunningModeEnum.MANUAL
                    && [
                        StrategyMetricSelectTypeEnum.SELECT,
                        StrategyMetricSelectTypeEnum.CHECKBOX,
                    ].includes(resultData?.selectType)
                ) {
                    // 如果为人工评估，且选择类型为下拉框和多选，且不需要评分，则修改所有分值为索引值
                    if (resultData?.noScoring) {
                        resultData.choices = resultData.choices.map(
                            (item: any, index: number) => ({
                                ...item,
                                score: index,
                            })
                        );
                    }
                    if (resultData?.hasTag) {
                        resultData.tags = formatPolicyTags(resultData.tags);
                    }
                }
                onFinish(resultData, metricItem?.metric);
                message.success(`${isEdit ? '修改' : '添加'}成功`);
                onClose();
            } catch (e) {
                if (e instanceof Error) {
                    message.error(
                        `${isEdit ? '修改' : '添加'}失败，${e.message ?? ''}`
                    );
                }
            }
        },
        [
            isEdit,
            metricItem?.metric,
            onClose,
            onFinish,
            runningMode,
            spaceCode,
            yiYanToolListRefresh,
        ]
    );

    useEffect(
        () => {
            form.resetFields();
        },
        [metricItem, form]
    );

    useEffect(
        () => {
            const getYiYanToolInfo = async () => {
                if (
                    metricItem
                && runningMode === StrategyRunningModeEnum.YIYANDIY
                ) {
                    const yiYanToolInfo = await apiEvaluateYiYanToolQuery({
                        toolID: metricItem?.ID,
                    });
                    if (yiYanToolInfo) {
                        form.setFieldsValue({
                            ...yiYanToolInfo,
                            ID: metricItem.ID,
                            dimensions: JSON.stringify(
                                yiYanToolInfo?.dimensions,
                                null,
                                2
                            ),
                        });
                    }
                }
            };
            getYiYanToolInfo();
        },
        [metricItem, form, runningMode]
    );

    const handleChangeValue = useCallback(
        // eslint-disable-next-line max-statements
        (changedValues: any, values: any) => {
            const genreData = changedValues?.genre;
            const metricData = changedValues?.metric;
            const promptVersionID = changedValues?.promptVersionID;
            const evaluatorID = changedValues?.evaluatorID;
            const evaluatorMetrics: EvaluatorMetricItem[] =
                changedValues?.metrics;
            const metricList: string[] = changedValues?.metricList;
            const metricListValue: string[] = values?.metricList;
            if (genreData) {
                form.setFieldsValue(DefalutInitValue);
            }
            if (metricData) {
                form.setFieldsValue({
                    desc: metricData,
                });
            }
            if (promptVersionID) {
                form.setFieldsValue({
                    ...DefalutInitValue,
                    promptVersionID,
                });
            }
            if (evaluatorID) {
                const evaluatorItem = evaluatorList?.find(
                    item => item.ID === evaluatorID
                );
                // 语音检测维度默认勾选所有指标
                let metrics: EvaluatorMetricItem[] = [];
                let desc = evaluatorItem?.funName;
                let metric = evaluatorItem?.funName;
                if (
                    evaluatorItem?.granularity
                        === EvaluateGranularityEnum.MULTI
                    && evaluatorItem?.funName === 'AUDIO'
                ) {
                    metrics = evaluatorItem?.metrics;
                    metric = metrics?.map(item => item.metric)?.join(',');
                    desc = metric;
                }
                form.setFieldsValue({
                    ...DefalutInitValue,
                    evaluatorID,
                    metrics,
                    desc,
                    metric,
                    explain: evaluatorItem?.note,
                });
            }
            if (evaluatorMetrics) {
                const metric = evaluatorMetrics
                    ?.map(item => item.metric)
                    ?.join(',');
                form.setFieldsValue({
                    desc: metric,
                    metric,
                });
            }
            if (metricList) {
                const metric = metricListValue?.join(',');
                form.setFieldsValue({
                    desc: metric,
                    metric,
                });
            }
        },
        [evaluatorList, form]
    );

    return (
        <div
            className={css`
                width: ${runningMode === StrategyRunningModeEnum.YIYANDIY
            ? '900px'
            : '640px'};
                max-height: 530px;
                overflow: auto;
                ::-webkit-scrollbar {
                    display: none;
                }
            `}
        >
            {!isCreateNewPrompt && (
                <Form
                    form={form}
                    onFinish={handleFinish}
                    initialValues={
                        metricItem
                            ? {
                                ...metricItem,
                                dimensions: JSON.stringify(
                                    metricItem?.dimensions,
                                    null,
                                    2
                                ),
                                tags: metricItem?.tags?.join('\n') ?? '',
                            }
                            : {
                                ...DefalutInitValue,
                                genre:
                                      runningMode
                                      === StrategyRunningModeEnum.MANUAL
                                          ? StrategyMetricGenreEnum.MANUAL
                                          : runningMode
                                            === StrategyRunningModeEnum.YIYANDIY
                                              ? StrategyMetricGenreEnum.YIYANDIY
                                              : StrategyMetricGenreEnum.ALGORITHM,
                                selectType:
                                      StrategyMetricSelectTypeEnum.SELECT,
                                createNewPrompt: false,
                            }
                    }
                    layout="inline"
                    labelCol={{flex: '85px'}}
                    labelAlign="left"
                    onValuesChange={handleChangeValue}
                >
                    <FlexLayout
                        gap={8}
                        direction="column"
                        style={{width: '100%'}}
                    >
                        <Form.Item
                            name="genre"
                            label="维度类型"
                            style={{width: '100%'}}
                            rules={[{required: true}]}
                            hidden={
                                [
                                    StrategyRunningModeEnum.MANUAL,
                                    StrategyRunningModeEnum.YIYANDIY,
                                ].includes(runningMode) || isEdit
                            }
                        >
                            <Radio.Group>
                                <Radio value={StrategyMetricGenreEnum.MANUAL}>
                                    {StrategyMetricGenreMap.MANUAL}
                                </Radio>
                                {/* <Radio value={StrategyMetricGenreEnum.API}>
                            {StrategyMetricGenreMap.API}
                        </Radio> */}
                                <Radio
                                    value={StrategyMetricGenreEnum.ALGORITHM}
                                >
                                    {StrategyMetricGenreMap.ALGORITHM}
                                </Radio>
                                <Radio
                                    value={StrategyMetricGenreEnum.AIGRADING}
                                >
                                    {StrategyMetricGenreMap.AIGRADING}
                                </Radio>
                            </Radio.Group>
                        </Form.Item>
                        <SelectTypeFormItem runningMode={runningMode} />
                        <CustomBoundary.FullSizeLoading>
                            <EvaluateIndicatorFormItem />
                            <EvaluateMetricsFormItem />
                            <GbAccFormItem />
                            <PromptVersionFormItem
                                setIsCreateNewPrompt={setIsCreateNewPrompt}
                            />
                            <SystemModelFormItem />
                            <FlexLayout gap={4}>
                                <DescFormItem />
                                <CategoryFormItem isEdit={isEdit} />
                                <MetricFormItem isEdit={isEdit} />
                            </FlexLayout>
                            <MetricListFormItem />
                            <TriggerValueFormItem />
                            <DefinitionFormItem />
                            <Form.Item label="评估配置" hidden={runningMode !== StrategyRunningModeEnum.MANUAL}>
                                <Space wrap>
                                    <NotRequiredFormItem />
                                    <NoScoringFormItem />
                                    <MetricNoteFormItem />
                                </Space>
                                <MetricTagFormItem />
                            </Form.Item>
                            <ChoicesFormItem />
                            <ParamsFormItem />
                            <SubMetricFormItem />
                            <VarColFormItem />
                            <ScoreStandardFormItem />
                            <Jinja2FormItem />
                            <ExplainFormItem />
                            <InputTemplateFormItem />
                            <YiYanToolIDFormItem />
                        </CustomBoundary.FullSizeLoading>
                    </FlexLayout>
                </Form>
            )}
        </div>
    );
};

export const UpdateMetricButton = ({
    metricItem,
    runningMode,
    onFinish,
}: UpdateMetricButtonProps) => {
    const [open, {off, on}] = useBoolean();
    const [form] = Form.useForm();
    const [isCreateNewPrompt, setIsCreateNewPrompt] = useState(false);

    const handleOk = useCallback(
        () => {
            form.submit();
        },
        [form]
    );

    const handleApplyPrompt = useCallback(
        (promptVersionID: number) => {
            form.setFieldValue(['promptVersionID'], promptVersionID);
        },
        [form]
    );

    const handleOpen = useCallback(
        () => {
            form.resetFields();
            on();
        },
        [form, on]
    );

    return (
        <Popconfirm
            icon={null}
            placement="right"
            okButtonProps={{
                style: {display: isCreateNewPrompt ? 'none' : undefined},
            }}
            cancelButtonProps={{
                style: {display: isCreateNewPrompt ? 'none' : undefined},
            }}
            description={
                <CustomBoundary.Loading>
                    <UpdateMetricContent
                        runningMode={runningMode}
                        metricItem={metricItem}
                        onFinish={onFinish}
                        onClose={off}
                        form={form}
                        isCreateNewPrompt={isCreateNewPrompt}
                        setIsCreateNewPrompt={setIsCreateNewPrompt}
                    />
                    {isCreateNewPrompt && (
                        <CreateNewPromptForm
                            setIsCreateNewPrompt={setIsCreateNewPrompt}
                            form={form}
                            onApplyPrompt={handleApplyPrompt}
                        />
                    )}
                </CustomBoundary.Loading>
            }
            title={null}
            open={open}
            onConfirm={handleOk}
            onCancel={off}
            destroyTooltipOnHide
        >
            <Button
                type="text"
                onClick={handleOpen}
                icon={metricItem ? <EditOutlined /> : <PlusOutlined />}
            >
                {metricItem ? '编辑' : '新增'}
            </Button>
        </Popconfirm>
    );
};
