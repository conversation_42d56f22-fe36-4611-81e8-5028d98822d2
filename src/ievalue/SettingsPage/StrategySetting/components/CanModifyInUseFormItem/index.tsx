import {Form, Switch} from 'antd';
import {StrategyRunningModeEnum} from '@/constants/ievalue/evaluate';

const CanModifyInUseFormItem = () => {
    const runningMode = Form.useWatch('runningMode');

    return (
        <Form.Item
            label="允许实时修改标签列表"
            name="canModifyInUse"
            tooltip="开启后在评估任务中可以实时修改可选择的标签列表"
            hidden={runningMode !== StrategyRunningModeEnum.MANUAL}
        >
            <Switch />
        </Form.Item>
    );

};
export default CanModifyInUseFormItem;
