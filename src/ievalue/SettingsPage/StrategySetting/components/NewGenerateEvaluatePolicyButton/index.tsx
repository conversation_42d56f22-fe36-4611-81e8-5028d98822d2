import {Button, ButtonProps, Modal, message} from '@panda-design/components';
import {useBoolean} from 'huse';
import {Form, Space, Typography} from 'antd';
import {useCallback, useState} from 'react';
import {Link} from 'react-router-dom';
import {CustomBoundary} from '@/components/ievalue/Boundary';
import {FormOnFinish} from '@/utils/ievalue/typing';
import {apiEvaluatePolicyGenerate} from '@/api/ievalue/evaluate';
import {apiEvaluatePolicyInfo} from '@/api/ievalue/model';
import GenerateEvaluatePolicyContent from './GenerateEvaluatePolicyContent';
export interface GenerateEvaluatePolicyProps {
    onFinish: () => Promise<void>;
    policyID?: number;
    currentMetric?: string;
}

type GenerateEvaluatePolicyButtonProps = GenerateEvaluatePolicyProps &
    ButtonProps;

const NewGenerateEvaluatePolicyButton = ({
    onFinish,
    policyID,
    currentMetric,
    title,
    ...props
}: GenerateEvaluatePolicyButtonProps) => {
    const [open, {off, on}] = useBoolean();
    const [loading, setLoading] = useState(false);
    const [btnLoading, setBtnLoading] = useState(false);
    const [form] = Form.useForm();

    const handleOk = useCallback(
        () => {
            form.submit();
        },
        [form]
    );

    const handleOpen = useCallback(
        async () => {
            form.resetFields();
            if (policyID) {
                try {
                    setBtnLoading(true);
                    const policyInfo = await apiEvaluatePolicyInfo({
                        ID: policyID,
                    });
                    form.setFieldsValue({
                        policy: {name: policyInfo.name},
                        task: {
                            evalModelID: policyInfo?.evalModelID,
                            policyID,
                            colMetric: policyInfo?.metric?.map(item => ({
                                metric: item.metric,
                                initStd: item.rule ?? '',
                                notOptimized: currentMetric
                                    ? item.metric !== currentMetric
                                    : false,
                            })) || [{}],
                        },
                    });
                } catch (error) {
                    console.error('get apiEvaluatePolicyInfo fail');
                } finally {
                    setBtnLoading(false);
                }
            }
            on();
        },
        [currentMetric, form, on, policyID]
    );

    const handleFinish: FormOnFinish<any> = useCallback(
        async formData => {
            setLoading(true);
            try {
                const colMetric = formData?.task?.colMetric?.reduce(
                    (result: any[], i: any) => {
                        if (!i?.notOptimized) {
                            const {colNote, colScore, initStd, metric} = i;
                            result.push({colNote, colScore, initStd, metric});
                        }
                        return result;
                    },
                    []
                );
                await apiEvaluatePolicyGenerate({
                    ...formData,
                    task: {
                        ...formData.task,
                        colMetric,
                    },
                });
                await onFinish();
                off();
            } catch (e) {
                if (e instanceof Error) {
                    message.error(`生成新的维度失败，${e.message ?? ''}`);
                }
            } finally {
                setLoading(false);
            }
        },
        [off, onFinish]
    );

    return (
        <>
            <Button loading={btnLoading} onClick={handleOpen} {...props}>
                {title}
            </Button>
            {open && (
                <Modal
                    title={
                        <Space size={16}>
                            <Typography.Title level={3}>
                                {title}
                            </Typography.Title>
                            <Link
                                target="_blank"
                                to="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/z1TN_5BM97/haIJtBaso2dGlR"
                            >
                                <Button type="link">说明文档</Button>
                            </Link>
                        </Space>
                    }
                    width="1200px"
                    open={open}
                    onCancel={off}
                    onOk={handleOk}
                    maskClosable={false}
                    okText="生成"
                    okButtonProps={{loading}}
                >
                    <CustomBoundary.Loading>
                        <GenerateEvaluatePolicyContent
                            onFinish={handleFinish}
                            policyID={policyID}
                            form={form}
                        />
                    </CustomBoundary.Loading>
                </Modal>
            )}
        </>
    );
};

export default NewGenerateEvaluatePolicyButton;
