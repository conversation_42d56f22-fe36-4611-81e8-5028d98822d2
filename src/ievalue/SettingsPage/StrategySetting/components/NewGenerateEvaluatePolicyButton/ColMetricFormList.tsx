import {DeleteOutlined, PlusOutlined} from '@ant-design/icons';
import {Button} from '@panda-design/components';
import {
    Card,
    Flex,
    Form,
    Input,
    Popconfirm,
    Select,
    Space,
    Switch,
} from 'antd';
import {FormItemParamProps} from '@/types/common/form';

interface ColMetricFormListProps {
    mapSelectOptions: any;
}

const NotOptimizedSwitch = ({
    value,
    onChange,
}: FormItemParamProps<boolean>) => {
    return (
        <Space>
            <Switch
                checked={!value}
                onChange={checked => onChange?.(!checked)}
            />
            <span>优化此维度</span>
        </Space>
    );
};

const ColMetricFormList = ({mapSelectOptions}: ColMetricFormListProps) => {
    const form = Form.useFormInstance();
    const colMetricList: any[] = Form.useWatch(['task', 'colMetric']) || [];
    const taskPolicyID: number = Form.useWatch(['task', 'policyID']);

    return (
        <Form.Item label="评估维度" required>
            <Form.List name={['task', 'colMetric']}>
                {(fields, {add, remove}) => (
                    <Flex vertical gap={8}>
                        {fields.map((field, index) => {
                            const notOptimized =
                                !!colMetricList?.[field.name]?.notOptimized;
                            return (
                                <Card
                                    styles={{
                                        header: {
                                            background:
                                                'linear-gradient(45deg, #f5f7fa, transparent)',
                                        },
                                    }}
                                    size="small"
                                    title={
                                        <Space size={24}>
                                            <span>{`维度 ${index + 1}`}</span>
                                            {!!taskPolicyID && (
                                                <Form.Item
                                                    name={[
                                                        field.name,
                                                        'notOptimized',
                                                    ]}
                                                    noStyle
                                                >
                                                    <NotOptimizedSwitch />
                                                </Form.Item>
                                            )}
                                        </Space>
                                    }
                                    key={field.key}
                                    extra={
                                        <Popconfirm
                                            title="确认删除当前维度吗"
                                            onConfirm={() => remove(field.name)}
                                        >
                                            <Button
                                                type="text"
                                                disabled={fields.length === 1}
                                                disabledReason="至少需要一个维度"
                                                icon={<DeleteOutlined />}
                                            />
                                        </Popconfirm>
                                    }
                                >
                                    <Flex gap={8}>
                                        <Form.Item
                                            style={{flex: 1}}
                                            labelCol={{flex: '100px'}}
                                            label="样本列"
                                            name={[field.name, 'colScore']}
                                            rules={[
                                                {
                                                    required: !notOptimized,
                                                    message: '请选择样本列',
                                                },
                                            ]}
                                        >
                                            <Select
                                                disabled={notOptimized}
                                                allowClear
                                                options={mapSelectOptions}
                                                placeholder="请选择示例文件中的维度列"
                                                onChange={value => {
                                                    if (value) {
                                                        form.setFieldValue(
                                                            [
                                                                'task',
                                                                'colMetric',
                                                                field.name,
                                                                'metric',
                                                            ],
                                                            value
                                                        );
                                                    }
                                                }}
                                            />
                                        </Form.Item>
                                        <Form.Item
                                            style={{flex: 1}}
                                            labelCol={{flex: '100px'}}
                                            label="维度名称"
                                            name={[field.name, 'metric']}
                                            required
                                            rules={[
                                                {
                                                    validator() {
                                                        if (notOptimized) {
                                                            return Promise.resolve();
                                                        }
                                                        const metric =
                                                            form.getFieldValue([
                                                                'task',
                                                                'colMetric',
                                                                field.name,
                                                                'metric',
                                                            ]);

                                                        const otherItems =
                                                            colMetricList.filter(
                                                                (_, i) =>
                                                                    i !== index
                                                            );
                                                        if (!metric) {
                                                            return Promise.reject(
                                                                new Error(
                                                                    '请输入维度名称'
                                                                )
                                                            );
                                                        }
                                                        if (
                                                            otherItems.some(
                                                                item =>
                                                                    item.metric
                                                                    === metric
                                                            )
                                                        ) {
                                                            return Promise.reject(
                                                                new Error(
                                                                    '维度名称不能重复'
                                                                )
                                                            );
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                },
                                            ]}
                                        >
                                            <Input disabled={notOptimized} />
                                        </Form.Item>
                                    </Flex>
                                    <Form.Item
                                        labelCol={{flex: '100px'}}
                                        label="备注"
                                        name={[field.name, 'colNote']}
                                    >
                                        <Select
                                            allowClear
                                            options={mapSelectOptions}
                                            placeholder="请选择示例文件该样本列的备注"
                                            disabled={notOptimized}
                                        />
                                    </Form.Item>
                                    <Form.Item
                                        labelCol={{flex: '100px'}}
                                        label="初始标准"
                                        name={[field.name, 'initStd']}
                                    >
                                        <Input.TextArea
                                            rows={2}
                                            placeholder="请输入当前评估维度的初始标准，如三分制、五分制等"
                                            disabled={notOptimized}
                                        />
                                    </Form.Item>
                                </Card>
                            );
                        })}
                        <Button
                            icon={<PlusOutlined />}
                            type="text"
                            onClick={() => add({})}
                        >
                            新增评估维度
                        </Button>
                    </Flex>
                )}
            </Form.List>
        </Form.Item>
    );
};

export default ColMetricFormList;
