import {useMemo, useCallback} from 'react';
import {useRealFiles, removeRealFiles} from '@/regions/dataset/realFiles';
import {useTempFiles, removeTempFiles} from '@/regions/dataset/tempFiles';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {TempFile} from '@/types/icode/datasetFileEdit';
import {FileNode} from '@/types/icode/repo';

import {useCurrentRefName} from '@/hooks/icode/current/useCurrentRefName';
import {useNavigateReplace} from '@/hooks/icode/common/useNavigate';
import {getFileUrl} from '@/utils/icode/route/getFileUrl';
import {useCurrentPath} from '@/hooks/icode/current/useCurrentPath';

export const useAllOpenedFileNodes = () => {
    const repoName = useCurrentRepoName();
    const realFileNodes = useRealFiles(repoName);
    const tempFiles = useTempFiles(repoName);
    const navigateReplace = useNavigateReplace();
    const refName = useCurrentRefName();
    const openedFilePath = useCurrentPath();

    const allFileNodes = useMemo(
        () => {
            // 这里是把临时文件转换成 FileNode 类型，然后和真实的 fileNodes 合并到一起
            const tempFilesToFileNodes = tempFiles.map((item: TempFile) => {
                const {oid, name, path, fileDir} = item;
                return {
                    lfsOid: oid,
                    name,
                    path,
                    isTempFile: true,
                    type: 'BLOB',
                    fileDir,
                } as FileNode;
            });

            return [...realFileNodes, ...tempFilesToFileNodes];
        },
        [realFileNodes, tempFiles]
    );

    const closeFileNode = useCallback(
        (path: string, isTempFile: boolean) => {
            isTempFile ? removeTempFiles(repoName, path) : removeRealFiles(repoName, path);

            if (openedFilePath === path) {
                const index = allFileNodes.findIndex(item => item.path === path);
                allFileNodes.splice(index, 1);
                const newFileNodes = [...allFileNodes];
                const newFileNode = newFileNodes[index] ?? newFileNodes[newFileNodes.length - 1] ?? '';
                if (newFileNode) {
                    const url = getFileUrl({
                        repoName,
                        type: 'blob',
                        encodedRefName: encodeURIComponent(refName),
                        path: newFileNode.path,
                    });

                    navigateReplace(url);
                }
                else {
                    const rootUrl = getFileUrl({
                        repoName,
                        type: 'tree',
                        encodedRefName: encodeURIComponent(refName),
                    });

                    navigateReplace(rootUrl);
                }
            }
        },
        [allFileNodes, navigateReplace, openedFilePath, refName, repoName]
    );

    return {
        allFileNodes,
        closeFileNode,
    };
};
