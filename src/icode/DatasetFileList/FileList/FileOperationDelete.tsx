import {useCallback} from 'react';
import {message} from '@panda-design/components';
import {useBoolean} from 'huse';
import {Modal} from '@panda-design/components';
import {Popover, Form, Input} from 'antd';
import {EllipsisOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import {removeCurrentFileChildrenNode, loadFileNode} from '@/regions/icode/fileTree';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {useCurrentBranchType} from '@/hooks/icode/current/useCurrentBranchType';
import {useCurrentPath} from '@/hooks/icode/current/useCurrentPath';
import {useCurrentRefName} from '@/hooks/icode/current/useCurrentRefName';
import {apiPostDeleteLfs} from '@/api/icode/dataset';
import {usePremissions} from '@/regions/icode/comateStack';
import NoPermissionTip from '@/components/icode/ComateStack/NoPermissionTip';
import {useIsDataset} from '@/providers/icode/IsDatasetProvider';
import {useAllOpenedFileNodes} from '../hooks/useAllOpenedFileNodes';

const MenuLayout = styled.div`
    background: var(--color-gray-1);
`;

const MenuItem = styled.div`
    font-size: 14px;
    height: 20px;
    line-height: 20px;
    cursor: pointer;
    padding-bottom: 12px;
`;

const ContentLayout = styled.div`
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 20px;
`;

interface FormData {
    message: string;
}

interface Props {
    path: string;
}

const FileOperationDelete = ({path}: Props) => {
    const [form] = Form.useForm();
    const repo = useCurrentRepoName();
    const branch = useCurrentRefName();
    const filePath = useCurrentPath();
    const branchType = useCurrentBranchType();
    const [modalOpen, {on: showModal, off: hideModal}] = useBoolean(false);
    const [loading, {on: showLoading, off: hideLoading}] = useBoolean(false);
    const hasDeleteDataSetPermission = usePremissions(['DATASET_DELETE']);
    const isDataset = useIsDataset();
    const {closeFileNode} = useAllOpenedFileNodes();

    const handleOnClose = useCallback(
        () => {
            hideModal();
            form.resetFields();
        },
        [form, hideModal]
    );

    const handleOnOk = useCallback(
        async (values: FormData) => {
            showLoading();
            try {
                await apiPostDeleteLfs({repo, branch, paths: [path], message: values.message});
                message.success('删除成功');
                const removeParams = {
                    repo,
                    commit: branch,
                    parentPath: filePath,
                    childrenPath: path,
                };

                closeFileNode(path, false);
                removeCurrentFileChildrenNode(removeParams);
                loadFileNode({
                    repo,
                    commit: branch,
                    type: branchType,
                    path: filePath,
                    forceUpdate: true,
                    withFileSize: true,
                    isDataset,
                });

                hideModal();
                hideLoading();
            }
            catch (e) {
                hideLoading();
                message.error(e.message);
            }
        },
        [branch, branchType, filePath, hideLoading, hideModal, isDataset, path, repo, showLoading, closeFileNode]
    );

    return (
        <>
            <Popover
                placement="bottomRight"
                mouseEnterDelay={0.2}
                content={
                    hasDeleteDataSetPermission
                        ? (
                            <MenuLayout>
                                <MenuItem onClick={showModal}>
                                    删除
                                </MenuItem>
                            </MenuLayout>
                        )
                        : NoPermissionTip
                }
            >
                <ContentLayout>
                    <EllipsisOutlined />
                </ContentLayout>
            </Popover>
            <Modal
                title="删除文件"
                open={modalOpen}
                onCancel={handleOnClose}
                width={600}
                maskClosable={false}
                onOk={form.submit}
                okButtonProps={{loading}}
                destroyOnClose
            >
                <Form<FormData> form={form} initialValues={{message: `delete ${path}`}} onFinish={handleOnOk}>
                    <Form.Item
                        label="提交信息"
                        name="message"
                        rules={[{required: true, message: '请输入提交信息'}]}
                        extra="确定后将删除该文件"
                    >
                        <Input.TextArea rows={3} maxLength={200} />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};

export default FileOperationDelete;
