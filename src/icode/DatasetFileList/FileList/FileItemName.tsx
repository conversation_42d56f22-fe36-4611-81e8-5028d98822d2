import styled from '@emotion/styled';
import {css} from '@emotion/css';
import {ellipsis, Text} from '@panda-design/components';
import {Link} from '@/utils/icode/link';
import {warnPercent} from '@/utils/icode/once';
import {FileNode} from '@/types/icode/repo';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {useCurrentRefName} from '@/hooks/icode/current/useCurrentRefName';
import {useCurrentPath} from '@/hooks/icode/current/useCurrentPath';
import {IconFolderClose} from '@/icons/dataset';
import {getDatasetFileIcon} from '@/utils/dataset/fils';
import {getFileUrl} from '@/utils/icode/route/getFileUrl';
import {useDrawerHandler} from './useDrawerHandler';

const NameSpan = styled.span`
    flex: 1;
    color: black;
    font-weight: 500;
    margin-right: 6px;

    > a {
        color: black;
    }
`;

const iconCss = css`
    color: var(--color-brand-5);
    margin-right: 5px;
    vertical-align: -.15em;
`;

const StyledFileName = styled.span<{active: boolean}>`
    cursor: pointer;
    color: ${props => (props.active ? 'var(--color-brand-6)' : undefined)};
`;

const getIsUrlValid = (url: string) => {
    if (!url) {
        return false;
    }

    try {
        const decodedUrl = decodeURIComponent(url);
        if (decodedUrl.includes('%')) {
            return false;
        }
        return true;
    }
    catch (e) {
        return false;
    }
};

interface FileNameProps {
    file: FileNode;
    preFilePath: string;
    nextFilePath: string;
    isFirst: boolean;
    isLast: boolean;
}

const FileName = ({file, preFilePath, nextFilePath}: FileNameProps) => {
    const repoName = useCurrentRepoName();
    const refName = useCurrentRefName();
    const filePath = useCurrentPath();
    const {name, type, path} = file;
    const url = getFileUrl({
        repoName,
        type: type.toLowerCase(),
        encodedRefName: encodeURIComponent(refName),
        path,
    });

    const isUrlValid = getIsUrlValid(url);

    if (!isUrlValid) {
        warnPercent();
    }

    const {
        handleOpenDrawer,
    } = useDrawerHandler({file, preFilePath, nextFilePath});
    const FileIcon = getDatasetFileIcon(name);
    return (
        <NameSpan className={ellipsis}>
            {type === 'BLOB' ? <FileIcon className={iconCss} /> : <IconFolderClose className={iconCss} />}
            {type === 'TREE' ? <Link to={url}>{name}</Link> : (
                <StyledFileName onClick={handleOpenDrawer} active={filePath === String(path)}>
                    <Text style={{maxWidth: '600px'}} ellipsis={{tooltip: true}}>{name}</Text>
                </StyledFileName>
            )}
        </NameSpan>
    );
};

export default FileName;
