import {useEffect} from 'react';
import {loadFileRenderProperties} from '@/regions/icode/dataset/fileRenderPropertiesRegion';
import {ParamsGetFileRenderProperties} from '@/types/comatestack/dataset';

export const useTempFileRenderPropertiesEffect = (
    tempFileSessionId: string,
    repo: string,
    path: string,
    oid: string
) => {
    useEffect(
        () => {
            if (tempFileSessionId) {
                const params = {
                    dataset: repo,
                    path,
                    oid,
                    sessionId: tempFileSessionId,
                } as ParamsGetFileRenderProperties;

                loadFileRenderProperties(params);
            }
        },
        [oid, path, repo, tempFileSessionId]
    );
};
