import {createContext, useContext} from 'react';
import {PublishedVersion} from '@/types/icode/dataset';
import {FileNode} from '@/types/icode/repo';

interface ContextValue {
    isLoadingVersionList: boolean;
    selectedVersion: PublishedVersion;
    draftVersionList: PublishedVersion[];
    draftSessionId: string;
    fileNode: FileNode;
    onSelectVersion: (versionId: number) => void;
}

export const FileNodeContext = createContext<ContextValue>(
    {
        isLoadingVersionList: false,
        selectedVersion: null,
        draftVersionList: [],
        draftSessionId: '',
        fileNode: {} as FileNode,
        onSelectVersion: () => {},
    }
);

export const useFileNodeContext = () => {
    return useContext(FileNodeContext);
};
