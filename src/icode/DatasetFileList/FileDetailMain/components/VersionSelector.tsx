import {useMemo, useState} from 'react';
import {Divider, Dropdown, Tooltip} from 'antd';
import type {MenuProps} from 'antd';
import {DownOutlined, LoadingOutlined, UpOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import {Text} from '@panda-design/components';
import {bigFileSizeTransform} from '@baidu/devops-components/es/DatasetFileTreeSelect/bigFileSizeTransform';
import {UserAvatar} from '@/design/icode/Avatar/UserAvatar';
import {PublishedVersion} from '@/types/icode/dataset';
import {formatTimeAgo} from '@/utils/common/time';
import {useFileEditStatus} from '@/regions/icode/dataset/fileEditStatusRegion';
import {getFileTotalRows, useFileTotalRows} from '@/regions/icode/dataset/fileTotalRowsRegion';
import {useFileNodeContext} from '../context/FileNodeContext';

const VersionLabel = styled.span`
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 100%;
    letter-spacing: 0%;
    color: #666666;
`;

const ItemContainer = styled.div`
    display: flex;
    font-size: 14px;
    gap: 9px;
`;

const MenuItemContainer = styled.div`
    display: flex;
    font-size: 14px;
    align-items: center;
    justify-content: space-between;

    .left, .right {
        display: flex;
        align-items: center;
        gap: 9px;
    }
`;

const VersionSelector = () => {
    const {fileNode, selectedVersion, draftVersionList, isLoadingVersionList, onSelectVersion} = useFileNodeContext();
    const {path} = fileNode;
    const {version, createdBy, id, isDraft, oid} = selectedVersion ?? {} as PublishedVersion;
    const [isOpen, setIsOpen] = useState(false);
    const {editSource} = useFileEditStatus(path);
    const selectedVersionTotalRows = useFileTotalRows({
        path,
        oid,
    });

    const versionItems: MenuProps['items'] = useMemo(
        () => {
            return draftVersionList.map(item => {
                const {id, version, createdBy, size, description, createdAt, isDraft, oid, path} = item;
                const totalRows = getFileTotalRows({
                    path,
                    oid,
                });

                return {
                    label: (
                        <Tooltip
                            title={isDraft ? '未发布的草稿' : description}
                            placement="right"
                            arrow={false}
                        >
                            <MenuItemContainer>
                                <div className="left">
                                    <UserAvatar
                                        iconSize={20}
                                        username={createdBy}
                                    />
                                    <Text ellipsis>
                                        {version}{isDraft && editSource ? (`（源于${editSource}）`) : ''}
                                    </Text>
                                </div>
                                <div className="right">
                                    <div>
                                        {formatTimeAgo(createdAt)}
                                        {
                                            size && (
                                                <>
                                                    <Divider type="vertical" />
                                                    {bigFileSizeTransform(size)}
                                                </>
                                            )
                                        }
                                        {
                                            totalRows && (
                                                <>
                                                    <Divider type="vertical" />
                                                    {totalRows}行
                                                </>
                                            )
                                        }
                                    </div>
                                </div>
                            </MenuItemContainer>
                        </Tooltip>
                    ),
                    key: id,
                };
            });
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [editSource, draftVersionList, selectedVersionTotalRows]
    );

    return (
        <>
            <VersionLabel>版本：</VersionLabel>
            {
                (isLoadingVersionList && !path)
                    ? (<LoadingOutlined />)
                    : (
                        <Dropdown
                            menu={{
                                items: versionItems,
                                style: {
                                    maxHeight: '200px',
                                    overflow: 'auto',
                                    width: '298px',
                                    padding: '10px',
                                    scrollbarWidth: 'none',
                                },
                                selectedKeys: [id?.toString()],
                                onClick: ({key}) => onSelectVersion(Number(key)),
                            }}
                            getPopupContainer={trigger => trigger.parentNode as HTMLElement}
                            onOpenChange={setIsOpen}
                        >
                            <ItemContainer>
                                <UserAvatar
                                    iconSize={20}
                                    username={createdBy}
                                />
                                <Text ellipsis>{version}{isDraft && editSource ? (`（源于${editSource}）`) : ''}</Text>
                                {
                                    isOpen ? <UpOutlined /> : <DownOutlined />
                                }
                            </ItemContainer>
                        </Dropdown>
                    )
            }
        </>
    );
};

export default VersionSelector;
