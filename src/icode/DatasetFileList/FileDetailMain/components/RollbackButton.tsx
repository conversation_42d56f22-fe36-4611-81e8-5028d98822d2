import {useCallback} from 'react';
import {Button, message, Modal, Text} from '@panda-design/components';
import {apiPostRollbackVersion} from '@/api/icode/dataset';
import {PublishedVersion} from '@/types/icode/dataset';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {loadVersionList} from '@/regions/icode/dataset/filePreviewVersionRegion';
import {useFileNodeContext} from '../context/FileNodeContext';

const RollbackButton = () => {
    const {fileNode, selectedVersion, onSelectVersion} = useFileNodeContext();
    const {path, lfsOid} = fileNode;
    const {version} = selectedVersion ?? {} as PublishedVersion;
    const repo = useCurrentRepoName();
    const handleRollback = useCallback(
        async () => {
            Modal.confirm({
                title: '回滚确认',
                content: (
                    <>
                        <Text>此操作将以{version}文件内容生成新版本，确认回滚？</Text>
                    </>
                ),
                async onOk() {
                    try {
                        const params = {
                            datasetRepo: repo,
                            branch: 'master', // 使用默认分支
                            rollbackVersion: version,
                            filePath: path,
                        };

                        const success = await apiPostRollbackVersion(params);
                        if (success) {
                            message.success('回滚成功');
                            await loadVersionList({path, oid: lfsOid, repo});
                            onSelectVersion(null);
                        }
                        else {
                            message.error('回滚失败');
                        }
                    }
                    catch (e) {
                        message.error('回滚失败');
                    }
                },
            });
        },
        [path, lfsOid, onSelectVersion, repo, version]
    );

    return (
        <Button
            onClick={handleRollback}
        >
            回滚至此版本
        </Button>
    );
};

export default RollbackButton;
