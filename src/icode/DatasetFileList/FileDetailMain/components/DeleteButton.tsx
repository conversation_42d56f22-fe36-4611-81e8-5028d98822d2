import {useCallback} from 'react';
import {DeleteOutlined} from '@ant-design/icons';
import {Button, message, Modal, Text} from '@panda-design/components';
import {resetFileEditDraft} from '@/regions/icode/dataset/fileEditCommonRegion';
import {loadFileEditStatus} from '@/regions/icode/dataset/fileEditStatusRegion';
import {apiPostDeleteDatasetFileDraft} from '@/api/icode/datasetFileEdit';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {useFileNodeContext} from '../context/FileNodeContext';
import {useAllOpenedFileNodes} from '../../hooks/useAllOpenedFileNodes';

interface Props {
    isTempFile?: boolean;
}

const DeleteButton = ({isTempFile}: Props) => {
    const {fileNode, selectedVersion, draftVersionList, draftSessionId, onSelectVersion} = useFileNodeContext();
    const {path, lfsOid} = fileNode;
    const repo = useCurrentRepoName();
    const {closeFileNode} = useAllOpenedFileNodes();
    const oid = selectedVersion?.oid ?? lfsOid;
    const label = isTempFile ? '新文件' : '草稿';

    const handleDeleteDraft = useCallback(
        () => {
            Modal.confirm({
                content: (
                    <>
                        <Text>{label}删除后无法恢复，请谨慎操作。</Text>
                        <p>
                            <Text>确定删除{label}吗？</Text>
                        </p>
                    </>
                ),
                async onOk() {
                    const params = {
                        dataset: repo,
                        path,
                        oid,
                        timestamp: Date.now(),
                        sessionId: draftSessionId,
                    };

                    try {
                        await apiPostDeleteDatasetFileDraft(params);
                        if (isTempFile) {
                            closeFileNode(path, true);
                            return;
                        }

                        // 删除完草稿后，更新一下最新的草稿状态
                        setTimeout(async () => {
                            loadFileEditStatus({
                                dataset: repo,
                                path,
                                oid,
                            });
                        }, 500);

                        resetFileEditDraft(path);
                        message.success(`${label}删除成功`);
                        // 切回到最新的版本
                        onSelectVersion && onSelectVersion(draftVersionList.filter(item => item.id !== -1)[0]?.id);
                    }
                    catch (e) {
                        console.error(e);
                        message.error(`${label}删除失败`);
                    }
                },
                okText: '删除',
            });
        },
        [repo, path, oid, draftSessionId, isTempFile, draftVersionList, label, onSelectVersion, closeFileNode]
    );

    return (
        <Button
            type="text"
            icon={<DeleteOutlined />}
            onClick={handleDeleteDraft}
        >
            删除
        </Button>
    );
};

export default DeleteButton;
