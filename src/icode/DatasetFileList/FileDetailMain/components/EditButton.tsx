import * as nodePath from 'path';
import {useCallback, useMemo, useState} from 'react';
import {Space, Tooltip} from 'antd';
import {Button, Modal} from '@panda-design/components';
import {ENABLE_ONLINE_EDIT_EXTENSION} from '@/constants/icode/datasetFile';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {loadFileEditStatus, useFileEditStatus, useHasDraft} from '@/regions/icode/dataset/fileEditStatusRegion';
import {setIsEditingFile, useIsEditingFile} from '@/regions/icode/dataset/fileIsEditingRegion';
import {setFilePublishModalOpen} from '@/regions/icode/dataset/fileEditCommonRegion';
import {apiPostAcquireEditFile} from '@/api/icode/datasetFileEdit';
import {PublishedVersion} from '@/types/icode/dataset';
import {useFileNodeContext} from '../context/FileNodeContext';

const EditButton = () => {
    const {fileNode, selectedVersion, onSelectVersion} = useFileNodeContext();
    const {path} = fileNode;
    const {oid, isDraft = false, version} = selectedVersion ?? {} as PublishedVersion;
    const repoName = useCurrentRepoName();
    const hasDraft = useHasDraft(path);
    const {editAt, editSource} = useFileEditStatus(path);
    const isEditing = useIsEditingFile(path);
    const [isRequiringEdit, setIsRequiringEdit] = useState(false);

    const isFileExtensionLegal = useMemo(
        () => {
            return ENABLE_ONLINE_EDIT_EXTENSION.includes(nodePath.extname(path)?.toLocaleLowerCase());
        },
        [path]
    );

    const buttonTooltip = useMemo(
        () => {
            if (!isFileExtensionLegal) {
                return '当前文件类型暂不支持编辑，目前仅支持json、jsonl、csv、xlsx';
            }

            return '';
        },
        [isFileExtensionLegal]
    );

    const handleEdit = useCallback(
        async () => {
            if (hasDraft) {
                if (isDraft) {
                    setIsEditingFile(path, !isEditing);
                }
                else {
                    if (editSource === version) {
                        onSelectVersion(-1);
                        setIsEditingFile(path, true);
                        return;
                    }

                    const instance = Modal.confirm({
                        content: (
                            <>
                                <p>
                                    您在 {editAt} 编辑的 {editSource} 版本草稿尚未发布。
                                </p>
                                <p>
                                    由于一个文件仅支持一个草稿，请先对该草稿删除、发布
                                </p>
                            </>
                        ),
                        footer: () => (
                            <Space>
                                <Button
                                    onClick={() => {
                                        instance.destroy();
                                        onSelectVersion(-1);
                                    }}
                                >
                                    查看草稿
                                </Button>
                                <Button
                                    onClick={() => {
                                        instance.destroy();
                                        setFilePublishModalOpen(path, true);
                                    }}
                                >
                                    发布草稿
                                </Button>
                            </Space>
                        ),
                    });
                }
            }
            else {
                setIsRequiringEdit(true);
                await apiPostAcquireEditFile({
                    dataset: repoName,
                    path,
                    oid,
                    query: '',
                    editSource: version,
                });

                await loadFileEditStatus({
                    dataset: repoName,
                    path,
                    oid,
                });

                if (!isDraft) {
                    onSelectVersion(-1);
                }

                setIsEditingFile(path, true);
                setIsRequiringEdit(false);
            }
        },
        [hasDraft, isDraft, path, isEditing, editSource, version, editAt, onSelectVersion, repoName, oid]
    );

    return (
        <Tooltip
            title={buttonTooltip}
        >
            <Button
                type={isDraft && isEditing ? 'default' : 'primary'}
                loading={isRequiringEdit}
                disabled={!isFileExtensionLegal}
                onClick={handleEdit}
            >
                {isDraft && isEditing ? '退出编辑' : '编辑'}
            </Button>
        </Tooltip>
    );
};

export default EditButton;
