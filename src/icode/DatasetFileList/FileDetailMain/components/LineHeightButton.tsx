import {Dropdown, theme, InputNumber} from 'antd';
import type {MenuProps} from 'antd';
import {Button, marginLeft, marginRight, marginTop} from '@panda-design/components';
import {cx} from '@emotion/css';
import styled from '@emotion/styled';
import {cloneElement, CSSProperties, ReactElement, ReactNode, useCallback, useEffect, useState} from 'react';
import {
    IconRowHeightDefault,
    IconRowHeightMiddle,
    IconRowHeightHigh,
    IconRowHeightCustomized,
} from '@/icons/agGrid';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {PublishedVersion} from '@/types/icode/dataset';
import {setFileRenderProperties, useRowHeight} from '@/regions/icode/dataset/fileRenderPropertiesRegion';
import {RenderProperty} from '@/types/agGrid/agGrid';
import {apiPostFileRenderProperties} from '@/api/dataset';
import {useFileNodeContext} from '../context/FileNodeContext';

type MenuItem = Required<MenuProps>['items'][number];
const {useToken} = theme;

const PopoverContentContainer = styled.div`
    width: 200px;
    padding: 10px;

    .title {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 100%;
        letter-spacing: 0%;
        color: #666666;
        margin-left: 16px;
    }
`;

const iconSize = '14px';

const lineHeightItems: MenuItem[] = [
    {
        key: '40',
        label: '默认',
        extra: '40',
        icon: <IconRowHeightDefault style={{fontSize: iconSize}} />,
    },
    {
        key: '200',
        label: '中',
        extra: '200',
        icon: <IconRowHeightMiddle style={{fontSize: iconSize}} />,
    },
    {
        key: '400',
        label: '高',
        extra: '400',
        icon: <IconRowHeightHigh style={{fontSize: iconSize}} />,
    },
    {
        key: '-1',
        label: '自适应内容',
        extra: 'Max',
        icon: <IconRowHeightCustomized style={{fontSize: iconSize}} />,
    },
    {
        type: 'divider',
    },
];

const LineHeightButton = () => {
    const {fileNode, selectedVersion, draftSessionId} = useFileNodeContext();
    const {path, lfsOid, isTempFile} = fileNode;
    const {version} = selectedVersion ?? {} as PublishedVersion;
    const repoName = useCurrentRepoName();
    const {token} = useToken();
    const oid = selectedVersion?.oid ?? lfsOid;
    const isDraft = selectedVersion?.isDraft || isTempFile;

    const rowHeight = useRowHeight({
        dataset: repoName,
        path,
        oid,
        uuid: isDraft ? draftSessionId : version,
    });

    const [customRowHeight, setCustomRowHeight] = useState<number>();

    const contentStyle: CSSProperties = {
        backgroundColor: token.colorBgElevated,
        borderRadius: token.borderRadiusLG,
        boxShadow: token.boxShadowSecondary,
    };

    const menuStyle: CSSProperties = {
        boxShadow: 'none',
    };

    useEffect(
        () => {
            if (rowHeight && rowHeight !== -1) {
                setCustomRowHeight(rowHeight);
            }
        },
        [rowHeight]
    );

    const handleSaveRowHeight = useCallback(
        (newRowHeight: string) => {
            const renderProperties: RenderProperty[] = [
                {
                    opObj: 'TABLE',
                    rowId: '',
                    colId: '',
                    name: 'height',
                    value: newRowHeight,
                },
            ];

            apiPostFileRenderProperties({
                dataset: repoName,
                path,
                oid,
                timestamp: Date.now(),
                operations: renderProperties,
            });
        },
        [oid, path, repoName]
    );

    const handleSelectRowHeight: MenuProps['onClick'] = useCallback(
        ({key}) => {
            setFileRenderProperties({
                dataset: repoName,
                path,
                oid,
                uuid: isDraft ? draftSessionId : version,
            }, {
                opObj: 'TABLE',
                rowId: '',
                colId: '',
                name: 'height',
                value: key,
            });

            handleSaveRowHeight(key);
        },
        [draftSessionId, handleSaveRowHeight, isDraft, oid, path, repoName, version]
    );

    const handleChangeRowHeight = useCallback(
        (value: number) => {
            if (value) {
                const heightString = value.toString();
                setCustomRowHeight(value);
                setFileRenderProperties({
                    dataset: repoName,
                    path,
                    oid,
                    uuid: isDraft ? draftSessionId : version,
                }, {
                    opObj: 'TABLE',
                    rowId: '',
                    colId: '',
                    name: 'height',
                    value: heightString,
                });

                handleSaveRowHeight(heightString);
            }
        },
        [draftSessionId, handleSaveRowHeight, isDraft, oid, path, repoName, version]
    );

    return (
        <Dropdown
            menu={{
                items: lineHeightItems,
                onClick: handleSelectRowHeight,
                selectedKeys: [rowHeight ? String(rowHeight) : '40'],
            }}
            popupRender={(menu: ReactNode) => {
                return (
                    <PopoverContentContainer
                        style={contentStyle}
                    >
                        <span className="title">选择行高</span>
                        {cloneElement(
                            menu as ReactElement<{
                            style: CSSProperties;
                            }>,
                            {style: menuStyle}
                        )}
                        <span className="title">自定义行高</span>
                        <div className={cx(marginLeft(10), marginRight(10), marginTop(8))}>
                            <InputNumber
                                size="small"
                                style={{width: '100%'}}
                                controls={false}
                                min={30}
                                max={1000}
                                value={customRowHeight}
                                onChange={handleChangeRowHeight}
                                placeholder="30-1000"
                            />
                        </div>
                    </PopoverContentContainer>
                );
            }}
        >
            <Button
                type="text"
                icon={
                    (lineHeightItems.find(e => e.key === String(rowHeight)) as any)?.icon
                    ?? <IconRowHeightCustomized style={{fontSize: iconSize}} />
                }
            >
                行高
            </Button>
        </Dropdown>
    );
};

export default LineHeightButton;
