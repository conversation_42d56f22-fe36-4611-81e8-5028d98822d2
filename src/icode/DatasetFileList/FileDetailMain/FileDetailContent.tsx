import styled from '@emotion/styled';
import PreviewGrid from '@/components/dataset/DatasetFilePreviewTable/PreviewGrid';
import {PublishedVersion} from '@/types/icode/dataset';
import {useIsEditingFile} from '@/regions/icode/dataset/fileIsEditingRegion';
import {useFileNodeContext} from './context/FileNodeContext';

const FileDetailContentContainer = styled.div`
    flex: 1;
    padding-bottom: 10px;
`;

const FileDetailContent = () => {
    const {fileNode, selectedVersion, draftSessionId} = useFileNodeContext();
    const {path, isTempFile} = fileNode;
    const {oid, version, isDraft} = selectedVersion ?? {} as PublishedVersion;
    const isEditing = useIsEditingFile(path);

    if (!path && !fileNode.path) {
        return null;
    }

    return (
        <FileDetailContentContainer>
            {
                isTempFile ? (
                    <PreviewGrid
                        path={fileNode.path}
                        oid={fileNode.lfsOid}
                        isDraft
                        version={'临时文件'}
                        editable
                        draftSessionId={draftSessionId}
                    />
                ) : (
                    <PreviewGrid
                        path={path}
                        oid={oid}
                        isDraft={isDraft}
                        version={version}
                        editable={isDraft && isEditing}
                        draftSessionId={draftSessionId}
                    />
                )
            }
        </FileDetailContentContainer>
    );
};

export default FileDetailContent;
