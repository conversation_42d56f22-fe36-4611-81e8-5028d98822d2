import {useCallback, useEffect, useMemo, useState} from 'react';
import styled from '@emotion/styled';
import {FileNode} from '@/types/icode/repo';
import FilePointerContent from '@/components/dataset/DatasetFilePreview/FilePointerContent';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {
    loadVersionList,
    useIsLoadingVersionList,
    useVersionList,
} from '@/regions/icode/dataset/filePreviewVersionRegion';
import {PublishedVersion} from '@/types/icode/dataset';
import {loadFileRenderProperties} from '@/regions/icode/dataset/fileRenderPropertiesRegion';
import {useTempFileData} from '@/regions/dataset/tempFiles';
import {loadFileEditStatus, useFileEditStatus} from '@/regions/icode/dataset/fileEditStatusRegion';
import {ParamsGetFileRenderProperties} from '@/types/comatestack/dataset';
import FileDetailHeader from './FileDetailHeader';
import FileDetailContent from './FileDetailContent';
import {FileNodeContext} from './context/FileNodeContext';
import {useTempFileRenderPropertiesEffect} from './hooks/useTempFileRenderPropertiesEffect';

const FileDetailMainContainer = styled.div`
    height: calc(100vh - 120px);
    background-color: #fff;
    padding: 10px;
    display: flex;
    flex-direction: column;
    border: 1px solid #E0E0E0;
    border-top: none;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
    overflow: auto;
`;

interface Props {
    fileNode: FileNode;
}

const FileDetailMain = ({fileNode}: Props) => {
    const repo = useCurrentRepoName();
    const {path, lfsOid, name, isTempFile} = fileNode;
    const versionList = useVersionList({path, oid: lfsOid, repo});
    const [selectedVersion, setSelectedVersion] = useState<PublishedVersion>(undefined);
    const {draftSessionId, editSourceOid, editAt, editBy} = useFileEditStatus(path);
    const isLoadingVersionList = useIsLoadingVersionList({path, oid: lfsOid, repo});
    const {tempFileSessionId, tempFileOid} = useTempFileData(repo, path);

    useTempFileRenderPropertiesEffect(tempFileSessionId, repo, path, tempFileOid);

    useEffect(
        () => {
            if (isTempFile) {
                return;
            }

            loadVersionList({path, oid: lfsOid, repo});
        },
        [path, lfsOid, repo, isTempFile]
    );

    // 有草稿时默认在版本列表顶部追加草稿
    const draftVersionList = useMemo(
        () => {
            if (draftSessionId) {
                return [
                    {
                        id: -1,
                        version: '草稿',
                        path,
                        commitId: '',
                        oid: editSourceOid,
                        createdAt: editAt,
                        createdBy: editBy,
                        isDraft: true,
                    },
                    ...versionList,
                ];
            }

            return versionList;
        },
        [draftSessionId, editAt, editBy, editSourceOid, path, versionList]
    );

    // selectedVersion 为 undefined 时，优先自动定位到草稿，其次是最新版本
    useEffect(
        () => {
            /**
             * draftSessionId: undefined 表示还未判定是否存在草稿，一定要等判定出结果
             * draftSessionId: 有值 表示有草稿
             * draftSessionId: null 表示已判定无草稿
             */
            if (draftSessionId === undefined) {
                return;
            }

            if (draftSessionId && selectedVersion === undefined && draftVersionList.length) {
                setSelectedVersion(draftVersionList[0]);
                return;
            }

            if (draftSessionId === null && selectedVersion === undefined && versionList.length) {
                setSelectedVersion(versionList[0]);
            }
        },
        [selectedVersion, draftSessionId, draftVersionList, versionList]
    );

    // selectedVersion 为 null 时，自动定位最新版本
    useEffect(
        () => {
            if (selectedVersion === null && versionList.length) {
                setSelectedVersion(versionList[0]);
            }
        },
        [selectedVersion, versionList]
    );

    // 初次进入页面时，获取草稿状态，判定是否存在草稿
    useEffect(
        () => {
            if (versionList.length) {
                const {oid} = versionList[0];
                const params = {
                    dataset: repo,
                    path,
                    oid,
                };

                loadFileEditStatus(params);
            }
        },
        [path, repo, versionList]
    );

    // 切换版本时，获取渲染属性
    useEffect(
        () => {
            if (!selectedVersion) {
                return;
            }

            const findDraftVersionItem = draftVersionList.find(item => item.id === selectedVersion.id);
            if (!findDraftVersionItem) {
                return;
            }

            const params = {
                dataset: repo,
                path,
                oid: findDraftVersionItem.oid,
            } as ParamsGetFileRenderProperties;

            if (findDraftVersionItem.isDraft) {
                params.sessionId = draftSessionId;
            }
            else {
                params.version = findDraftVersionItem.version;
            }

            loadFileRenderProperties(params);
        },
        [selectedVersion, draftSessionId, draftVersionList, path, repo]
    );

    const handleSelectVersion = useCallback(
        (id: number) => {
            if (id === null) {
                setSelectedVersion(null);
                return;
            }

            setSelectedVersion(draftVersionList.find(item => item.id === id));
            const findVersion = versionList.find(item => item.id === id);
            if (!findVersion) {
                return;
            }

            const params = {
                dataset: repo,
                path,
                oid: findVersion.oid,
            };

            loadFileEditStatus(params);
        },
        [draftVersionList, path, repo, versionList]
    );

    return (
        <FileDetailMainContainer>
            {
                name?.startsWith('README')
                    ? <FilePointerContent filePath={path} />
                    : (
                        <FileNodeContext.Provider
                            value={{
                                isLoadingVersionList,
                                selectedVersion,
                                draftVersionList,
                                // 将临时新文件的 Session 和 草稿的 SessionId 融合到 Context 中的 draftSessionId
                                draftSessionId: tempFileSessionId || draftSessionId,
                                fileNode,
                                onSelectVersion: handleSelectVersion,
                            }}
                        >
                            <FileDetailHeader />
                            <FileDetailContent key={selectedVersion?.id ?? tempFileSessionId} />
                        </FileNodeContext.Provider>
                    )
            }
        </FileDetailMainContainer>
    );
};

export default FileDetailMain;
