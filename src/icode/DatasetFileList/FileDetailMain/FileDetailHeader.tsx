/* eslint-disable complexity */
import {useMemo} from 'react';
import {Divider, Space} from 'antd';
import {FormProvider} from '@panda-design/path-form';
import styled from '@emotion/styled';
import {Button, marginLeft} from '@panda-design/components';
import {PublishedVersion} from '@/types/icode/dataset';
import DatasetFilePublishModal from '@/components/dataset/DatasetFileEdit/DatasetFilePublishModal';
import TempFilePublishModal from '@/components/dataset/DatasetFileEdit/TempFilePublishModal';
import FileDownloadButton from '@/components/icode/DatasetFileOperation/FileDownloadButton';
import {setFilePublishModalOpen, setTempFilePublishModalOpen} from '@/regions/icode/dataset/fileEditCommonRegion';
import {useFileTotalRows} from '@/regions/icode/dataset/fileTotalRowsRegion';
import {MAX_ROW_COUNT} from '@/constants/comatestack/agGrid';
import LineHeightButton from './components/LineHeightButton';
import EditButton from './components/EditButton';
import DeleteButton from './components/DeleteButton';
import RollbackButton from './components/RollbackButton';
import VersionSelector from './components/VersionSelector';
import {useFileNodeContext} from './context/FileNodeContext';

const FileDetailHeaderContainer = styled.div`
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: space-between;
`;

const LeftContainer = styled.div`
    display: flex;
    align-items: center;
`;

const FileDetailHeader = () => {
    const {fileNode, selectedVersion, draftVersionList} = useFileNodeContext();
    const {path, isTempFile, name, fileDir} = fileNode;
    const {version, isDraft} = selectedVersion ?? {} as PublishedVersion;
    const oid = selectedVersion?.oid ?? fileNode.lfsOid;
    const totalRows = useFileTotalRows({
        path,
        oid,
    });

    const latestVersion = useMemo(
        () => {
            const onlyVersionList = draftVersionList.filter(item => !item.isDraft);
            if (!onlyVersionList.length) {
                return '';
            }

            // 排除草稿，获取第一个版本作为最新版本
            return onlyVersionList[0].version;
        },
        [draftVersionList]
    );

    return (
        <FileDetailHeaderContainer>
            <LeftContainer>
                {/* 版本 */}
                {
                    !isTempFile && <VersionSelector />
                }
                {/* 行数 */}
                {
                    !isTempFile && totalRows && (
                        <>
                            <Divider style={{margin: '0 16px'}} type="vertical" />
                            {totalRows}行
                        </>
                    )
                }
                {/* 行高 */}
                {
                    totalRows && totalRows <= MAX_ROW_COUNT && (
                        <>
                            {
                                !isTempFile && <Divider className={marginLeft(16)} type="vertical" />
                            }
                            <LineHeightButton />
                        </>
                    )
                }
            </LeftContainer>
            <Space>
                {
                    (isDraft || isTempFile) && <DeleteButton isTempFile={isTempFile} />
                }
                {
                    !isDraft && !isTempFile && (
                        <FileDownloadButton
                            oid={oid}
                            path={path}
                            name={path?.split('/')?.pop() || ''}
                            style={{marginRight: '10px', fontSize: '18px'}}
                        />
                    )
                }
                {/* 回滚按钮：仅在非草稿、非最新版本、非最旧版本 时才显示 */}
                {version && !isDraft && version !== 'V0' && version !== latestVersion && draftVersionList.length && (
                    <RollbackButton />
                )}
                {
                    (isDraft || isTempFile) && (
                        <Button
                            type="default"
                            onClick={() => {
                                isDraft && setFilePublishModalOpen(path, true);
                                isTempFile && setTempFilePublishModalOpen(path, true);
                            }}
                        >
                            发布
                        </Button>
                    )
                }
                {
                    !isTempFile && totalRows && totalRows <= MAX_ROW_COUNT && <EditButton />
                }
            </Space>
            {/* 草稿发布成新版本 */}
            {
                isDraft && (
                    <FormProvider>
                        <DatasetFilePublishModal />
                    </FormProvider>
                )
            }
            {/* 临时文件发布成真实文件 */}
            {
                isTempFile && (
                    <FormProvider initialValues={{fileName: name, fileExtension: '.jsonl', folderPath: fileDir}}>
                        <TempFilePublishModal oid={oid} />
                    </FormProvider>
                )
            }
        </FileDetailHeaderContainer>
    );
};

export default FileDetailHeader;
