/**
 * @file 文件列表页
 */
import {useCallback, useEffect, useMemo} from 'react';
import {Divider, Tabs} from 'antd';
import type {TabsProps} from 'antd';
import {useNavigate, useParams} from 'react-router-dom';
import {Button, marginLeft, marginRight} from '@panda-design/components';
import {cx} from '@emotion/css';
import {CloseOutlined} from '@ant-design/icons';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {useFileNode, useFileNodeError} from '@/regions/icode/fileTree';
import {
    loadDatasetLinkModel,
    loadDatasetLinkReport,
    loadDatasetLinkTag,
} from '@/regions/icode/dataset';
import {useCurrentRefName} from '@/hooks/icode/current/useCurrentRefName';
import {useCurrentPath} from '@/hooks/icode/current/useCurrentPath';
import {
    pushRealFiles,
    useRealFiles,
} from '@/regions/dataset/realFiles';
import {loadTempFiles} from '@/regions/dataset/tempFiles';
import {getFileUrl} from '@/utils/icode/route/getFileUrl';
import {getDatasetFileIcon} from '@/utils/dataset/fils';
import {ProjectFilesLink} from '@/links/comatestack';
import {useAllOpenedFileNodes} from '../hooks/useAllOpenedFileNodes';
import FileDetailMain from '../FileDetailMain';
import FileListPage from '../FileListPage';
import {FileListMainContainer, TabName} from './FileListMainStyle';

export const FileListMain = () => {
    const repoName = useCurrentRepoName();
    const refName = useCurrentRefName();
    const {projectUuid} = useParams();
    const filePath = useCurrentPath() as string;
    const error = useFileNodeError({repo: repoName, commit: refName, path: filePath});
    const currentNode = useFileNode({repo: repoName, commit: refName, path: filePath});
    const fileNodes = useRealFiles(repoName);
    const navigate = useNavigate();
    const {allFileNodes, closeFileNode} = useAllOpenedFileNodes();

    useEffect(
        () => {
            loadDatasetLinkModel({repoName});
            loadDatasetLinkReport({repoName});
            loadDatasetLinkTag({repoName});
            loadTempFiles({dataset: repoName});
        },
        [repoName]
    );

    useEffect(
        () => {
            const findNode = fileNodes.find(item => item.path === currentNode?.path);
            if (!findNode && currentNode && currentNode.type === 'BLOB') {
                pushRealFiles(repoName, currentNode);
            }
        },
        [currentNode, fileNodes, refName, repoName]
    );

    const tabItems: TabsProps['items'] = useMemo(
        () => {
            return allFileNodes.map((item, index) => {
                const {name = '', path, isTempFile} = item;
                const fileName = name.split('/').pop();
                const selectedNodeIndex = fileNodes.findIndex(item => item.path === filePath);
                const Icon = getDatasetFileIcon(name);
                return {
                    key: path,
                    label: (
                        <>
                            <TabName ellipsis={{tooltip: true}}>{fileName}</TabName>
                            <Button
                                className={cx(path === filePath ? marginRight(12) : 'tab-close-button')}
                                size="small"
                                type="text"
                                icon={<CloseOutlined />}
                                onClick={e => {
                                    e.stopPropagation();
                                    closeFileNode(path, isTempFile);
                                }}
                            />
                            <Divider
                                className={cx(marginLeft(0), marginRight(0))}
                                type="vertical"
                                // 当前选中的和其左侧的第一个，不显示分割线
                                style={{
                                    visibility: path === filePath || index === selectedNodeIndex - 1
                                        ? 'hidden'
                                        : 'visible',
                                }}
                            />
                        </>
                    ),
                    children: <FileDetailMain fileNode={item} />,
                    icon: <Icon style={{fontSize: '16px'}} />,
                    forceRender: true,
                };
            });
        },
        [allFileNodes, closeFileNode, fileNodes, filePath]
    );

    const handleChangeTab = useCallback(
        (activeKey: string) => {
            const findNode = allFileNodes.find(item => item.path === activeKey);
            const url = getFileUrl({
                repoName,
                type: findNode?.type?.toLowerCase(),
                encodedRefName: encodeURIComponent(refName),
                path: activeKey,
            });

            navigate(url);
        },
        [allFileNodes, navigate, refName, repoName]
    );

    const mainContent = useMemo(
        () => {
            return (
                <FileListMainContainer hasItems={tabItems.length > 0} isSelectedTree={currentNode?.type === 'TREE'}>
                    <Tabs
                        activeKey={filePath}
                        items={tabItems}
                        hideAdd
                        onChange={handleChangeTab}
                        animated={false}
                        // tabBarExtraContent={tabItems.length > 0 && <FullscreenButton />}
                    />
                </FileListMainContainer>
            );
        },
        [currentNode, filePath, handleChangeTab, tabItems]
    );

    // 统一跳转到根目录
    if (error) {
        navigate(ProjectFilesLink.toUrl({projectUuid}));
        return;
    }

    return (
        <div style={{backgroundColor: '#F2F2F2'}}>
            {mainContent}
            {/* 文件夹信息 */}
            {
                currentNode?.type === 'TREE' && <FileListPage />
            }
        </div>
    );
};
