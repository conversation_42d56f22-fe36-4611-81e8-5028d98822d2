import {Typography} from 'antd';
import styled from '@emotion/styled';
import {colors} from '@/constants/colors';
const {Text} = Typography;
export const FileListMainContainer = styled.div<{hasItems: boolean, isSelectedTree: boolean}>`
    background-color: ${colors['gray-3']} !important;
    margin-bottom: 12px;

    .ant-5-tabs {
        .ant-5-tabs-nav {
            background-color: #FCFCFC !important;
            border: ${({hasItems}) => (hasItems ? 'solid 1px #E0E0E0;' : 'none')};
            border-radius: 6px 6px ${({isSelectedTree}) => (isSelectedTree ? '6px 6px' : '0 0')} !important;

            .ant-5-tabs-nav-wrap {
                overflow: auto !important;
                scrollbar-width: none !important;;
            }

            .ant-5-tabs-ink-bar {
                display: none !important;
            }

            .ant-5-tabs-tab {
                margin-left: 0 !important;
                padding: 9px 12px !important;
                padding-right: 0 !important;

                .ant-5-typography {
                    line-height: 20px !important;
                    color: #545454 !important;
                }

                .ant-5-tabs-tab-btn {
                    display: flex;
                    align-items: center;
                }

                .ant-5-tabs-tab-icon {
                    margin-right: 4px !important;
                }

                .tab-close-button {
                    position: absolute;
                    right: 12px;
                    visibility: hidden;
                }

                &:hover {
                    &::before {
                        display: block;
                    }

                    .tab-close-button {
                        visibility: visible;
                    }

                    .ant-5-typography {
                        color: #181818 !important;
                    }
                }
            }

            .ant-5-tabs-tab-active {
                background-color: #E5F2FF !important;
                border-top-left-radius: 6px !important;
                border-top-right-radius: 6px !important;
                border-bottom: solid 2px #3381F7 !important;

                .ant-5-typography {
                    line-height: 22px !important;
                    color: #181818 !important;
                }

                &:hover {
                    &::before {
                        display: none;
                    }

                    .tab-close-button {
                        visibility: hidden;
                    }
                }
            }

            .ant-5-tabs-tab::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(270deg, #FCFCFC 30%, rgba(252, 252, 252, 0) 100%);
                display: none;
                pointer-events: none;
            }

            .ant-5-tabs-nav-more {
                display: none !important;
            }
        }
    }
`;

export const TabName = styled(Text)`
    max-width: 200px !important;
    margin-right: 12px !important;
    &:hover {
        color: ${colors.primary};
    }
`;
