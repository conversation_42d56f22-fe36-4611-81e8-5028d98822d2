/**
 * @file 文件 列表/详情 页状态信息
 */
import {Divider} from 'antd';
import {LeftOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import {css} from '@emotion/css';
import {Link, useLocation} from 'react-router-dom';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {getPrefixByRepoName} from '@/utils/icode/route/getPrefixByRepoName';
import {useCurrentEncodeRefName, useCurrentRefName} from '@/hooks/icode/current/useCurrentRefName';
import {useCurrentPath} from '@/hooks/icode/current/useCurrentPath';
import {getParentPath} from '@/utils/icode/files';
import {getFileUrl} from '@/utils/icode/route/getFileUrl';
import {useFileNode} from '@/regions/icode/fileTree';
import {myToken} from '@/constants/colors';

const RootContainer = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 56px;
    padding-left: 14px;
    padding-right: 16px;
    border: ${myToken.border};
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    border-bottom: none;
    background-color: #FFF;
`;


const UpLevelContainer = styled.span`
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
`;

const FilePathContainer = styled.span`
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #000000;
`;

const dividerClassName = css`
    margin: 16px;
`;

const useCurrentFileNodeOrDirectory = () => {
    const {pathname} = useLocation();
    const repoName = useCurrentRepoName();
    const prefix = getPrefixByRepoName(repoName);
    const [type] = pathname.slice(`/${prefix}/`.length).split('/');
    const refName = useCurrentRefName();
    const filePath = useCurrentPath();
    const parentPath = getParentPath(filePath);
    const currentNode = useFileNode({repo: repoName, commit: refName, path: filePath});
    const parentNode = useFileNode({repo: repoName, commit: refName, path: parentPath});
    if (type === 'tree') {
        return currentNode;
    }
    return parentNode;
};

const StatusBar = () => {
    const file = useCurrentFileNodeOrDirectory();
    const {path: filePath} = file ?? {};
    const repoName = useCurrentRepoName();
    const refName = useCurrentEncodeRefName();
    const parentPath = getParentPath(filePath);

    const parentUrl = getFileUrl({
        repoName,
        type: 'tree',
        encodedRefName: refName,
        path: parentPath,
    });

    return (
        <RootContainer>
            <div>
                {
                    filePath && (
                        <>
                            <Link to={parentUrl} style={{color: '#545454'}}>
                                <LeftOutlined />
                                <UpLevelContainer>上一级</UpLevelContainer>
                            </Link>
                            <Divider type="vertical" className={dividerClassName} />
                        </>
                    )
                }
                <FilePathContainer>{filePath}</FilePathContainer>
            </div>
        </RootContainer>
    );
};

export default StatusBar;
