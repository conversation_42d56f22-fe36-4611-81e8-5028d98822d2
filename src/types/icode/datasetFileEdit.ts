export interface DatasetFileEditStatus {
    enableEdit: boolean;
    status: string;
    editAt: string;
    editBy: string;
    editSourceOid: string;
    draftSessionId: string;
    detail: string;
    editSource: string;
}

export interface ParamsCreateFile {
    dataset: string;
    fileDir: string;
}

export interface TempFile {
    sessionId: string;
    fileDir: string;
    path: string;
    name: string;
    oid: string;
    message: string;
}
