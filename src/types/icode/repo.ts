/* eslint-disable max-lines */
import {DatasetSourcePlatform, TrainScene, TrainTarget} from './dataset';

/**
 * @file 代码库相关类型
 */
export type RepoType = 'OPENSOURCE' | 'PRIVATE' | 'PUBLIC';

// 代码库冻结状态
export enum FrozenStatus {
    /**
    * 冻结状态
    */
    FROZEN = 'FROZEN',
    /**
    * 待冻结状态
    */
    PENDING_FROZEN = 'PENDING_FROZEN',
    /**
    * 活跃状态(正常状态)
    */
    ACTIVE = 'ACTIVE',
}

export interface Contributor {
    addLines: number;
    changedFilesCount: number;
    commits: number;
    deleteLines: number;
    username: string;
}

export interface RepoInfo {
    abTesting: null;
    comment: string;
    contributors?: Contributor[];
    creator: string;
    defaultBranch: string;
    description: string;
    enableDownloadRestriction: null;
    engineeringType: string;
    favoriteCount?: number;
    fullName: string;
    // 有些旧的数据集、代码库是没有这个字段的
    cDatasetName?: string;
    gmtCreate: string;
    gmtModified: string;
    groupRole: {
        groupName: string;
        groupType: string;
        role: string;
    } | null;
    groupRoles: {
        icafe?: string;
    } | null;
    hasNoPermission: null;
    id: number;
    isFavorite: boolean;
    labels: string;
    language: string;
    languageReal: string;
    luoShuAccountId: string | null;
    name: string | null;
    pdbProductId: number | null;
    productId: number | null;
    scmFlow: boolean;
    secret: RepoType | null;
    starsPeriod?: number;
    updateTime?: string;
    workflowType: string;
    templateName: string | null;
    frozenStatus: FrozenStatus;
    type: 'REPO' | 'DATASET';
    sourcePlatform: DatasetSourcePlatform;
    cname: string;
}

export interface CurrentRepo extends RepoInfo {
    product: {
        applySubmitSetting: boolean;
        companyId: number;
        createRepoRole: string;
        gmtCreate: string;
        gmtModified: string;
        id: number;
        name: string;
        personalCode: boolean;
    };
    paths: {
        hook: string;
        http: string;
        httpsBasePath: unknown;
        httpsCloneCmd: unknown;
        ssh: string;
    };
    space: {
        openIssueNum: unknown;
        spaceId: number;
        spaceState: string;
    } | null;
}

// NOTE 未验证
export interface CommitDetailUser {
    name: string;
    email: string;
    time: string;
}

export interface CommitDetailInfo {
    author: CommitDetailUser;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    branches: any[];
    changeId: string;
    // 不一定有评审链接
    changeNumber?: string;
    children: string[];
    comment: string;
    commitId: string;
    commitTime: number;
    committer: CommitDetailUser;
    parents: string[];
    submitter: CommitDetailUser;
}

export interface Tag {
    ahead: null;
    base: false;
    behind: null;
    commitId: string;
    commitTime: null;
    committer: string;
    committerEmail: null;
    createTime: null;
    createdBySystem: false;
    gzURL: null;
    message: null;
    name: string;
    packageURL: null;
    refName: string;
    repoName: null;
    rollbackOperator: null;
    rollbackTime: null;
    workflowStatus: string;
    zipURL: null;
    // 前端新增
    isBase?: boolean;
}

export interface FileNode {
    comment?: string | null;
    commit?: string | null;
    commitDate?: string | null;
    committer?: string | null;
    committerEmail?: string | null;
    name: string;
    path: string;
    type: 'TREE' | 'BLOB';
    children?: FileNode[];
    // 下面是 dataset 的字段
    fileSize: number;
    lfsOid: string;
    tag: number[] | null;
    model: number[] | null;
    linkReport: number[] | null;
    trainScene: TrainScene;
    trainTarget: TrainTarget;
    // 文件的最新版本
    fileVersion: string;
    // 临时文件相关字段
    isTempFile?: boolean;
    fileDir?: string;
}

export type CiBuildResult = 'SUCCESS' | 'STAGE' | 'FAILED' | 'NONE' | 0 | -1 | null;

export interface CiPipeline {
    pipelineName: string;
    pipelineUrl: string;
    result: CiBuildResult;
}

export interface Ci {
    buildResult: CiBuildResult;
    pipelines: CiPipeline[];
}

export interface FileContentOrMine {
    // Content 有 value，Mine 有 mine 和 url
    value: string;
    length: number;
    mime?: string;
    url?: string;
}

export interface MergeCompareInfo {
    ahead: number;
    behind: number;
    fromCommitId: string;
    fromCommitTime: string;
    fromCommitter: string;
    toCommitId: string;
    toCommitTime: string;
    toCommitter: string;
}

export interface CanAutoMerge {
    status: string;
    data: boolean;
    message: string;
}

interface Member {
    name: string;
    type: string;
}

export interface ProtectedBranch {
    branchName: string;
    repoName: string;
    members: Member[];
    groups: Member[];
    gmtCreate: string;
    canMerge?: boolean;
    relationIcafe?: boolean;
    icafeStatus?: string;
}

export interface RepoCommit {
    author: string;
    authorDate: string;
    authorEmail: string;
    commitDate: string;
    commitId: string;
    committer: string;
    committerEmail: string;
    fullComment: string;
    isInMainLine: boolean;
    isMergePoint: boolean;
    parentIds: string[];
    shortComment: string;
    submitDate: string;
    submitter: string;
    submitterEmail: string;
}

// 代码不落盘状态
export interface RepoLimitStatus {
    repoName: string;
    canOfficeNetworkOperate: boolean;
    exemptRule: string;
    exemptReason: string;
    exemptTimeType: string;
    exemptExpire: string;
    exemptTime: string;
    inProcessingExemptTimeType: 'TEMP' | 'LONG' | null;
}

export interface LuoshuAccount {
    accountId: string;
    accountType: string;
    children: LuoshuAccount[];
    codeName: string;
    initAccount: boolean;
    isFrozen: boolean;
    level: number;
    name: string;
    parentPath: string[];
    parentPdbId: number;
    pdbId: number;
    id: number;
}

export interface DevelopmentLanguage {
    devScenarioName: string;
    devScenarioDesc: string;
    devScenarioKey: string;
    languageRecommends: [
    {
        languageName: string;
        languageDesc: string;
        languageRecommendType: string;
    },
    ];
}

export interface LuoshuAccountNode {
    disabled: boolean;
    key: string;
    node: LuoshuAccount;
    parentPdbId: number;
    selectable: boolean;
    title: string;
    value: string;
}

export interface BranchOrTag {
    isProtected?: boolean;
    name: string;
    refName: string;
    buildResult?: string;
    pipelines?: unknown[];
    // Branch
    active?: boolean;
    ahead?: null;
    behind?: null;
    branchStatus?: string;
    commitId?: string;
    commitTime?: string;
    committer?: string;
    committerEmail?: null;
    createTime?: string;
    creator?: string;
    fastForwardStatus?: null;
    isBase?: boolean;
    isDefaultBranch?: boolean;
    lastBuildSuccess?: null;
    lastBuildURL?: null;
    needMergeTag?: null;
    relatedStory?: null;
    relatedStoryURL?: null;
}

export type RefType = 'branch' | 'tag' | 'commitId';

export interface CommitFile {
    name: string;
    data: {
        status: string;
    };
}

export type TargetBranchPlacement = 'top' | 'bottom';

export interface RepoTemplateBuiltin {
    id: number;
    name: string;
    title: string;
    language: string;
    serviceType: string;
    templateRepoName: string;
    description: string;
    buildType: string;
}

export interface RepoLanguageMeta {
    group?: string;
    artifact?: string;
    name?: string;
    description?: string;
    packageName?: string;
    module?: string;
    buildType?: string;
}

export interface BrowseCodeTreeNode {
    type: 'folder' | 'file';
    filename: string;
    title: string;
    key: string;
    path: string;
    hidden: boolean | null;
    isLeaf: boolean;
    language?: string;
    content?: string;
    children?: BrowseCodeTreeNode[];
}

export interface SystemLabel {
    hashTag: string;
    labelName: string;
    description: string;
    labelColor: string;
    repoPath: string;
    inUsed: true;
    createDate: string;
    relatedCount: number;
    isAutoLabel: boolean;
}

export interface CustomLabel {
    hashTag: string;
    labelName: string;
    description: string;
    labelColor: string;
    repoPath: string;
    inUsed: boolean;
    createDate: string;
    relatedCount: number;
    isAutoLabel: boolean;
}

export interface CreateCustomLabelProps {
    labelName: string;
    description: string;
    labelColor: string;
}

export interface ICafeCardInfo {
    id: string;
    title: string;
    url: string;
}

export interface DeprecatedError {
    kind: 'deprecated';
    name: string;
}

export interface OutdatedError {
    kind: 'outdated';
    name: string;
    currentVersionRange: string;
    allowedMinimumVersion: string;
}

export type DependencyError = DeprecatedError | OutdatedError;

// 代码库消息通知
interface BasicUser {
    username: string;
}

export interface RepoNotificationPatchSetCreate {
    type: 'PATCHSET_CREATE';
    notifyObject: {
        body: {
            repoName: string;
            changeNumber: number;
            commitId: string;
            commitMessage: string;
            toBranch: string;
            patchSet: string;
            changeOwnerUserName: BasicUser;
            changeOwner: BasicUser;
            changeUploaderUserName: BasicUser;
            changeUploader: string;
            topic: string;
            defaultReviewers: BasicUser[] | null;
            happenDate: string;
        };
    };
}

export interface RepoNotificationCommentAdd {
    type: 'COMMENT_ADD';
    notifyObject: {
        body: {
            repoName: string;
            changeNumber: number;
            toBranch: string;
            changeUrl: string;
            score: number | null;
            commentsCounts: number | null;
            changeOwner: BasicUser;
            commentsOperator: BasicUser;
            commitMessage: string;
            happenDate: string;
        };
    };
}

export interface RepoNotificationChangeMerge {
    type: 'CHANGE_MERGE';
    notifyObject: {
        body: {
            repoName: string;
            changeNumber: number;
            fromBranch: string;
            toBranch: string;
            newCommitId: string;
            changeOwner: BasicUser;
            comments: string;
            changeMergeOperator: BasicUser;
            commitMessage: string;
            happenDate: string;
        };
    };
}

export interface RepoNotificationCustom {
    type: 'CUSTOM';
    notifyObject: {
        body: string;
    };
}

export interface JsDepsWarningNotificationCreate {
    type: 'JS_DEPS_WARNING';
    notifyObject: {
        body: string;
    };
}

export interface JsDepsErrorNotificationCreate {
    type: 'JS_DEPS_ERROR';
    notifyObject: {
        body: string;
        depsCheckResult: DependencyError[];
    };
}

type NotifyValue = RepoNotificationPatchSetCreate
    | RepoNotificationCommentAdd
    | RepoNotificationChangeMerge
    | RepoNotificationCustom
    | JsDepsWarningNotificationCreate
    | JsDepsErrorNotificationCreate;

export interface RepoNotification {
    notifyValue: NotifyValue;
    notifyScore: {
        score: number;
    };
}

export interface FuzzyMatchProduct {
    createRepoRole: string;
    name: string;
    isRepoProductName: boolean;
}

// 用户在某一代码库下的权限
// CHIEF：代码库owner
// ADMIN：管理员
// OWNER：写权限
// MEMBER：读权限
// DELIVERER： 交付权限
export type RepoRole = 'CHIEF' | 'ADMIN' | 'OWNER' | 'MEMBER' | 'DELIVERER';
