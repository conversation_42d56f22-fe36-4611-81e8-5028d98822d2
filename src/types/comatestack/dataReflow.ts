export interface DesensitizationsRuleType {
    key: string;
    method: string;
}

// eslint-disable-next-line max-len
export type TimeLabel = 'HOUR1' | 'HOUR2' | 'HOUR3' | 'HOUR4' | 'HOUR6' | 'HOUR8' | 'HOUR12' | 'DAY1' | 'DAY2' | 'DAY3' | 'DAY4' | 'DAY5' | 'DAY6' | 'WEEK1';

export interface ParamsCreateReflowProject {
    flywheelProjectName: string;
    stackSpace: string;
    stackProject: string;
    clientId: string;
    taskName: string;
    taskFileName: string;
    taskTransferTime: string;
    taskTargetPath: string;
    taskArchiveTime?: string;
    taskTargetBranch: string;
    taskTransferEncryption: boolean;
    transferDesensitizations: DesensitizationsRuleType[];
    dataSourceType: string;
    dataPathList: string[];
    accessKey: string;
    secretKey: string;
    remoteAddr: string;
    taskDelayTime: number;
    fileSuffix: string;
}

export interface ResultDataSource {
    name: string;
    clientId: string;
    creator: string;
    admins: string[];
    desc: string;
    label: string;
    projectNumber: number;
}

export interface ParamsTaskNameCheck {
    taskName: string;
    stackSpace: string;
    stackProject: string;
}

interface SourceType {
    name: string;
    clientId: string;
    creator: string;
    desc: string;
    label: null;
    status: string;
    admins: string[];
}

export interface TaskType {
    taskId: number;
    taskName: string;
    taskCreator: string;
    transferDesensitizations: DesensitizationsRuleType[];
    taskStatus: 'INIT' | 'OPEN' | 'CLOSE';
    reflowLines: number;
    clientId: string;
    source: SourceType;
}

export interface DatasetType {
    stackSpace: string;
    stackProject: string;
    fullName: string;
    desc: string;
    secLevel: string;
    admins: string[];
    tasks: TaskType[];
}

export interface ResultReflowProjectTask {
    flywheelProjectName: string;
    datasets: DatasetType[];
}


export interface ParamsInstructionStartTask {
    stackSpace: string;
    stackProject: string;
    taskName: string;
    taskId: number;
    instruction: string;
}

export interface ParamsStartOrSuspendTask {
    stackSpace: string;
    stackProject: string;
    taskName: string;
    taskId: number;
    action: string;
}

export interface ParamsProjectReflowTask {
    stackSpace: string;
    stackProject: string;
    repo?: string;
}

export interface ResultProjectReflowTask {
    flywheelProjectName: string;
    taskName: string;
    taskCreator: string;
    taskId: number;
    taskStatus: 'INIT' | 'OPEN' | 'CLOSE';
    reflowLines: number;
    taskFileName: string;
    transferDesensitizations: DesensitizationsRuleType[];
    clientId: string;
    source: SourceType;
    taskTransferTime: TimeLabel;
    taskArchiveTime: string;
    taskTargetBranch: string;
    taskTargetPath: string;
    dataSourceType: string;
    dataPathList: string[];
    accessKey: string;
    secretKey: string;
    remoteAddr: string;
    taskDelayTime: number;
    fileSuffix: string;
}

export interface ParamsModifyReflowTaskConfig {
    stackSpace: string;
    stackProject: string;
    taskId: number;
    clientId?: number | string;
    taskName?: string;
    taskFileName?: string;
    taskTransferTime?: Array<string | number> | string;
    taskTargetPath?: string;
    taskArchiveTime?: string;
    transferDesensitizations?: DesensitizationsRuleType[];
    dataPathList?: string[];
    accessKey?: string;
    secretKey?: string;
    remoteAddr?: string;
    [key: string]: any;
}

export interface ParamsProjectNameCheck {
    name: string;
}
