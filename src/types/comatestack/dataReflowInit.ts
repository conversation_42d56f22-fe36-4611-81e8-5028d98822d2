import {ProjectSecLevel} from './project';
import {MemberItem} from './suggestUsers';
import {DesensitizationsRuleType} from './dataReflow';

export interface SubmitPropsType {
    workspaceUuid: string;
    accountId: string;
    name: string;
    uuid: string;
    projectDesc: string;
    secLevel: ProjectSecLevel;
    visibleType: string;
    createWorkspace: boolean;
    admins: MemberItem[];
    reflowScenario: string;
    stackSpace: string;
    stackProject: string;
    clientId: string;
    taskName: string;
    taskFileName: string;
    taskTransferTime: string[];
    taskTargetPath: string;
    taskArchiveTime: string;
    taskTargetBranch: string;
    taskTransferEncryption: boolean;
    transferDesensitizations: DesensitizationsRuleType[];
    dataSourceType: string;
    dataPathList: string[];
    accessKey: string;
    secretKey: string;
    remoteAddr: string;
    taskDelayTime: number;
    fileSuffix: string;
}

export interface InitialValuesType {
    reflowScenario: string;
    workspaceUuid: string;
    name: string;
    uuid: string;
    description: string;
    visibleType: string;
    secLevel: string;
    createWorkspace: boolean;
    admins: MemberItem[];
    accountId: string;
    taskTransferTime: [string, number];
    taskTargetPath: string;
    taskArchiveTime: string;
    taskFileName: string;
    taskTargetBranch: string;
    taskTransferEncryption: boolean;
    dataSourceType: string;
    fileSuffix: string;
}

export interface CreateTaskType {
    workspaceUuid: string;
    accountId: string;
    uuid: string;
    projectDesc: string;
    reflowScenario: string;
    clientId: string;
    taskName: string;
    taskFileName: string;
    taskTransferTime: string[];
    taskTargetPath: string;
    taskArchiveTime: string;
    taskTargetBranch: string;
    taskTransferEncryption: boolean;
    transferDesensitizations: DesensitizationsRuleType[];
    dataSourceType: string;
    dataPathList: string[];
    accessKey: string;
    secretKey: string;
    remoteAddr: string;
    taskDelayTime: number;
    fileSuffix: string;
}
