import {
    ColDef,
    CellValueChangedEvent,
    ColumnMovedEvent,
    Column,
    RowNode,
} from '@/third-party/devops-federation/types';

export interface RenderProperty {
    opObj: 'TABLE' | 'ROW' | 'COLUMN' | 'CELL';
    rowId: string;
    colId: string;
    name: string;
    value: string;
}

export interface AGColDef extends ColDef {
    columnType?: string;
}

export type handleChangeFormattersType = (colId: string, formatterCodes: string[]) => void;

export interface AGGridProps {
    gridRef: any; // 必须提供
    gridId: string; // 必须提供
    loading?: boolean;
    rowData: any[];
    columns: AGColDef[];
    editable?: boolean;
    pagination?: {
        pageNumber?: number;
        pageSize?: number;
        offset?: number;
        limit?: number;
        totalRows: number;
        pageChangeMode?: 'page' | 'offset';
        onPageChange?: (pageNumber: number, pageSize: number) => void;
        onOffsetChange?: (offset: number, limit: number) => void;
    };
    renderProperties?: RenderProperty[];
    rowHeight?: number;
    rowIdName?: string; // 可以指定rowId的字段名，默认为每行的index
    onUpdateRenderProperties?: (renderProperties: RenderProperty[]) => void;
    onCellValueChanged?: (event: CellValueChangedEvent) => void;
    onColumnMoved?: (event: ColumnMovedEvent) => void;
    onRowAdded?: (rowId: string, rowIndex: number, position: 'up' | 'down', count: number) => void;
    onColAdded?: (
        colId: string,
        position: 'left' | 'right',
        newColumnDefs: ColDef[],
        handleChangeFormatters: handleChangeFormattersType
    ) => void;
    onRowRemoved?: (rowId: string) => void;
    onColRemoved?: (colId: string) => void;
    onSaveHeaderName?: (oldName: string, newName: string) => void;
}

export type ContextMenuType = 'Column' | 'Cell';

export interface ContextMenuProps {
    gridRef: any;
    gridId: string;
    editable: boolean;
    isOpen: boolean;
    position: {
        top: number;
        left: number;
    };
    type: ContextMenuType;
    currentColumn: Column;
    currentRowNode: RowNode;
    rowIdName: string;
    onChangeContextMenuType: (type: ContextMenuType) => void;
    onRowAdded: (rowId: string, rowIndex: number, position: 'up' | 'down', count: number) => void;
    onColAdded: (
        colId: string,
        position: 'left' | 'right',
        newColumnDefs: ColDef[],
        handleChangeFormatters: handleChangeFormattersType
    ) => void;
    onRowRemoved: (rowId: string) => void;
    onColRemoved: (colId: string) => void;
    closeContextMenu: () => void;
    handleChangeFormatters: handleChangeFormattersType;
    onSaveHeaderName: (oldName: string, newName: string) => void;
}
