import {createRegion, createMappedRegion} from 'region-core';
import {DatasetTableData} from '@/api/icode/dataset';
import {apiGetDraftDatasetTable} from '@/api/icode/datasetFileEdit';

// 记录编辑时的文件信息
interface FileEditInfo {
    filePath: string;
}

const fileEditInfoRegion = createRegion<FileEditInfo>({filePath: ''});

export const useFileEditInfo = fileEditInfoRegion.useValue;

export const setFileEditInfo = fileEditInfoRegion.set;

export const resetFileEditInfo = fileEditInfoRegion.reset;

// 记录编辑前的原始内容
const fileEditContentRegion = createRegion<string>('');

export const useFileEditContent = fileEditContentRegion.useValue;

export const setFileEditContent = fileEditContentRegion.set;

export const resetFileEditContent = fileEditContentRegion.reset;


// 记录当前文件的草稿的内容
const fileEditDraftRegion = createMappedRegion<string, DatasetTableData>();

export const useFileEditDraft = fileEditDraftRegion.useValue;

export const resetFileEditDraft = fileEditDraftRegion.reset;

export const useFileEditDraftLoading = fileEditDraftRegion.useLoading;

export const loadFileEditDraft = fileEditDraftRegion.loadBy(
    params => params.path,
    apiGetDraftDatasetTable
);


// 记录文件编辑模式 '' | CellMode | EditorMode
const fileEditModeRegion = createRegion<string>('');

export const useFileEditMode = fileEditModeRegion.useValue;

export const setFileEditMode = fileEditModeRegion.set;

export const resetFileEditMode = fileEditModeRegion.reset;


// 记录当前编辑的单元格位置和模式，格式：'' | 'x-y'
const fileEditPositionRegion = createRegion<string>('');

export const useFileEditPosition = fileEditPositionRegion.useValue;

export const setFileEditPosition = fileEditPositionRegion.set;

export const resetFileEditPosition = fileEditPositionRegion.reset;

export const useParsedFileEditPosition = () => {
    const xyPositions = useFileEditPosition().split('-');
    if (xyPositions.length === 2) {
        return {
            rowId: xyPositions[0],
            colName: xyPositions[1],
        };
    }

    return {
        rowId: '',
        colName: '',
    };
};


// 记录发布草稿弹框的打开状态
const filePublishModalOpenRegion = createMappedRegion<string, boolean>(false);

export const useFilePublishModalOpen = filePublishModalOpenRegion.useValue;

export const setFilePublishModalOpen = filePublishModalOpenRegion.set;

export const resetFilePublishModalOpen = filePublishModalOpenRegion.reset;


const tempFilePublishModalOpenRegion = createMappedRegion<string, boolean>(false);

export const useTempFilePublishModalOpen = tempFilePublishModalOpenRegion.useValue;

export const setTempFilePublishModalOpen = tempFilePublishModalOpenRegion.set;

export const resetTempFilePublishModalOpen = tempFilePublishModalOpenRegion.reset;


// 记录临时保存的内容，格式：'{rowId, colName, content}'
interface FileEditStashedItem {
    rowId: string;
    colName: string;
    content: string;
}

const fileEditStashedRecordsRegion = createRegion<FileEditStashedItem[]>([]);

export const useFileEditStashedRecords = fileEditStashedRecordsRegion.useValue;

export const stashFileEditStashedRecords = (fileEditStashedItem: FileEditStashedItem) => {
    const {rowId, colName, content} = fileEditStashedItem;
    const fileEditStashedItems = fileEditStashedRecordsRegion.getValue();
    const findItem = fileEditStashedItems.find(item => item.rowId === rowId && item.colName === colName);
    if (findItem) {
        findItem.content = content;
    }
    else {
        fileEditStashedItems.push(fileEditStashedItem);
    }

    fileEditStashedRecordsRegion.set(fileEditStashedItems);
};

export const setFileEditStashedRecords = fileEditStashedRecordsRegion.set;

export const useParsedFileEditStashedRecords = (rowId: string, colName: string) => {
    const fileEditStashedItems = useFileEditStashedRecords();
    const findItem = fileEditStashedItems.find(item => item.rowId === rowId && item.colName === colName);
    return findItem ? findItem.content : null;
};


// 记录正在保存的状态
const fileEditIsSavingContentRegion = createRegion<boolean>(false);

export const useFileEditIsSavingContent = fileEditIsSavingContentRegion.useValue;

export const setFileEditIsSavingContent = fileEditIsSavingContentRegion.set;
