import {createMappedRegion} from 'region-core';
import {useCallback} from 'react';
import {message} from '@panda-design/components';
import {useBatchTaskTotal} from '@/hooks/label/useLabelBatchTaskParams';

// 记录当前标注数据项的序号，第一项存储为0，展示时为1
const batchMarkTaskIndex = createMappedRegion<number, number>(0);

export const useBatchMarkTaskIndex = batchMarkTaskIndex.useValue;

export const setBatchMarkTaskIndex = batchMarkTaskIndex.set;

export const useSetBatchMarkTaskIndex = (key: number) => {
    const total = useBatchTaskTotal();
    const backward = useCallback(
        () => {
            setBatchMarkTaskIndex(key, prev => {
                if (prev <= 0) {
                    message.info('已经是第一个了');
                    return 0;
                }
                return prev - 1;
            });
        },
        [key]
    );
    const forward = useCallback(
        () => {
            setBatchMarkTaskIndex(key, prev => {
                if (prev >= total - 1) {
                    message.info('已经是最后一个了');
                    return total - 1;
                }
                return prev + 1;
            });
        },
        [key, total]
    );
    return {
        backward,
        forward,
    };
};

export const setBatchMarkTaskIndexNext = (key: number) => {
    setBatchMarkTaskIndex(key, prev => prev + 1);
};

export const resetBatchMarkTaskIndex = batchMarkTaskIndex.reset;
