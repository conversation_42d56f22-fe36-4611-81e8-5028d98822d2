import {createMappedRegion} from 'region-core';
import {FileNode} from '@/types/icode/repo';

/**
 * 只保存打开的真实文件，不保存文件夹，不保存临时文件
 * key: repoName
 */
const realFilesRegion = createMappedRegion<string, FileNode[]>([]);

export const useRealFiles = realFilesRegion.useValue;

export const pushRealFiles = (key: string, fileNode: FileNode) => {
    realFilesRegion.set(key, fileNodes => {
        const findNode = fileNodes.find(item => item.path === fileNode.path);
        if (findNode) {
            return fileNodes;
        }

        return [...fileNodes, fileNode];
    });
};

export const removeRealFiles = (key: string, path: string) => {
    realFilesRegion.set(key, fileNodes => {
        const index = fileNodes.findIndex(item => item.path === path);
        fileNodes.splice(index, 1);
        return [...fileNodes];
    });
};
