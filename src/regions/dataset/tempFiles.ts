import {createMappedRegion} from 'region-core';
import {TempFile} from '@/types/icode/datasetFileEdit';
import {apiGetTempFiles} from '@/api/icode/datasetFileEdit';

/**
 * key: repoName
 */
const tempFilesRegion = createMappedRegion<string, TempFile[]>([]);

export const useTempFiles = tempFilesRegion.useValue;

export const setTempFiles = tempFilesRegion.set;

export const pushTempFiles = (key: string, tempFile: TempFile) => {
    tempFilesRegion.set(key, tempFiles => {
        const findTempFile = tempFiles.find(item => item.path === tempFile.path);
        if (findTempFile) {
            return tempFiles;
        }

        return [...tempFiles, tempFile];
    });
};

export const removeTempFiles = (key: string, path: string) => {
    tempFilesRegion.set(key, tempFiles => {
        const index = tempFiles.findIndex(item => item.path === path);
        tempFiles.splice(index, 1);
        return [...tempFiles];
    });
};

export const loadTempFiles = tempFilesRegion.loadBy(
    params => params.dataset,
    apiGetTempFiles,
    (_, result) => {
        return result ?? [];
    }
);

export const useTempFileData = (repo: string, path: string) => {
    const tempFiles = useTempFiles(repo);
    const findTempFile = tempFiles.find(item => item.path === path);
    return {
        tempFileSessionId: findTempFile?.sessionId ?? '',
        tempFileOid: findTempFile?.oid ?? '',
    };
};
