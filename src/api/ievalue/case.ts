/* eslint-disable max-lines */
import {
    CaseHistoryTypeEnum,
    CaseStageStatusEnum,
    CaseStatusEnum,
    ChatStatusEnum,
} from '@/constants/ievalue/case';
import {
    ChatStepRecordTypeEnum,
    StrategyAutoComputeStatisticTypeEnum,
    StrategyAutoComputeVisibilityEnum,
    StrategyGenreEnum,
    StrategyMetricSelectTypeEnum,
    StrategyRunningModeEnum,
    StrategyTagCategoryEnum,
    StrategyTagDenominatorTypeEnum,
} from '@/constants/ievalue/evaluate';
import {FeedbackLikeEnum} from '@/constants/ievalue/task';
import {createInterface} from './axios';
import {
    Dimension,
    PromptExcuteResultListItem,
    PromptVariable,
    PromptVersionVersionItem,
} from './prompt-version';
import {ImageListItem, MessageItem, SearchResultsItem, UploadFileItem} from './prompt';
import {BaseTagItem} from './tags';

// case历史记录变更
export interface HistoryItem {
    ID: number;
    groupID: number;
    caseID: number;
    recordID: number;
    stageID: number;
    type: CaseHistoryTypeEnum;
    operator: string;
    field: string;
    origin: string;
    turnTo: string;
    text: string;
    Units?: HistoryItem[];
    createTime: string;
    updateTime: string;
}

export interface CaseHistoryListResponse {
    list: HistoryItem[];
}

export interface ParamsCase {
    caseID: number;
}

export interface ParamsTask {
    taskID: number;
}

export const apiCaseHistoryList = createInterface<
    ParamsCase,
    CaseHistoryListResponse
>('GET', '/audit/history/list');

export interface ChoiceItem {
    score: number;
    name: string;
    desc?: string;
    vetoPower?: boolean;
}

export interface MetricItem {
    ID?: number; // 一言定制
    category?: string; // 一言定制
    definition?: string; // 一言定制
    dimensions?: Dimension[]; // 一言定制
    inputTemplate?: any; // 一言定制
    name?: string; // 一言定制
    scoreStandard?: string; // 一言定制
    exclusiveParam?: any;
    template?: string;
    choices: ChoiceItem[];
    desc: string;
    metric: string;
    models?: string[];
    confidence?: number[];
    output?: string;
    genre?: string;
    selectType: StrategyMetricSelectTypeEnum;
    promptVersionID?: string;
    varColMap?: any;
    explain?: string;
    metricList?: string[];
    hasTag?: boolean;
    tags?: string[];
    hasNote?: boolean;
    isNoteRequired?: boolean;
    noScoring?: boolean;
    notRequired?: boolean;
    rule?: string;
}

export interface MetricFormItemParamItem extends MetricItem {
    checked: boolean;
}

export interface TagPolicyItem {
    statisticPolicyName: string;
    statisticCategory: StrategyTagCategoryEnum;
    smolecularTag: string[]; // 分子
    denominatorType: StrategyTagDenominatorTypeEnum; // 分母类型
    denominatorTag: string[]; // 分母
    note?: string;
}

export interface AutoComputeDepMetricItem {
    name: string;
    weight?: number;
}

export interface AutoComputeMetricItem {
    metric: string;
    dependentMetrics: AutoComputeDepMetricItem[];
    statisticType: StrategyAutoComputeStatisticTypeEnum;
    visibilityLevel: StrategyAutoComputeVisibilityEnum;
    desc: string;
    explain?: string;
}

export interface StagetegyItem {
    ID: number;
    spaceCode: string;
    name: string;
    orderType: string;
    runningMode: StrategyRunningModeEnum;
    genre: StrategyGenreEnum;
    metric: MetricItem[];
    descript: string;
    tags: any;
    evaluatorID?: number;
    needSort: number;
    canModifyInUse: number;
    sortMetric: string;
    isCustomTag?: boolean;
    tagPolicy: TagPolicyItem[];
    isAutoComputeMetric?: boolean;
    autoComputeMetric: AutoComputeMetricItem[];
    noteNames?: string[];
    status?: string;
    evalModelID?: number;
}

export interface StagetegyTaskItem extends StagetegyItem {
    taskID?: number;
}

export const apiStrategyTaskList = createInterface<
    ParamsTask,
    StagetegyTaskItem[]
>('GET', '/strategy/task/list');

export interface StrategyListParam {
    spaceCode: string;
    strategyID?: number;
    runningMode?: StrategyRunningModeEnum;
    genre?: StrategyGenreEnum;
}

// 评估策略列表
export const apiStrategyList = createInterface<
    StrategyListParam,
    StagetegyItem[]
>('GET', '/strategy/list');

interface GroupCaseListParam {
    groupID?: number;
    taskID?: number;
    input?: string;
    stageName?: string;
    stageStatus?: CaseStageStatusEnum;
    pn?: number;
    size?: number;
    tags?: string;
    feedbacks?: string;
    status?: string;
    pass?: number;
    modelFilters?: any;
    optionalMatch?: any;
    target?: string;
}
export interface OutputMultiData {
    images: UploadFileItem[];
    all: UploadFileItem[];
    files: UploadFileItem[];
    videos: UploadFileItem[];
    audios: UploadFileItem[];
}

export interface OutputItem {
    recordID: number;
    modelID: number;
    modelName: string;
    modelOutput: string;
    score: ScoreItem[];
    note: string;
    chatTaskID: string;
    timeUsed: number;
    status: string;
    outputMultiData: OutputMultiData;
    reasoningContent: string;
    searchResults: SearchResultsItem[];
}

export interface CaseItem {
    groupID: number;
    stageID: number;
    groupName: string;
    line: number;
    caseID: number;
    status: CaseStatusEnum;
    stageStatus: CaseStageStatusEnum;
    stageName: string;
    query: string;
    referenceOutput: string;
    output: OutputItem[];
    rank: number[];
    pass: number;
    tags: string[];
    multiReferenceOutput: IOption[];
    variable: IOption[];
    optionalRaw: string;
    caseLineNum: number;
}

export interface GroupCaseListResponse {
    list: CaseItem[];
    versionInfos: PromptVersionVersionItem[];
    total: number;
}
// 子任务用例列表
export const apiGroupCaseList = createInterface<
    GroupCaseListParam,
    GroupCaseListResponse
>('GET', '/group/case/list');

export interface EvaluateCaseItem {
    groupID: number;
    caseID: number;
    stageID: number;
    multiModal?: MultiModalItem;
    input: string;
    operators: any[];
    ranking: any[];
    tags: any[];
    referenceOutput: string;
    records?: Record<string, any>;
    optionalRaw?: string;
    header?: string;
    variables?: PromptVariable[];
}
export interface EvaluateCaseListResponse {
    caseList: EvaluateCaseItem[];
    total: number;
}

// 自动评估结果检查刷新
export const apiEvaluateAutoCheck = createInterface<ParamsTask, any>(
    'GET',
    '/evaluate/auto/check'
);

// 自动评估任务重试（仅支持算法自动化、AI Grading）
export const apiEvaluateAutoRetry = createInterface<ParamsTask>(
    'POST',
    '/evaluate/auto/retry'
);

// 检查是否可以进行自动评估
export const apiEvaluateAutoRetryCheck = createInterface<ParamsTask, boolean>(
    'POST',
    '/evaluate/auto/retry/check'
);

// 算法自动化/AI Grading自动化评估取消
export const apiEvaluateAutoCancel = createInterface<ParamsTask>(
    'POST',
    '/evaluate/auto/cancel_v1'
);

// 检测自动评估任务是否可以取消
export const apiEvaluateAutoCancelCheck = createInterface<ParamsTask, boolean>(
    'POST',
    '/evaluate/auto/cancel/check'
);
// 评估子任务用例列表
export const apiEvaluateCaseList = createInterface<
    GroupCaseListParam,
    EvaluateCaseListResponse
>('POST', '/evaluate/case/list');

interface EvaluateCaseListColumnParams {
    column: string;
    groupID: number;
}

export const apiEvaluateCaseListColumn = createInterface<
    EvaluateCaseListColumnParams,
    string[]
>('GET', '/evaluate/case/list/column');

export interface GroupCaseUpdateBody {
    caseID: number;
    input?: string;
    taskID?: number;
    groupID?: number;
    stageID?: number;
    referenceOutput?: string;
}

// 子任务用例更新
export const apiGroupCaseUpdate = createInterface<GroupCaseUpdateBody, string>(
    'POST',
    '/group/case/update'
);

export interface GroupCaseCreateBody
    extends Omit<GroupCaseUpdateBody, 'caseID' | 'stageID'> {
    l0: string;
    l1: string;
    optionalRaw: string;
    modelOutputMap: object;
}

// 子任务用例新增
export const apiGroupCaseCreate = createInterface<GroupCaseCreateBody, string>(
    'POST',
    '/group/case/create'
);

export interface ChatRecordCreateItem {
    chatInput: string;
    chatOutput: string;
}

export interface PredictCreateParamItem {
    modelID: number;
    input: string;
    output: string;
    chatRecordCreateParam: ChatRecordCreateItem[];
}

export interface GroupCaseCreateV2Body
    extends Omit<GroupCaseUpdateBody, 'caseID' | 'stageID'> {
    l0: string;
    l1: string;
    optionalRaw: string;
    predictCreateParams: PredictCreateParamItem;
}

// 子任务用例新增
export const apiGroupCaseCreateV2 = createInterface<
    GroupCaseCreateV2Body,
    void
>('POST', '/group/case/createV2');

interface GroupOutputCaseUpdateBody {
    recordID: number;
    output: string;
    caseID: number;
    taskID: number;
    groupID: number;
    stageID: number;
}

// 预测用例更新
export const apiGroupOutputCaseUpdate = createInterface<
    GroupOutputCaseUpdateBody,
    string
>('POST', '/predict/record/update');

export interface CaseEvaluateListParam {
    caseID: number;
    taskID: number;
    stageID: number;
}

export interface ScoreItem {
    desc: string;
    score: number | null;
    scoreName: string | null;
    scoreInput?: string;
    note?: string;
    tags?: BaseTagItem[];
    scoreList?: string[];
    metric: string;
    evaluateMode?: string;
    output?: string;
}

export interface FeedbackItem {
    ID: number;
    content: string;
    dislike: FeedbackLikeEnum;
    like: FeedbackLikeEnum;
    metric: string;
    metricID: number;
    spaceCode: string;
}

export interface AutoMetricItem {
    ID: number;
    autoScore: number;
    feedbacks: FeedbackItem[];
    manualScore: number;
    metric: string;
    name: string;
    output: string;
    overview: string;
    recordID: number;
    scoreName: string;
    spaceCode: string;
    note: string;
}

export interface OtherNoteItem {
    name: string;
    content: string;
}
export interface CaseEvaluateItem {
    ID: number;
    stageID: number;
    acceptRecordID?: number;
    disputeRecordID?: number;
    evaluateCaseID: number;
    caseID: number;
    modelID: number;
    predictRecordID: number;
    model: string;
    input: string;
    output: string | any;
    status: string;
    operator: string;
    note: string;
    otherNotes?: OtherNoteItem[];
    totalScore: number;
    completionTokens: number;
    promptTokens: number;
    timeUsed: number;
    tags: string[];
    createTime: string;
    score: ScoreItem[];
    promptVersion: PromptVersionVersionItem;
    variables: PromptVariable[];
    imageList: ImageListItem[];
    multiModal: UploadFileItem[];
    messages: MessageItem[];
    referenceOutput: string;
    flowRunID: number;
    metrics: AutoMetricItem[];
    isLiked?: number;
    needDiscussion?: number;
    reasoningContent?: string;
    searchResults?: SearchResultsItem[];
    outputMultiData?: OutputMultiData;
}

export interface CaseEvaluateRecordItem {
    diff?: CaseEvaluateItem;
    origin: CaseEvaluateItem;
    modelID?: number;
}
export interface IOption {
    key: string;
    value: string;
}
interface RankMapRecordItem {
    ranking: number[];
    operators: string[];
}

interface RankMapItem {
    origin: RankMapRecordItem;
    diff?: RankMapRecordItem;
}
export interface CaseEvaluateListResponse {
    records: CaseEvaluateRecordItem[];
    rankMap: RankMapItem;
}

// 查询case模型评估列表
export const apiCaseEvaluateList = createInterface<
    CaseEvaluateListParam,
    CaseEvaluateListResponse
>('GET', '/evaluate/record/list');

export interface UpdateCaseEvaluateScoreItem {
    desc?: string;
    metric: string;
    score: number | null;
    scoreName?: string;
    scoreList?: string[];
    scoreInput?: string;
    noScoring?: boolean;
    notRequired?: boolean;
}

export interface UpdateDiffItem {
    type?: CaseHistoryTypeEnum;
    caseID?: number;
    groupID?: number;
    recordID?: number;
    field?: string;
    origin?: string | null;
    turnTo?: string | null;
}

interface CaseEvaluateUpsertBody {
    predictRecordID: number;
    groupID: number;
    taskID: number;
    accepted?: number;
    stageID: number;
    caseID: number;
    totalScore?: number;
    score?: UpdateCaseEvaluateScoreItem[];
    tags?: string[];
    note?: string;
    diff?: UpdateDiffItem[];
}

// 更新case打分
export const apiCaseEvaluateUpsert = createInterface<
    CaseEvaluateUpsertBody,
    string
>('POST', '/evaluate/record/upsert');

interface CaseRecordNotesUpsertParam {
    caseID: number;
    otherNotes: OtherNoteItem[];
    predictRecordID: number;
    stageID: number;
    taskID: number;
}
// 更新record其他标签
export const apiCaseRecordNotesUpsert = createInterface<
    CaseRecordNotesUpsertParam,
    any
>('POST', '/evaluate/record/notes/upsert');

// 更新turn级别其他标签信息
export const apiChatRecordNotesUpdate = createInterface<
    {ID: number, otherNotes: OtherNoteItem[]},
    any
>('POST', '/chat/record/notes/update');

export interface GroupCaseInfoOutputItem {
    recordID: number;
    modelID: number;
    modelName: string;
    modelOutput: string;
    variable: IOption[];
}
export interface GroupCaseInfoItem {
    groupID: number;
    multiModal?: MultiModalItem;
    header?: string;
    groupName: string;
    caseID: number;
    status: CaseStatusEnum;
    line: number;
    query: string;
    optionalRaw: string;
    referenceOutput: string;
    output: GroupCaseInfoOutputItem[];
    ranking: number[];
    pass: number;
    stageID: number;
    stageName: string;
    stageStatus: string;
    policyID: number;
    queries: Query[];
    multiReferenceOutput?: IOption[];
    promptVersions?: PromptVersionVersionItem[];
    system?: string;
    l0?: string;
    l1?: string;
    referenceIndicator?: object[];
}

export interface Query {
    ID: number;
    caseID: number;
    image?: string;
    sequence: number;
    referenceOutput: string;
    input: string;
    output: string;
}

interface GroupCaseInfoParams {
    caseID: number;
    taskID: number;
}
export const apiGroupCaseInfo = createInterface<
    GroupCaseInfoParams,
    GroupCaseInfoItem
>('GET', '/group/case/info');

export const apiCaseEvaluatePass = createInterface<GroupCaseInfoParams, string>(
    'GET',
    '/evaluate/isPass'
);

export const apiCaseAuditPass = createInterface<GroupCaseInfoParams, string>(
    'GET',
    '/audit/case/finish'
);

interface CaseAuditForwardPassRes {
    caseFinish?: boolean;
    reachedThreshold?: boolean;
}

interface CaseAuditForwardPassParams extends GroupCaseInfoParams {
    pass?: number;
    stageID: number;
}

export const apiCaseAuditForwardPass = createInterface<
    CaseAuditForwardPassParams,
    CaseAuditForwardPassRes
>('GET', '/audit_forward/case/finish');

export const apiCaseAcceptancePass = createInterface<
    GroupCaseInfoParams,
    string
>('GET', '/acceptance/confirm');

interface GroupCaseRandomParams {
    caseID?: number;
    groupID?: number;
    taskID: number;
    stageID: number;
}

export interface GroupCaseRandomItem extends CaseItem {
    ID: number;
}

export const apiGroupCaseRandom = createInterface<
    GroupCaseRandomParams,
    GroupCaseRandomItem | string
>('GET', '/group/case/random');

interface GroupCaseDeleteParams {
    groupID: number;
    taskID: number;
    caseID: number;
}

export const apiGroupCaseDelete = createInterface<
    GroupCaseDeleteParams,
    {ID: number}
>('GET', '/group/case/delete');

export const apiGroupCaseNext = createInterface<
    GroupCaseRandomParams,
    GroupCaseRandomItem | string
>('GET', '/group/case/next');

export const apiGroupCaseLogGet = createInterface<{caseID: number}, any>(
    'GET',
    '/record/log/list'
);

interface EvaluateAcceptParams {
    taskID: number;
    originRecordID: number;
    targetRecordID: number;
}

export const apiEvaluateAccept = createInterface<EvaluateAcceptParams, string>(
    'GET',
    '/evaluate/accept'
);

export const apiEvaluateDispute = createInterface<EvaluateAcceptParams, string>(
    'GET',
    '/evaluate/dispute'
);

interface EvaluateCaseUpsertParams {
    stageID: number;
    caseID: number;
    groupID: number;
    taskID: number;
    ranking: number[];
    diff?: any[];
    operators?: string[];
}
export const apiEvaluateCaseUpsert = createInterface<
    EvaluateCaseUpsertParams,
    string
>('POST', '/evaluate/case/upsert');

interface EvaluateCaseUpsertOperatorsParams {
    taskID: number;
    stageID: number;
    caseID: number;
    groupID: number;
    operators: string[];
    diff?: any[];
}
export const apiEvaluateCaseUpsertOperators = createInterface<
    EvaluateCaseUpsertOperatorsParams,
    string
>('POST', '/evaluate/case/upsert');

interface EvaluateFinalDisputeParams {
    caseID: number;
    taskID: number;
    evaluateCaseID: number;
    evaluateRecordID: number;
}
export const apiEvaluateFinalDispute = createInterface<
    EvaluateFinalDisputeParams,
    string
>('GET', '/evaluate/finalDispute');

interface EvaluateRecordUpdateParams {
    ID: number;
    taskID: number;
    predictRecordID: number;
    evaluateCaseID: number;
    totalScore?: number;
    score?: UpdateCaseEvaluateScoreItem[];
    tags?: string[];
    note?: string;
    diff?: UpdateDiffItem[];
}

export const apiEvaluateRecordUpdate = createInterface<
    EvaluateRecordUpdateParams,
    string
>('POST', '/evaluate/record/update');

interface EvaluateRecordScoreHistoryParams {
    taskID: number;
    recordID: number;
}

export const apiEvaluateRecordScoreHistory = createInterface<
    EvaluateRecordScoreHistoryParams,
    Record<number, ScoreItem[]>
>('GET', '/evaluate/record/score/history');

interface EvaluateConfirmParams {
    taskID: number;
    caseID: number;
    originRecordID: number;
    targetRecordID: number;
    predictRecordID: number;
}

export const apiEvaluateConfirm = createInterface<
    EvaluateConfirmParams,
    string
>('GET', '/evaluate/confirm');

export const apiGroupRejectedFinish = createInterface<
    {groupID: number},
    string
>('GET', '/group/rejected/finish');

export interface ChatCreateResponse {
    ID: number;
    taskID: number;
    groupID: number;
    caseID: number;
    modelID: number;
    datasetID: number;
    policyID: number;
    promptVersionID: number;
    stageID: number;
    chatTaskID: string;
    status: string;
    line: number;
    input: string;
    variable?: any;
    output: string;
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
    score?: any;
    totalScore?: any;
    operator: string;
    note: string;
    startTime?: any;
    endTime?: any;
}
export const apiChatCreate = createInterface<
    {caseID: number, predictRecordID?: number},
    PromptExcuteResultListItem[]
>('GET', '/chat/create');
export const apiChatClose = createInterface<
    {caseID: number, predictRecordID?: number},
    PromptExcuteResultListItem[]
>('GET', '/chat/close');
export interface SendMsgResponse {
    groupID: number;
    caseID: number;
    ID: number;
    predictRecordID: number;
    sequence: number;
    chatTaskID: string;
    status: string;
    input: string;
    output: string;
}
export const apiChatSendQuery = createInterface<
    {caseID: number, predictRecordID?: number, input: string},
    SendMsgResponse
>('GET', '/chat/sendQuery');
export const apiGroupCaseQueryAdd = createInterface<
    {caseID: number, input: string},
    SendMsgResponse | Query
>('POST', '/group/case/query/add');

interface OtherInfoItem {
    columnName: string;
    value: string;
}

export interface ChatRecordItem {
    ID: number;
    predictRecordID: number;
    referenceOutput: string;
    sequence: number;
    chatTaskID: string;
    status: string;
    input: string;
    multiModal?: MultiModalItem;
    output: string;
    timeUsed: number;
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
    note: string;
    otherNotes: OtherNoteItem[];
    startTime: string;
    endTime: string;
    score?: any;
    tags?: string[];
    open?: boolean;
    optionalRaw?: any;
    image?: string;
    scoreUnChecked?: boolean;
    highlight?: string;
    audioLink?: string;
    event?: string;
    queryTime?: string;
    otherInfo?: OtherInfoItem[];
    specialModelParam?: any;
    isLiked?: number;
    needDiscussion?: number;
    reasoningContent?: string;
    searchResults?: SearchResultsItem[];
}

export interface ChatRecordListParams {
    predictRecordID: number;
    chatTaskID: string;
    chatRecordID?: number;
}

export const apiChatRecordList = createInterface<
    ChatRecordListParams,
    ChatRecordItem[]
>('GET', '/chat/record/list');

export interface ChatStatusItem {
    chatTaskID: string;
    err: string;
    modelID: number;
    predictRecordID: number;
    status: ChatStatusEnum;
}
export const apiChatGetStatus = createInterface<
    {caseID: number, predictRecordID?: number},
    ChatStatusItem[]
>('GET', '/chat/getStatus');

export interface ChatOutputUpdateBody {
    taskID: number;
    predictRecordID: number;
    stageID: number;
    ID: number;
    input?: string;
    output?: string;
    note?: string;
    tags?: string[];
    score?: any[];
    function?: object;
    functionResult?: object;
    responseFormat?: object;
}

// 更新模型输出结果
export const apiChatOutputUpdate = createInterface<ChatOutputUpdateBody, any>(
    'POST',
    '/chat/record/update'
);

interface ChatRecordDeleteBody {
    taskID: number;
    ID: number;
}

export const apiChatRecordDelete = createInterface<ChatRecordDeleteBody, any>(
    'POST',
    '/chat/record/delete'
);

export interface ChatRecordCreateBody {
    taskID?: number;
    chatTaskID?: string;
    predictRecordID?: number;
    preID?: number;
    input?: string;
    output?: string;
    function?: object;
    functionResult?: object;
    responseFormat?: object;
}

// 更新模型输出结果
export const apiChatRecordCreate = createInterface<ChatRecordCreateBody, any>(
    'POST',
    '/chat/record/create'
);

export interface ChatStepRecordListParams {
    chatRecordID: number;
    chatTaskID?: string;
    predictRecordID?: number;
}
// 灵境agent航线
export const apiChatStepRecordList = createInterface<
    ChatStepRecordListParams,
    any
>('GET', '/chat/step/record/list');

export interface LinkRecordListParams {
    taskID: number;
    predictRecordID?: number;
}

// 单轮航线
export const apiLinkRecordList = createInterface<
    LinkRecordListParams,
    ChatStepRecordItem[]
>('GET', '/link/record/list');

export interface ChatStepRecordItem {
    ID: string;
    predictRecordID: number;
    predictChatRecordID: number;
    chatTaskID: string;
    link_id: string;
    link_parent_id: string;
    type: ChatStepRecordTypeEnum;
    input: string;
    input_ext: string;
    output: string;
    output_ext: string;
    name: string;
    thought: string;
}

// 灵境agent航线
export const apiChatLinkRecordList = createInterface<
    ChatStepRecordListParams,
    ChatStepRecordItem[]
>('GET', '/chat/link/record/list');

interface GroupCaseFeatureListParam {
    groupFeatureID: number;
    stageName?: string;
    stageStatus?: CaseStageStatusEnum;
    pn?: number;
    size?: number;
    tags?: string;
}
export interface MultiType {
    url: string;
    name: string;
    type: string;
}

export interface MultiModalItem {
    images?: MultiType[];
    audios?: MultiType[];
    videos?: MultiType[];
    files?: MultiType[];
}

export interface CaseFeatureItem {
    caseFeatureID: number;
    caseID: number;
    groupFeatureID: number;
    groupID: number;
    groupName: string;
    header: string;
    multiModal?: MultiModalItem;
    l0: string;
    l1: string;
    line: number;
    multiReferenceOutput: IOption[];
    optionalRaw: string;
    output: GroupCaseInfoOutputItem[];
    pass: number;
    policyID: number;
    queries: string[];
    query: string;
    ranking: string;
    reference: string;
    referenceOutput: string;
    stageID: number;
    stageName: string;
    stageStatus: CaseStageStatusEnum;
    status: string;
    system: string;
    tags: string[];
    userName: string;
    variable: IOption[];
}

interface GroupCaseFeatureListResponse {
    list: CaseFeatureItem[];
    total: number;
}

export const apiGroupCaseFeatureList = createInterface<
    GroupCaseFeatureListParam,
    GroupCaseFeatureListResponse
>('GET', '/evaluate/group/case/feature/list');

interface GroupCaseFeatureInfoParams {
    caseFeatureID: number;
    taskID: number;
}

export interface GroupCaseFeatureInfoItem {
    caseFeatureID: number;
    groupID: number;
    multiModal?: MultiModalItem;
    groupName: string;
    caseID: number;
    status: string;
    line: number;
    query: string;
    optionalRaw: string;
    referenceOutput: string;
    output: GroupCaseInfoOutputItem[];
    ranking: number[];
    pass: number;
    stageID: number;
    stageName: string;
    stageStatus: string;
    policyID: number;
    queries: Query[];
    multiReferenceOutput?: IOption[];
    promptVersions?: PromptVersionVersionItem[];
    system?: string;
    referenceIndicator?: object[];
}

export const apiGroupCaseFeatureInfo = createInterface<
    GroupCaseFeatureInfoParams,
    GroupCaseFeatureInfoItem
>('GET', '/group/case/feature/info');

interface RecordFeatureListByCaseFeatureIDParams {
    caseFeatureID: number;
    taskID: number;
    stageID: number;
}

export interface RecordFeatureItem {
    ID: number;
    groupID: number;
    caseFeatureID: number;
    caseID: number;
    completionTokens: number;
    groupFeatureID: number;
    stageID: number;
    acceptRecordID?: number;
    disputeRecordID?: number;
    evaluateCaseID: number;
    modelID: number;
    predictRecordID: number;
    model: string;
    input: string;
    output: string | any;
    status: string;
    operator: string;
    note: string;
    totalScore: number;
    promptTokens: number;
    timeUsed: number;
    tags: string[];
    createTime: string;
    score: ScoreItem[];
    promptVersion: PromptVersionVersionItem;
    variables: PromptVariable[];
    imageList: ImageListItem[];
    multiModal: UploadFileItem[];
    messages: MessageItem[];
    referenceOutput: string;
    flowRunID: number;
    metrics: AutoMetricItem[];
    stageStatus: CaseStageStatusEnum;
    reasoningContent?: string;
    searchResults?: SearchResultsItem[];
    outputMultiData?: OutputMultiData;
}

interface RecordFeatureListByCaseFeatureIDResponse {
    recordFeatures: RecordFeatureItem[];
}

export const apiRecordFeatureListByCaseFeatureID = createInterface<
    RecordFeatureListByCaseFeatureIDParams,
    RecordFeatureListByCaseFeatureIDResponse
>('GET', '/evaluate/record/feature/listByCaseFeatureID');

export const apiEvaluateRecordFeatureListByRecordID = createInterface<
    {taskID: number, predictRecordID: number},
    RecordFeatureListByCaseFeatureIDResponse
>('GET', '/evaluate/record/feature/listByRecordID');

export const apiCaseFeatureEvaluatePass = createInterface<
    {caseFeatureID: number, taskID: number},
    string
>('GET', '/evaluate/case/feature/isPass');

export interface UpdateFeatureDiffItem {
    type?: CaseHistoryTypeEnum;
    caseFeatureID?: number;
    groupFeatureID?: number;
    predictRecordFeatureID?: number;
    field?: string;
    origin?: string | null;
    turnTo?: string | null;
}

interface CaseRecordFeatureUpdateBody {
    predictRecordFeatureID: number;
    stageID: number;
    groupFeatureID: number;
    caseFeatureID: number;
    totalScore?: number;
    score?: UpdateCaseEvaluateScoreItem;
    tags?: string[];
    note?: string;
    diff?: UpdateFeatureDiffItem[];
}

// 更新case打分
export const apiCaseRecordFeatureUpdate = createInterface<
    CaseRecordFeatureUpdateBody,
    string
>('POST', '/evaluate/record/feature/update');

interface TaskCaseAuthBatchUpdateBody {
    taskID: number;
    caseIDs: number[];
    username: string;
}

// 批量更新用例权限
export const apiTaskCaseAuthBatchUpdate = createInterface<
    TaskCaseAuthBatchUpdateBody,
    void
>('POST', '/task/case/auth/batchUpdate');

interface TaskPolicyDimensionNoteUpdateParams {
    predictRecordID: number;
    chatRecordID?: number;
    metric: string;
    note: string;
}

// 更新维度级备注
export const apiTaskPolicyDimensionNoteUpdate = createInterface<
    TaskPolicyDimensionNoteUpdateParams,
    string
>('POST', '/task/policy/metric/note/update');

interface TaskPolicyMetricTagUpdateParams {
    caseID: number;
    chatRecordID?: number;
    groupID: number;
    isChecked: boolean;
    metric: string;
    predictRecordID: number;
    stageID: number;
    tagID: number;
    tagName: string;
    taskID: number;
}

// 更新维度级标签
export const apiTaskPolicyMetricTagUpdate = createInterface<
    TaskPolicyMetricTagUpdateParams,
    string
>('POST', '/task/policy/metric/tag/update');

// 检查用例评估是否完成(case级别带打回)
export const apiTaskCaseEvaluateFinish = createInterface<
    GroupCaseInfoParams,
    void
>('GET', '/task/case/evaluate/finish');

// 检查用例验收是否完成(case级别带打回)
export const apiTaskCaseAcceptanceFinish = createInterface<
    GroupCaseInfoParams,
    void
>('GET', '/task/case/acceptance/finish');

interface TaskCaseAuditFinishParams {
    caseID: number;
    pass: boolean;
}

// 流转任务审核结束(case级别带打回)
export const apiTaskCaseAuditFinish = createInterface<
    TaskCaseAuditFinishParams,
    void
>('GET', '/task/case/audit/finish');

interface BatchFinishByCaseIDParams {
    taskID: number;
    caseIDs: number[];
}

interface BatchFinishByCaseIDRes {
    stageErrorCaseIDS: number[];
    finishCaseIDS: number[];
    unProcessedCaseIDS: number[];
    unAuthorizedCaseID: number[];
    successedCaseIDS: number[];
}

// 批量通过单向审核case列表
export const apiAuditForwardBatchFinishByCaseID = createInterface<
    BatchFinishByCaseIDParams,
    BatchFinishByCaseIDRes
>('POST', '/audit_forward/batch/finishByCaseID');

// 批量通过验收case列表
export const apiAcceptanceBatchFinishByCaseID = createInterface<
    BatchFinishByCaseIDParams,
    BatchFinishByCaseIDRes
>('POST', '/acceptance/batchFinishByCaseID');
