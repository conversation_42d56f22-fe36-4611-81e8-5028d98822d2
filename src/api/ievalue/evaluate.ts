/* eslint-disable max-lines */
import {
    EvaluateAccessWayEnum,
    EvaluateDeptEnum,
    EvaluateGranularityEnum,
    PolicyOptTaskStatusEnum,
} from '@/constants/ievalue/evaluate';
import {APP_WEBSOCKET_PREFIX} from '@/constants/app';
import {createOnceWebSocket} from '@/utils/createInterface/createOnceWebSocket';
import {createInterface} from './axios';
import {PromptWithVersionModel} from './prompt';

export interface EvaluatorMetricItem {
    name: string;
    metric: string;
}
export interface EvaluateEvaluatorItem {
    ID: number;
    spaceCode: string;
    name: string;
    modelName: string[];
    evaluateWay: string;
    accessWay: EvaluateAccessWayEnum;
    funName: string;
    accessURL: string;
    funcURL: string;
    note: string;
    granularity: EvaluateGranularityEnum;
    dept: EvaluateDeptEnum;
    metrics?: EvaluatorMetricItem[];
}

export interface SpaceCodeParam {
    spaceCode?: string;
}

export const apiEvaluateEvaluatorListGet = createInterface<
    SpaceCodeParam,
    EvaluateEvaluatorItem[]
>('GET', '/evaluate/evaluator/list');

export interface EvaluateEvaluatorUpdateParams {
    ID: number;
    name: string;
    accessURL: string;
    accessWay: EvaluateAccessWayEnum;
    queueURL?: string;
    cancelURL?: string;
    updateURL?: string;
    modelName?: string[];
}
export const apiEvaluateEvaluatorUpdate = createInterface<
    EvaluateEvaluatorUpdateParams,
    string
>('POST', '/evaluate/evaluator/update');

export const apiEvaluateEvaluatorCreate = createInterface<
    Omit<EvaluateEvaluatorUpdateParams, 'ID'>,
    string
>('POST', '/evaluate/evaluator/create');

interface EvaluateFileGenerateResponse {
    ID: number;
    name: string;
    url: string;
    spaceCode: string;
    uploadTime: string;
    taskID: number;
    datasetID: number;
}

export const apiEvaluateFileGenerate = createInterface<
    {taskID: number, datasetType: string},
    EvaluateFileGenerateResponse
>('POST', '/evaluate/temp/file/generate');

interface EvaluateMetricUpdateParams {
    ID: number;
    name?: string;
    manualScore?: number;
    autoScore?: number;
    scoreName?: string;
    output?: string;
    overview?: string;
    note?: string;
}

export const apiEvaluateMetricUpdate = createInterface<
    EvaluateMetricUpdateParams,
    any
>('POST', '/metric/update');
interface EvaluateFeedbackUpdateParams {
    ID: number;
    metric?: string;
    metricID?: number;
    like?: number;
    dislike?: number;
    content?: string;
}

export const apiEvaluateFeedbackUpdate = createInterface<
    EvaluateFeedbackUpdateParams,
    any
>('POST', '/feedback/update');

export interface Dimension {
    名称: string;
    定义: string;
    评价结果取值: string;
}

export interface EvaluateYiYanToolItem {
    ID?: number;
    category: string;
    definition: string;
    dimensions: Dimension[];
    inputTemplate: any;
    name: string;
    scoreStandard: string;
    spaceCode: string;
}

export const apiEvaluateYiYanToolList = createInterface<
    SpaceCodeParam,
    EvaluateYiYanToolItem[]
>('GET', '/evaluate/yiyan/tool/list');

export const apiEvaluateYiYanToolQuery = createInterface<
    {toolID: number},
    EvaluateYiYanToolItem
>('GET', '/evaluate/yiyan/tool/query');

export const apiEvaluateYiYanToolUpdate = createInterface<
    EvaluateYiYanToolItem,
    EvaluateYiYanToolItem
>('PUT', '/evaluate/yiyan/tool/update');

export const apiEvaluateYiYanToolCreate = createInterface<
    EvaluateYiYanToolItem,
    EvaluateYiYanToolItem
>('POST', '/evaluate/yiyan/tool/create');

export const apiEvaluateExtractMetric = createInterface<{promptVersionID: number}, string[]>(
    'GET',
    '/evaluate/policy/ai_grading/extract_metric'
);

interface EvaluateYiYanToolSubmetricResponse {
    dimensions: Dimension[];
}

export const apiEvaluateYiYanToolSubmetricList = createInterface<
    SpaceCodeParam,
    EvaluateYiYanToolSubmetricResponse
>('GET', '/evaluate/yiyan/tool/submetrics');

export const apiEvaluatePolicyOptTaskCreate = createInterface<any, void>(
    'POST',
    '/evaluate/policy/opt/task/create'
);

interface EvaluatePolicyOptTaskListParams {
    policyID: number;
    pn: number;
    size: number;
}

export interface PolicyOptResultRecordItem {
    consistency: string;
    subMetrics: Dimension[];
}

export interface EvaluatePolicyOptResultItem {
    name: string;
    origin: PolicyOptResultRecordItem;
    opt: PolicyOptResultRecordItem;
}

export interface EvaluatePolicyOptTaskItem {
    createTime?: string;
    ID: number;
    policyID: number;
    result: EvaluatePolicyOptResultItem[];
    sampleURL: string;
    spaceCode: string;
    status: PolicyOptTaskStatusEnum;
    updateTime: string;
}

interface EvaluatePolicyOptTaskListRes {
    list: EvaluatePolicyOptTaskItem[];
    total: number;
}

export const apiEvaluatePolicyOptTaskList = createInterface<
    EvaluatePolicyOptTaskListParams,
    EvaluatePolicyOptTaskListRes
>('GET', '/evaluate/policy/opt/task/list');

interface PolicyMetricUpdateSubMetricsParams {
    policyID: number;
    policyOptID: number;
    updateDatas: any;
}

export const apiPolicyMetricUpdateSubMetrics = createInterface<
    PolicyMetricUpdateSubMetricsParams,
    void
>('POST', '/metric/update/subMetrics');

/**
 * 生成评价指标的子指标
 */
export const apiEvaluatePolicySubmetricGenerate = createOnceWebSocket<any, any>(
    `${APP_WEBSOCKET_PREFIX}/api/ievalue/api/v1/ws/evaluate/policy/submetric/generate`
);

// 评估策略生成
export const apiEvaluatePolicyGenerate = createInterface<any, void>(
    'POST',
    '/policy/generate'
);

interface EvaluateYiyanToolCheckParams {
    toolName: string;
}

interface EvaluateYiyanToolCheckRes {
    exist: boolean;
    msg: string;
}

// 检查yiyan工具是否重复
export const apiEvaluateYiyanToolCheck = createInterface<EvaluateYiyanToolCheckParams, EvaluateYiyanToolCheckRes>(
    'GET',
    '/evaluate/yiyan/tool/check'
);

export interface PromptGenerateParams {
    维度: string;
    场景: string;
    打分规则: string;
    spaceCode: string;
}

// 单维度打分prompt生成
export const apiEvaluatePolicyPromptGenerate = createOnceWebSocket<PromptGenerateParams, PromptWithVersionModel>(
    `${APP_WEBSOCKET_PREFIX}/api/ievalue/api/v1/ws/policy/ai_grading/prompt_gen`
);

/**
 * 得分示例相关API
 */

// 维度项类型
interface DimensionItem {
    dimension: string;
    score: number;
}

// 分数信息类型
export interface ScoreInfo {
    createTime: string;
    creator: string;
    dimension: string;
    id: number;
    note: string;
    output: string;
    score: string;
    tags: string[];
    predictRecordID: number;
    caseID: number;
    [key: string]: string | string[] | number;
}

// 创建分数示例
export interface RecordScoreExampleCreateParams {
    dimensions: DimensionItem[]; // 维度及分数数组
    note: string; // 备注信息
    predictRecordID?: number;
    tags: string[]; // 标签数组
    taskID?: number;
    queryMd5?: string;
}

export const apiRecordScoreExampleCreate = createInterface<
    RecordScoreExampleCreateParams,
    string
>('POST', '/record/score/example/create');

// 获取维度列表
export type RecordExampleDimensionListParams =
    {
        recordID: number;
        taskID: number;
    }
    | {
        queryMd5: string;
    };

export const apiRecordExampleDimensionList = createInterface<
    RecordExampleDimensionListParams,
    {dimensions: string[]} // 返回维度名称数组
>('GET', '/record/example/dimension/list');

// 获取分数列表
export type RecordExampleScoreListParams = (
    {
        recordID: number;
        dimension: string;
        taskID: number;
    }
    | {
        queryMd5: string;
        dimension: string;
    }
);

export const apiRecordExampleScoreList = createInterface<
    RecordExampleScoreListParams,
    {score: string[]} // 返回分数数组
>('GET', '/record/example/score/list');

// 获取分数详情
export type RecordExampleScoreInfoParams = (
    {
        recordID: number;
        taskID: number;
        dimension: string;
        score: string;
    }
    | {
        queryMd5: string;
        dimension: string;
        score: string;
    }
);

export const apiRecordExampleScoreInfo = createInterface<
    RecordExampleScoreInfoParams,
    {
        scoreList: ScoreInfo[]; // 分数详情列表
        total: number; // 总数
    }
>('GET', '/record/example/score/info');

export interface DatasetListWithQueryMd5Params {
    datasetID: number;
    pn?: number;
    size?: number;
}

export interface QueryMd5Item {
    id: number;
    query: string;
    queryMd5: string;
    hasExample: boolean;
}

export interface DatasetListWithQueryMd5Response {
    list: QueryMd5Item[];
    total: number;
}

export const apiDatasetListWithQueryMd5 = createInterface<
    DatasetListWithQueryMd5Params,
    DatasetListWithQueryMd5Response
>('GET', '/dataset/case/listWithQueryMd5');

// 从数据集创建评分记录
export interface CreateFromDatasetParams {
    id?: number; // 记录ID (可选)
    output?: string; // 输出内容 (可选)
    score: string; // 评分值 (必需)
    note?: string; // 备注信息 (可选)
    tags?: string[]; // 标签列表 (可选)
    dimension: string; // 评分维度 (必需)
    creator?: string; // 创建人 (可选)
    createTime?: string; // 创建时间 (可选)
    predictRecordID?: number; // 预测记录ID (可选)
    caseID?: number; // 用例ID (可选)
    queryMd5: string; // 查询的MD5值 (必需)
    datasetID: number;
}

export const apiCreateExampleFromDataset = createInterface<
    CreateFromDatasetParams,
    string
>('POST', '/record/score/example/createFromDataset');

export interface UpdateScoreExampleParams {
    id: number; // 打分示例ID (必需)
    output?: string; // 示例输出内容 (可选)
    score?: string; // 打分结果 (可选)
    note?: string; // 备注信息 (可选)
    tags?: string[]; // 标签列表 (可选)
    dimension?: string; // 评分维度 (可选)
    creator?: string; // 创建者 (可选)
    createTime?: string; // 创建时间 (可选)
    predictRecordID?: number; // 预测记录ID (可选)
    caseID?: number; // 用例ID (可选)
    queryMd5?: string; // 查询内容的MD5值 (可选)
    deleted?: number;
}

export const apiUpdateScoreExample = createInterface<
    UpdateScoreExampleParams,
    string
>('POST', '/record/score/example/update');

export type HistoryAction = 'create' | 'update' | 'delete';

export interface ScoreHistory {
    ID: number;
    action: HistoryAction;
    caseID: number;
    datasetID: number;
    dimension: string;
    exampleID: number;
    groupID: number;
    name: string;
    operateTime: string;
    operator: string;
    policyID: number;
    predictRecordID: number;
    queryMd5: string;
    score: string;
    spaceCode: string;
    stage: string;
    stageID: number;
    taskID: number;
    origin?: string;
    to?: string;
    field?: 'note' | 'tags';
}

export const apiScoreHistoriesList = createInterface<
    {
        recordID: number;
        taskID: number;
        queryMd5?: string;
    },
    ScoreHistory[]
>('GET', '/record/example/histories');


interface TasksZipFileGenerateParams {
    taskIDs: number[];
    datasetType?: string;
    spaceCode: string;
}

/**
 * 获取指定任务集的结果集压缩包
 */
export const apiTasksZipFileGenerate = createOnceWebSocket<
    TasksZipFileGenerateParams,
    string
>(`${APP_WEBSOCKET_PREFIX}/api/ievalue/api/v1/ws/tasks/zip/file/generate`);
