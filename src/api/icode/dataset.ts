/* eslint-disable max-len */
/**
 * @file 代码库相关的API
 */
import {ParamsGetQualityReport, QualityReportInfo} from '@/types/icode/qualityReport';
import {createInterface} from '@/utils/icode/api/createInterface';
import {createFileZipInterface} from '@/utils/icode/api/createFileZipInterface';
import {
    DatasetInfo,
    DatasetMetaDataTags,
    CommitPartUploadFileInfo,
    LinkReportInfo,
    LinkTagInfo,
    LinkModelInfo,
    FileMetaTargetType,
    FileMetaTarget,
    TrainScene,
    TrainTarget,
    SecLevel,
    RequirementInfo,
    PublishedVersion,
} from '@/types/icode/dataset';
import {Category} from '@/types/icode/nav';

export interface ParamsPostCreateDataset {
    datasetName: string;
    secret: boolean;
    luoShuAccountId: string;
    username: string;
    description?: string;
    secLevel: SecLevel;
    trainScenes: string;
}

// 创建数据集
export const apiPostCreateDataset = createInterface<ParamsPostCreateDataset, void>(
    'POST',
    '/rest/git/dataset/bpmCreateDataset'
);

export interface ParamsGetDatasetList {
    role?: Category;
    queryType: 'repoName';
    desc: boolean;
    pageNumber: string;
    pageSize: string;
    datasetName: string;
}

// 获取数据集列表
export const apiGetDatasetList = createInterface<ParamsGetDatasetList, DatasetInfo[]>(
    'GET',
    '/rest/git/dataset/self/datasetInfo'
);

export interface ParamsGetDatasetFileTree {
    dataSet: string;
    branch: string;
}

export interface DatasetFile {
    uuid: string;
    filePath: string;
    version: string;
    oid: string;
    rows: number;
    updatedTime: string;
    status: 'PENDING' // 索引中
        | 'RUNNING' // 索引中
        | 'SUCCESS' // 索引成功
        | 'FAILED'; // 索引失败
}

// 获取数据集文件列表
export const apiGetDatasetFileTree = createInterface<ParamsGetDatasetFileTree, DatasetFile[]>(
    'GET',
    '/rest/dataset-viewer/file/tree'
);

// 重新索引数据集
export const apiPostRebuildDataset = createInterface<Array<DatasetFile['uuid']>>(
    'POST',
    '/rest/dataset-worker/index/rebuild'
);

interface ParamsGetDatasetTable {
    dataSet: string;
    branch?: string;
    filePath?: string;
    pageSize: number;
    pageNum: number;
    oid?: string;
}

export interface DatasetTableRow {
    _iDataSetRowNumber: number;
    [key: string]: unknown;
}

export interface DatasetTableData {
    totalRows: number;
    totalPages: number;
    currentPage: number;
    features: Array<{ // 表格列信息
        name: string;
        dtype: string;
    }>;
    rows: any[];
}

// 分页获取数据集
export const apiGetDatasetTable = createInterface<ParamsGetDatasetTable, DatasetTableData>(
    'GET',
    '/rest/dataset-viewer/view/display'
);

interface ParamsGetMatchedDatasetTableRows extends Omit<ParamsGetDatasetTable, 'pageSize' | 'pageNum'> {
    query: string;
}

export const apiGetMatchedDatasetTableRows = createInterface<ParamsGetMatchedDatasetTableRows, DatasetTableData>(
    'GET',
    '/rest/dataset-viewer/view/search'
);

export interface ParamsGetPublishedVersionList {
    path: string;
    repo: string;
    oid: string; // 这个 oid 是文件树中 FileNode 的 oid，即 lfsOid
}

export const apiGetPublishedVersionList = createInterface<ParamsGetPublishedVersionList, PublishedVersion[]>(
    'GET',
    '/rest/data/office/file/publish/versions'
);

interface ParamsFileEditWithMerge {
    name: string;
    subject: string;
    branch?: string;
    isDsReadme?: boolean;
    path: string;
    content: string;
}

// 直接把修改的文件内容merge到对应分支
export const apiPostFileEditWithMerge = createInterface<ParamsFileEditWithMerge, boolean>(
    'POST',
    '/rest/review/setreview/{name}/change/review/createWithoutReview',
    {headers: {'Content-Type': 'application/x-www-form-urlencoded'}}
);

export interface ParamsGetDatasetMetaDataTags {
    repo: string;
    branch: string;
}

// 获取数据集metadata
export const apiGetDatasetMetaDataTags = createInterface<ParamsGetDatasetMetaDataTags, DatasetMetaDataTags>(
    'GET',
    '/rest/review/dataset/viewer/meta/all'
);

interface ParamsGetUpcomingDatasetList {
    repo: string;
    pageSize: number;
    pageNumber: number;
}

interface ResultGetUpcomingDatasetList {
    requirementInfo: RequirementInfo[];
}

// 获取上车列表
export const apiGetOnboardList = createInterface<ParamsGetUpcomingDatasetList, ResultGetUpcomingDatasetList>(
    'GET',
    '/rest/repocore/dataset/core/onboard/list/all'
);


export const apiGetQualityReport = createInterface<ParamsGetQualityReport, QualityReportInfo>(
    'GET',
    '/rest/repocore/qc/result'
);

interface FeedbackInfo {
    result: string;
    reason?: string;
}

interface ParamsPostFeedback {
    taskId: string |undefined;
    lines: any[];
    feedback: FeedbackInfo;
}

export const apiPostFeedBack = createInterface<ParamsPostFeedback, void>(
    'POST',
    '/rest/repocore/qc/{taskId}/feedback'
);

interface ParamsPostCancelFeedback {
    taskId: string | undefined;
    line: number;
}

export const apiPostCancelFeedBack = createInterface<ParamsPostCancelFeedback, void>(
    'POST',
    '/rest/repocore/qc/{taskId}/{line}/feedback/cancel'
);

interface ParamsGetOnboardDetail {
    repo: string;
    requirementId: string;
}

interface ResultGetOnboardDetail {
    requirementInfo: RequirementInfo[];
}

export const apiGetOnboardDetail = createInterface<ParamsGetOnboardDetail, ResultGetOnboardDetail>(
    'GET',
    '/rest/repocore/dataset/core/onboard/requirement/detail'
);

export interface ParamsGetRequirementDetail {
    repo: string;
    requirementId: string;
}

interface ResultGetRequirementDetail {
    requirementInfo: RequirementInfo[];
}

export const apiGetRequirementDetail = createInterface<ParamsGetRequirementDetail, ResultGetRequirementDetail>(
    'GET',
    '/rest/repocore/dataset/core/onboard/requirement/detail'
);
interface GetAvailableDates {
    availableDates: string[];
}

export const apiGetAvailableDates = createInterface<void, GetAvailableDates>(
    'GET',
    '/rest/repocore/dataset/core/onboard/getAvailableDates'
);

interface ParamsGetOnboardWithdraw {
    repo: string;
    fileId: number;
    reason: string;
}

// 撤销上车
export const apiGetOnboardWithdraw = createInterface<ParamsGetOnboardWithdraw, boolean>(
    'POST',
    '/rest/repocore/dataset/core/onboard/withdraw'
);

interface ParamsPostSmallLfsFileUpload {
    sha256: string;
    repo: string;
    branch: string;
    path: string;
    message: string;
    trainScene: string;
    trainTarget: string;
    file: File;
}

// 数据集上传文件（小于10M）
export const apiPostSmallLfsFileUpload = createInterface<ParamsPostSmallLfsFileUpload, void>(
    'POST',
    '/rest/files/lfs/directUpload?repo={repo}&branch={branch}&path={path}&message={message}&sha256={sha256}&trainScene={trainScene}&trainTarget={trainTarget}',
    {headers: {'Content-Type': 'multipart/form-data'}}
);

interface ParamsGetDownloadEncryptionLfsByOid {
    repo: string;
    path: string;
    oid: string;
}

// 数据集下载L5级别数据集的文件
export const apiGetDownloadEncryptionLfsByOid = createFileZipInterface<ParamsGetDownloadEncryptionLfsByOid, Blob>(
    'GET',
    '/rest/files/lfs/downloadEncryptionLfsByOid'
);

interface ParamsPostUploadFromBOS {
    url: string;
    repo: string;
    branch: string;
    path: string;
    commitMsg: string;
    trainScene: string;
    trainTarget: string;
}

interface ResultPostUploadFromBOS {
    commitId: string;
    metaChangeUnlockSuccess: boolean;
}

export const apiPostUploadFromBOS = createInterface<ParamsPostUploadFromBOS, ResultPostUploadFromBOS>(
    'POST',
    '/rest/files/lfs/uploadFromBOS'
);

interface ParamsPostInitPartUpload {
    sha256: string;
    repo: string;
    branch: string;
    path: string;
    allPartNum: number;
}

type ResultPostInitPartUpload = string;

// 启动分片上传
export const apiPostInitPartUpload = createInterface<ParamsPostInitPartUpload, ResultPostInitPartUpload>(
    'POST',
    '/rest/files/lfs/initPartUpload?repo={repo}&branch={branch}&path={path}&sha256={sha256}&allPartNum={allPartNum}'
);

interface ParamsPostProcessPartUpload {
    sha256: string;
    uploadId: string;
    currentPartNum: number;
    file: File;
}

type ResultPostProcessPartUpload = string;

// 上传分片
export const apiPostProcessPartUpload = createInterface<ParamsPostProcessPartUpload, ResultPostProcessPartUpload>(
    'POST',
    '/rest/files/lfs/processPartUpload?sha256={sha256}&uploadId={uploadId}&currentPartNum={currentPartNum}',
    {headers: {'Content-Type': 'multipart/form-data'}}
);

interface ParamsPostCompletePartUpload {
    sha256: string;
    uploadId: string;
    onlyUpload: boolean;
    message: string;
}

interface ResultPostCompletePartUpload {
    repo: string;
    branch: string;
    operator: string;
    path: string;
    uploadId: string;
    size: number;
}

// 完成所有分片上传后将文件送入数据库
export const apiPostCompletePartUpload = createInterface<ParamsPostCompletePartUpload, ResultPostCompletePartUpload>(
    'POST',
    '/rest/files/lfs/completePartUpload?sha256={sha256}&uploadId={uploadId}&onlyUpload={onlyUpload}&message={message}'
);

interface ParamsPostCommitPartUpload {
    repo: string;
    branch: string;
    message: string;
    files: CommitPartUploadFileInfo[];
    trainScene: string;
    trainTarget: string;
}

// 完成所有分片上传后将文件commit到指定代码库
export const apiPostCommitPartUpload = createInterface<ParamsPostCommitPartUpload, void>(
    'POST',
    '/rest/files/lfs/commitPartUpload'
);

interface ParamsPostCancelPartUpload {
    sha256: string;
    uploadId: string;
}

// 取消分片上传
export const apiPostCancelPartUpload = createInterface<ParamsPostCancelPartUpload, void>(
    'POST',
    '/rest/files/lfs/cancelPartUpload?sha256={sha256}&uploadId={uploadId}'
);

interface ParamsGetDownloadLfsByOid {
    oid: string;
    repo: string;
    path: string;
}

export const apiGetDownloadLfsByOid = createInterface<ParamsGetDownloadLfsByOid, string>(
    'GET',
    '/rest/files/lfs/downloadLfsByOid'
);

interface ParamsGetisSignAgreement {
    type: string;
}

interface SignAgreementData {
    admin: boolean;
    companyName: string;
    signedConfidentialAgreement: boolean;
    username: string;
}

export const apiGetisSignAgreement = createInterface<ParamsGetisSignAgreement, SignAgreementData>(
    'GET',
    '/git/user/name'
);

interface ParamsPostSignAgreement {
    type: string;
}

export const apiPostSignAgreement = createInterface<ParamsPostSignAgreement, void>(
    'POST',
    '/git/user/confidentialAgreement/sign?type={type}'
);

interface ParamsPostDeleteLfs {
    repo: string;
    branch: string;
    message: string;
    paths: string[];
}

export const apiPostDeleteLfs = createInterface<ParamsPostDeleteLfs, string>(
    'POST',
    '/rest/files/file/{repo}/delete'
);

export interface ParamsPostGetLinkModel {
    repoName: string;
    id?: string[];
    pageNumber?: number;
    pageSize?: number;
}

export const apiPostGetLinkModel = createInterface<ParamsPostGetLinkModel, LinkModelInfo[]>(
    'POST',
    '/rest/git/dataset/getLinkModel'
);

export interface ParamsPostGetLinkReport {
    repoName: string;
    id?: string[];
    pageNumber?: number;
    pageSize?: number;
}

export const apiPostGetLinkReport = createInterface<ParamsPostGetLinkReport, LinkReportInfo[]>(
    'POST',
    '/rest/git/dataset/getLinkReport'
);

export interface ParamsPostGetLinkTag {
    repoName: string;
    id?: string[];
    pageNumber?: number;
    pageSize?: number;
}

export const apiPostGetLinkTag = createInterface<ParamsPostGetLinkTag, LinkTagInfo[]>(
    'POST',
    '/rest/git/dataset/getLinkTag'
);

export interface ParamsPostGetFileMetaData {
    repoName: string;
    commit: string;
    target: FileMetaTarget[];
}

export interface ResultPostGetFileMetaData {
    path?: string;
    type?: FileMetaTargetType;
    secLevel?: string;
    tag: number[] | null;
    model: number[] | null;
    linkReport: number[] | null;
    trainScene: TrainScene[] | null;
    trainTarget: TrainTarget[] | null;
}

// 获取对文件级的元数据信息
export const apiPostGetFileMetaData = createInterface<ParamsPostGetFileMetaData, ResultPostGetFileMetaData[]>(
    'POST',
    '/rest/git/dataset/getMeta'
);

interface ParamsPostUpdateFileMetaData {
    repoName: string;
    branch: string;
    target: Array<{
        path: string;
        type: FileMetaTargetType;
        tag: number[] | null;
        model: number[] | null;
        linkReport: number[] | null;
        trainScene: TrainScene[] | null;
        trainTarget: TrainTarget[] | null;
    }>;
}

// 更新文件级的元数据信息
export const apiPostUpdateFileMetaData = createInterface<ParamsPostUpdateFileMetaData, boolean>(
    'POST',
    '/rest/git/dataset/updateMeta'
);

interface ParamsPostAddLinkModel {
    repoName: string;
    model: {
        id: number | null;
        name: string;
        majorVersion: string;
        minorVersion: string;
        trainInfo: string;
        type: string;
        description: string;
        link: string;
    };
}

// 新增或者更新关联模型
export const apiPostAddLinkModel = createInterface<ParamsPostAddLinkModel, LinkModelInfo>(
    'POST',
    '/rest/git/dataset/linkModel'
);

interface ParamsPostAddLinkReport {
    repoName: string;
    report: {
        id: number | null;
        name: string;
        modelVersion: string;
        platform: string;
        description: string;
    };
}

// 新增或者更新评估报告
export const apiPostAddLinkReport = createInterface<ParamsPostAddLinkReport, LinkReportInfo>(
    'POST',
    '/rest/git/dataset/linkReport'
);

interface ParamsPostAddLinkTag {
    repoName: string;
    content: string;
    id: number | null;
}

// 新增或者更新标签
export const apiPostAddLinkTag = createInterface<ParamsPostAddLinkTag, LinkTagInfo>(
    'POST',
    '/rest/git/dataset/linkTag'
);

interface GetModelNameIsExist {
    repoName: string;
    modelName: string;
}

// 模型名称是否可用, 名称可用true，名称不可用false
export const apiGetModelNameIsExist = createInterface<GetModelNameIsExist, boolean>(
    'GET',
    '/rest/git/dataset/check/modelName'
);

interface GetReportNameIsExist {
    repoName: string;
    reportName: string;
}

// 评估报告名称是否可用, 名称可用true，名称不可用false
export const apiGetReportNameIsExist = createInterface<GetReportNameIsExist, boolean>(
    'GET',
    '/rest/git/dataset/check/reportName'
);

interface GetGetTagNameIsExist {
    repoName: string;
    tagContent: string;
}

// 自定义标签名称是否可用, 名称可用true，名称不可用false
export const apiGetTagNameIsExist = createInterface<GetGetTagNameIsExist, boolean>(
    'GET',
    '/rest/git/dataset/check/tagContent'
);

interface ParamsPostRollbackVersion {
    datasetRepo: string;
    branch: string;
    rollbackVersion: string;
    filePath: string;
}

// 回滚数据集文件到指定版本，回滚成功true，否则false
export const apiPostRollbackVersion = createInterface<ParamsPostRollbackVersion, boolean>(
    'POST',
    '/rest/data/office/file/rollback?datasetRepo={datasetRepo}&branch={branch}&rollbackVersion={rollbackVersion}&filePath={filePath}'
);
