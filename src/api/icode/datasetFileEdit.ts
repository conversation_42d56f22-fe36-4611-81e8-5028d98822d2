/**
 * @file 数据集文件在线编辑相关的API
 */

import {createInterface} from '@/utils/icode/api/createInterface';
import {DatasetFileEditStatus, TempFile, ParamsCreateFile} from '@/types/icode/datasetFileEdit';
import {DatasetTableData} from './dataset';

interface ParamsBase {
    dataset: string;
    path: string;
    oid: string;
}

interface ParamsGetDatasetFileEditStatus {
    dataset: string;
    path: string;
    oid: string;
}

interface ParamsGetDraftDatasetTable extends ParamsBase {
    sessionId: string;
    query: string;
    pageSize?: number;
    pageNum?: number;
    offset?: number;
    limit?: number;
}

interface ParamsPostStartEditDatasetFile extends ParamsBase {
    editSource: string;
    query?: string;
}

interface ParamsPostRenewEditSessionId extends ParamsBase {
    timestamp: number;
    sessionId: string;
}

interface ParamsPostSaveEditUpdateObject {
    column?: string;
    from?: string;
    to?: string;
}

interface ParamsPostSaveDatasetFileEdit extends ParamsBase {
    timestamp: number;
    sessionId: string;
    opType: string;
    updateRowId?: string;
    updateContent?: ParamsPostSaveEditUpdateObject;
}

interface EditAcquireResult {
    message: string;
    sessionId: string;
}

interface ParamsPostSaveDatasetFileDraft extends ParamsBase {
    timestamp: number;
    sessionId: string;
}

interface ParamsPostPublishDatasetFileDraft extends ParamsPostSaveDatasetFileDraft {
    description: string;
}

interface ParamsPostDeleteDatasetFileDraft extends ParamsBase {
    timestamp: number;
    sessionId: string;
}

// 获取数据集文件在线编辑状态
export const apiGetDatasetFileEditStatus = createInterface<ParamsGetDatasetFileEditStatus, DatasetFileEditStatus>(
    'GET',
    '/rest/data/editor/edit/status'
);

// 获取草稿内容（此接口通过传入的sessionId来区别「已保存」和「编辑中」的草稿内容）
export const apiGetDraftDatasetTable = createInterface<ParamsGetDraftDatasetTable, DatasetTableData>(
    'GET',
    '/rest/data/editor/edit/content'
);

// 发起编辑请求
export const apiPostAcquireEditFile = createInterface<ParamsPostStartEditDatasetFile, EditAcquireResult>(
    'POST',
    '/rest/data/editor/edit/acquire'
);

// 续期编辑态
export const apiPostRenewEditSessionId = createInterface<ParamsPostRenewEditSessionId, string>(
    'POST',
    '/rest/data/editor/edit/renewal'
);

// 保存单次变更
export const apiPostSaveDatasetFileEdit = createInterface<ParamsPostSaveDatasetFileEdit,
    {rows: any[], colNames: string[]}
>(
    'POST',
    '/rest/data/editor/edit/save'
);

// 发布草稿
export const apiPostPublishDatasetFileDraft = createInterface<ParamsPostPublishDatasetFileDraft, string>(
    'POST',
    '/rest/data/editor/edit/publish'
);

// 删除草稿
export const apiPostDeleteDatasetFileDraft = createInterface<ParamsPostDeleteDatasetFileDraft, string>(
    'POST',
    '/rest/data/editor/edit/drop'
);

// 新建临时文件
export const apiPostCreateTempFile = createInterface<ParamsCreateFile, TempFile>(
    'POST',
    '/rest/data/editor/edit/create/sheet'
);

interface ParamsGetTempFiles {
    dataset: string;
}

// 获取新建的临时文件
export const apiGetTempFiles = createInterface<ParamsGetTempFiles, TempFile[]>(
    'GET',
    '/rest/data/editor/edit/preview/newsheet'
);
