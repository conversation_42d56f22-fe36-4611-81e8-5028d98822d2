import type { SVGProps } from "react";
const SvgSendActived = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 28 28"
        {...props}
    >
        <path
            fill="url(#SendActived_svg__a)"
            fillRule="evenodd"
            d="M26.237 5.731a1 1 0 0 0-1.303-1.108l-22.5 7.5-.077.029c-.752.316-.829 1.378-.098 1.79l6.497 3.665-.006 3.463.003.072a1 1 0 0 0 1.39.849l3.63-1.554 7.985 4.505a1 1 0 0 0 1.479-.711zm-5.286 5.553a1 1 0 0 0-1.341-1.48l-6 5-.061.054a1 1 0 0 0 1.34 1.481l6-5z"
            clipRule="evenodd"
        />
        <defs>
            <linearGradient
                id="SendActived_svg__a"
                x1={23.268}
                x2={-5.262}
                y1={-10.805}
                y2={0.326}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#00DAE5" />
                <stop offset={0.472} stopColor="#00B3FF" />
                <stop offset={1} stopColor="#0080FF" />
            </linearGradient>
        </defs>
    </svg>
);
export default SvgSendActived;
