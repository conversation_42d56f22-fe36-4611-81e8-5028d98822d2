import type { FC, SVGProps } from "react";
import { createIcon } from "@panda-design/components";
import AiTools1 from "./AiTools1";
import AiTools2 from "./AiTools2";
import AiTools3 from "./AiTools3";
import AiTools4 from "./AiTools4";
import SendActived from "./SendActived";
import Alert from "./Alert";
import ArrowRight from "./ArrowRight";
import Copy from "./Copy";
import Debug from "./Debug";
import Delete from "./Delete";
import Detail from "./Detail";
import Elipsis from "./Elipsis";
import ExitFullscreen from "./ExitFullscreen";
import Eye from "./Eye";
import Fullscreen from "./Fullscreen";
import Import from "./Import";
import LeftOutlined from "./LeftOutlined";
import LightMySpcae from "./LightMySpcae";
import LightPlayground from "./LightPlayground";
import List from "./List";
import Local from "./Local";
import LocalMcp from "./LocalMcp";
import McpAvatar from "./McpAvatar";
import MySpcae from "./MySpcae";
import OffcialExample from "./OffcialExample";
import Organization from "./Organization";
import Params from "./Params";
import Playground from "./Playground";
import PlaygroundConfig from "./PlaygroundConfig";
import Refresh from "./Refresh";
import Remote from "./Remote";
import RemoteMcp from "./RemoteMcp";
import Result from "./Result";
import RightArrow from "./RightArrow";
import Send from "./Send";
import Setting from "./Setting";
import ShowMore from "./ShowMore";
import SortAsc from "./SortAsc";
import SortDesc from "./SortDesc";
import Sse from "./Sse";
import Standard from "./Standard";
import Stdio from "./Stdio";
import StdioMcp from "./StdioMcp";
import Step01 from "./Step01";
import Step02 from "./Step02";
import Step03 from "./Step03";
import Step04 from "./Step04";
import StopGenerate from "./StopGenerate";
import Subscribe from "./Subscribe";
import SubscribeFilled from "./SubscribeFilled";
import Subtract from "./Subtract";
import Tag from "./Tag";
import Tool from "./Tool";
import Unfold from "./Unfold";

export const IconSendActived = createIcon(SendActived);
export const IconAiTools1 = createIcon(AiTools1);
export const IconAiTools2 = createIcon(AiTools2);
export const IconAiTools3 = createIcon(AiTools3);
export const IconAiTools4 = createIcon(AiTools4);
export const IconAlert = createIcon(Alert);
export const IconArrowRight = createIcon(ArrowRight);
export const IconCopy = createIcon(Copy);
export const IconDebug = createIcon(Debug);
export const IconDelete = createIcon(Delete);
export const IconDetail = createIcon(Detail);
export const IconElipsis = createIcon(Elipsis);
export const IconExitFullscreen = createIcon(ExitFullscreen);
export const IconEye = createIcon(Eye);
export const IconFullscreen = createIcon(Fullscreen);
export const IconImport = createIcon(Import);
export const IconLeftOutlined = createIcon(LeftOutlined);
export const IconLightMySpcae = createIcon(LightMySpcae);
export const IconLightPlayground = createIcon(LightPlayground);
export const IconList = createIcon(List);
export const IconLocal = createIcon(Local);
export const IconLocalMcp = createIcon(LocalMcp);
export const IconMcpAvatar = createIcon(McpAvatar);
export const IconMySpcae = createIcon(MySpcae);
export const IconOffcialExample = createIcon(OffcialExample);
export const IconOrganization = createIcon(Organization);
export const IconParams = createIcon(Params);
export const IconPlayground = createIcon(Playground);
export const IconPlaygroundConfig = createIcon(PlaygroundConfig);
export const IconRefresh = createIcon(Refresh);
export const IconRemote = createIcon(Remote);
export const IconRemoteMcp = createIcon(RemoteMcp);
export const IconResult = createIcon(Result);
export const IconRightArrow = createIcon(RightArrow);
export const IconSend = createIcon(Send);
export const IconSetting = createIcon(Setting);
export const IconShowMore = createIcon(ShowMore);
export const IconSortAsc = createIcon(SortAsc);
export const IconSortDesc = createIcon(SortDesc);
export const IconSse = createIcon(Sse);
export const IconStandard = createIcon(Standard);
export const IconStdio = createIcon(Stdio);
export const IconStdioMcp = createIcon(StdioMcp);
export const IconStep01 = createIcon(Step01);
export const IconStep02 = createIcon(Step02);
export const IconStep03 = createIcon(Step03);
export const IconStep04 = createIcon(Step04);
export const IconStopGenerate = createIcon(StopGenerate);
export const IconSubscribe = createIcon(Subscribe);
export const IconSubscribeFilled = createIcon(SubscribeFilled);
export const IconSubtract = createIcon(Subtract);
export const IconTag = createIcon(Tag);
export const IconTool = createIcon(Tool);
export const IconUnfold = createIcon(Unfold);

export const iconsMap: Record<string, FC<SVGProps<SVGSVGElement>>> = {
    SendActived: IconSendActived,
    aiTools1: IconAiTools1,
    aiTools2: IconAiTools2,
    aiTools3: IconAiTools3,
    aiTools4: IconAiTools4,
    alert: IconAlert,
    arrowRight: IconArrowRight,
    copy: IconCopy,
    debug: IconDebug,
    delete: IconDelete,
    detail: IconDetail,
    elipsis: IconElipsis,
    exitFullscreen: IconExitFullscreen,
    eye: IconEye,
    fullscreen: IconFullscreen,
    import: IconImport,
    leftOutlined: IconLeftOutlined,
    lightMySpcae: IconLightMySpcae,
    lightPlayground: IconLightPlayground,
    list: IconList,
    local: IconLocal,
    localMCP: IconLocalMcp,
    mcpAvatar: IconMcpAvatar,
    mySpcae: IconMySpcae,
    offcialExample: IconOffcialExample,
    organization: IconOrganization,
    params: IconParams,
    playground: IconPlayground,
    playgroundConfig: IconPlaygroundConfig,
    refresh: IconRefresh,
    remote: IconRemote,
    remoteMCP: IconRemoteMcp,
    result: IconResult,
    rightArrow: IconRightArrow,
    send: IconSend,
    setting: IconSetting,
    showMore: IconShowMore,
    sortAsc: IconSortAsc,
    sortDesc: IconSortDesc,
    sse: IconSse,
    standard: IconStandard,
    stdio: IconStdio,
    stdioMCP: IconStdioMcp,
    step01: IconStep01,
    step02: IconStep02,
    step03: IconStep03,
    step04: IconStep04,
    stopGenerate: IconStopGenerate,
    subscribe: IconSubscribe,
    subscribeFilled: IconSubscribeFilled,
    subtract: IconSubtract,
    tag: IconTag,
    tool: IconTool,
    unfold: IconUnfold,
};

export default iconsMap;
