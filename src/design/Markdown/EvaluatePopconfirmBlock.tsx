import {Popconfirm} from 'antd';
import {ReactNode} from 'react';

interface EvaluatePopconfirmBlockProps {
    children: ReactNode;
    reason: string;
    result: 'accept' | 'reject' | 'default';
}

const ResultColorMap = {
    accept: 'green',
    reject: 'red',
    default: 'gray',
};

export function EvaluatePopconfirmBlock(props: EvaluatePopconfirmBlockProps) {
    const {children, reason, result} = props;
    return (
        <Popconfirm
            title="错误原因"
            icon={null}
            description={
                <div
                    style={{maxHeight: 200, maxWidth: 300, overflowY: 'auto'}}
                >
                    {reason ?? ''}
                </div>
            }
            okText="正确"
            cancelText="错误"
        >
            <span
                style={{backgroundColor: ResultColorMap[result]}}
                onClick={e => {
                    e.preventDefault();
                    e.stopPropagation();
                }}
            >
                {children}
            </span>
        </Popconfirm>
    );
}
