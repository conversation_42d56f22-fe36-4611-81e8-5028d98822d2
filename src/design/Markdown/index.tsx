/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Image, Popover, Space, Typography} from 'antd';
import 'katex/dist/katex.min.css';
import {CSSProperties, ComponentType, ReactNode, lazy, useMemo} from 'react';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeRaw from 'rehype-raw';
import remarkBreaks from 'remark-breaks';
import {Link} from 'react-router-dom';
import rehypeKatex from 'rehype-katex';
import CodeBlock from './CodeBlock';
import MarkdownConfigProvider from './MarkdownConfigProvider';
import {EvaluatePopconfirmBlock} from './EvaluatePopconfirmBlock';

const ReactMarkdown = lazy(
    () => import(/* webpackChunkName: "ReactMarkdown" */ 'react-markdown')
);

interface TransparentProps {
    children: ReactNode;
    src?: string;
    alt?: string;
    style?: any;
    title?: string;
    href?: string;
}

const TableContent = styled.table`
    width: 100%;
    border: 1px solid #ddd;
    margin-bottom: 0.6em;
    border-collapse: collapse;
    text-align: left;

    thead {
        background: #eee;
    }
    th {
        padding: 0.1em 0.4em;
        min-width: 250px;
    }

    td {
        vertical-align: top;
        padding: 0.1em 0.4em;
        border: 1px solid #ddd;
        .ant-5-image {
            max-width: 200px;
        }
    }
`;

function Transparent({children}: TransparentProps) {
    return <>{children}</>;
}

function TableBlock({children}: TransparentProps) {
    return (
        <div style={{width: '100%', overflowX: 'auto'}}>
            <TableContent>{children}</TableContent>
        </div>
    );
}

function ImageBlock(props: TransparentProps) {
    const style = props?.style ?? {};
    const title = props?.title;
    return (
        <Space direction="vertical" align="end">
            <div style={style}>
                <Image src={props.src} />
            </div>
            {title && (
                <Typography.Text type="secondary">{title}</Typography.Text>
            )}
        </Space>
    );
}

function emptyBlock() {
    return <></>;
}

function tdBlock({children}: TransparentProps) {
    return (
        <td>
            <Popover
                // eslint-disable-next-line react/jsx-no-bind
                getPopupContainer={() => {
                    return (
                        document.querySelector('#markdownPanel_true')
                        || document.body
                    );
                }}
                content={
                    <div
                        style={{
                            maxWidth: 500,
                            overflow: 'auto',
                            maxHeight: 250,
                        }}
                    >
                        <Typography.Paragraph>{children}</Typography.Paragraph>
                    </div>
                }
            >
                <Typography.Paragraph ellipsis={{rows: 4}}>
                    {children}
                </Typography.Paragraph>
            </Popover>
        </td>
    );
}

function aBlock(props: TransparentProps) {
    return (
        <Link to={props?.href} target="_blank">
            {props?.children}
        </Link>
    );
}

const components: Record<string, ComponentType<any>> = {
    pre: Transparent,
    code: CodeBlock,
    table: TableBlock,
    img: ImageBlock,
    input: emptyBlock,
    textarea: emptyBlock,
    td: tdBlock,
    a: aBlock,
    evaluatepopconfirm: EvaluatePopconfirmBlock,
};

interface Props {
    content: string;
    codeHighlight: boolean;
    style?: CSSProperties;
    /**
     * 有业务需要保留html元素在markdown中，会手动把这个值设置为false
     * @default true
     */
    renderHtml?: boolean;
}

const Layout = styled.div`
    h1 {
        font-size: 2em;
        margin: 0.67em 0;
    }
    h2 {
        font-size: 1.5em;
        margin: 0.75em 0;
    }
    h3 {
        font-size: 1.17em;
        margin: 0.83em 0;
    }
    h4 {
        margin: 1.12em 0;
    }
    h5 {
        font-size: 0.83em;
        margin: 1.5em 0;
    }
    h6 {
        font-size: 0.75em;
        margin: 1.67em 0;
    }
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    b,
    strong {
        font-weight: bolder;
    }

    a {
        color: #0047b3;
    }

    p {
        margin-block-start: 1em;
        margin-block-end: 1em;

        :first-of-type {
            margin-top: 0;
        }

        :last-child {
            margin-bottom: 0;
        }
    }

    pre {
        margin: 0;
        border: 1em solid transparent;
        font-size: 12px;
        background-color: #000;
        color: #fff;
        border-radius: 4px;
        overflow-x: auto;
    }

    code {
        word-wrap: break-word;
        padding: 2px 4px;
        border-radius: 4px;
        margin: 0 2px;
        color: #1e6bb8;
        background-color: rgba(27, 31, 35, 0.05);
        font-family: Operator Mono, Consolas, Monaco, Menlo, monospace;
        word-break: break-all;
    }

    ol,
    ul {
        padding: 0 18px;
    }

    ol li {
        padding-left: 0.2em;
        margin-left: 0.2em;
        list-style: decimal;
    }
    ul li {
        padding-left: 0.2em;
        margin-left: 0.2em;
        list-style: disc;
    }
    .ant-5-image .ant-5-image-mask {
        display: none;
    }
    word-break: break-all;
`;

export function Markdown({
    content,
    codeHighlight,
    style,
    renderHtml = true,
}: Props) {
    const rehypePlugins = useMemo(
        () => {
            const pluginList = [rehypeKatex];
            if (renderHtml) {
                pluginList.push(rehypeRaw as any);
            }
            return pluginList;
        },
        [renderHtml]
    );
    return (
        <MarkdownConfigProvider codeHighlight={codeHighlight}>
            <Layout style={style}>
                <ReactMarkdown
                    components={components}
                    remarkPlugins={[remarkBreaks, remarkGfm, remarkMath]}
                    rehypePlugins={rehypePlugins}
                >
                    {content}
                </ReactMarkdown>
            </Layout>
        </MarkdownConfigProvider>
    );
}
