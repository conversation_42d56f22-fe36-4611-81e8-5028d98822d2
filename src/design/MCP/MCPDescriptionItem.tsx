import {CSSProperties, ReactNode} from 'react';
import styled from '@emotion/styled';
import {Flex} from 'antd';

const DescriptionWrapper = styled(Flex) <{type: 'secondary' | 'default'}>`
    color: ${({type}) => (type === 'secondary' ? '#545454' : '#181818')};
    font-size: ${({type}) => (type === 'secondary' ? '12px' : '14px')};

    label {
        flex-shrink: 0;
        white-space: nowrap;
        color: ${({type}) => (type === 'secondary' ? '#8F8F8F' : '#545454')};
        line-height: ${({type}) => (type === 'secondary' ? '20px' : '22px')};
    }
`;

interface DescriptionItemProps {
    type?: 'secondary' | 'default';
    label: string | JSX.Element;
    children: ReactNode;
    labelStyle?: CSSProperties;
    gap?: number;
}

const DescriptionItem = ({
    type = 'default',
    label,
    children,
    labelStyle,
    gap = 16,
}: DescriptionItemProps) => (
    <DescriptionWrapper gap={gap} type={type}>
        <label style={labelStyle}>{label}</label>
        <div style={{flex: 1}}>
            {children}
        </div>
    </DescriptionWrapper>
);

export default DescriptionItem;
