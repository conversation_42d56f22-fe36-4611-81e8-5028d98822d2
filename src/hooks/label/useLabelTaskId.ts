import {useSearchParams} from '@panda-design/router';
import {useBatchMarkTaskIndex} from '@/regions/labelBatchTask/batchMarkTaskIndex';
import {useLabelBatchAllTaskIds} from '@/regions/labelBatchTask/allTaskIds';
import {useLabelBatchTaskSelectIds} from './useLabelBatchTaskParams';
import {useLabelParams} from './useLabelParams';

// 这个 hook 是为了在快速和普通标注中都能获取 taskId
export const useLabelTaskId = () => {
    const {labelStudioProjectId} = useLabelParams();
    const {taskId} = useSearchParams();
    const currentIndex = useBatchMarkTaskIndex(labelStudioProjectId);
    const selectedTaskIds = useLabelBatchTaskSelectIds();
    const allTaskIds = useLabelBatchAllTaskIds(labelStudioProjectId);

    // task 页面可以从 url 中获取 taskId
    if (taskId) {
        return Number(taskId);
    }
    // 选择 selectedIds 的情况，可以从 selectedIds 中获取 taskId
    if (selectedTaskIds && selectedTaskIds.length) {
        return selectedTaskIds[currentIndex];
    }
    return allTaskIds[currentIndex];
};
