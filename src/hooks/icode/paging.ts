import {useState, useCallback} from 'react';

export function usePageAndPageSize(defaultPage: number = 1, defaultPageSize: number = 10) {
    const [page, updatePage] = useState<number>(defaultPage);
    const [pageSize, updatePageSize] = useState<number>(defaultPageSize);
    const onChange = useCallback(
        (page: number, pageSize?: number) => {
            updatePage(page);
            updatePageSize(pageSize ?? defaultPageSize);
        },
        [defaultPageSize]
    );

    return {page, pageSize, onChange};
}

export function useOffsetAndLimit(defaultOffset: number = 0, defaultLimit: number = 10) {
    const [offset, updateOffset] = useState<number>(defaultOffset);
    const [limit, updateLimit] = useState<number>(defaultLimit);
    const onChangeOffsetLimit = useCallback(
        (offset?: number, limit?: number) => {
            updateOffset(offset ?? defaultOffset);
            updateLimit(limit ?? defaultLimit);
        },
        [defaultLimit, defaultOffset]
    );

    return {offset, limit, onChangeOffsetLimit};
}
