/* eslint-disable max-lines */
import {useCallback} from 'react';
import {message} from '@panda-design/components';
import {apiPostFileRenderProperties} from '@/api/dataset';
import {handleChangeFormattersType, RenderProperty} from '@/types/agGrid/agGrid';
import {CellValueChangedEvent, ColDef, ColumnMovedEvent} from '@/third-party/devops-federation/types';
import {apiPostSaveDatasetFileEdit} from '@/api/icode/datasetFileEdit';

interface Props {
    gridRef: any;
    dataset: string;
    path: string;
    oid: string;
    version: string;
    draftSessionId: string;
}

export const useFileEdit = ({
    gridRef,
    dataset,
    path,
    oid,
    version,
    draftSessionId,
}: Props) => {
    const handleUpdateRenderProperties = useCallback(
        (renderProperties: RenderProperty[]) => {
            apiPostFileRenderProperties({
                dataset,
                path,
                oid,
                version,
                sessionId: draftSessionId,
                timestamp: Date.now(),
                operations: renderProperties,
            });
        },
        [dataset, path, oid, version, draftSessionId]
    );

    const handleCellValueChanged = useCallback(
        ({data, column, oldValue, newValue}: CellValueChangedEvent) => {
            const colName = column.getColId();
            const params = {
                dataset,
                path,
                oid,
                timestamp: Date.now(),
                sessionId: draftSessionId,
                opType: 'ROW_UPDATE',
                updateRowId: data.__SYSTEM_INTERNAL_ROW_ID,
                updateContent: {
                    column: colName,
                    from: oldValue ?? '',
                    to: newValue,
                },
            };

            apiPostSaveDatasetFileEdit(params);
        },
        [dataset, draftSessionId, path, oid]
    );

    const handleColumnMoved = useCallback(
        ({
            finished,
            api,
            toIndex,
            column,
        }: ColumnMovedEvent) => {
            if (finished) {
                const params = {
                    dataset,
                    path,
                    oid,
                    timestamp: Date.now(),
                    sessionId: draftSessionId,
                    opType: 'COL_MOVE',
                    updateContent: {
                        column: column.getColId(),
                        to: toIndex === 0 ? '' : ((api.getColumnDefs() ?? [])[toIndex - 1] as any)?.colId,
                    },
                };

                apiPostSaveDatasetFileEdit(params);
            }
        },
        [dataset, draftSessionId, oid, path]
    );

    const handleRowAdded = useCallback(
        async (currentRowId: string, currentRowIndex: number, position: 'up' | 'down', count: number) => {
            const params = {
                dataset,
                path,
                oid,
                timestamp: Date.now(),
                sessionId: draftSessionId,
                opType: 'ROW_INSERT',
                updateRowId: currentRowId,
                updateContent: {
                    to: position,
                    counts: count,
                },
            };

            try {
                const {rows} = await apiPostSaveDatasetFileEdit(params);
                gridRef.current!.api?.applyTransaction({
                    addIndex: position === 'up' ? currentRowIndex : currentRowIndex + 1,
                    add: rows ?? [],
                });
            }
            catch {
                message.error('插入失败');
            }
        },
        [dataset, path, oid, draftSessionId, gridRef]
    );

    const handleColAdded = useCallback(
        async (
            currentColId: string,
            position: 'left' | 'right',
            newColumnDefs: ColDef[],
            handleChangeFormatters: handleChangeFormattersType
        ) => {
            const params = {
                dataset,
                path,
                oid,
                timestamp: Date.now(),
                sessionId: draftSessionId,
                opType: 'COL_ADD',
                updateContent: {
                    column: currentColId,
                    to: position,
                    counts: newColumnDefs.length,
                    dType: 'text',
                },
            };

            try {
                const {colNames} = await apiPostSaveDatasetFileEdit(params);
                for (const [index, columnDef] of newColumnDefs.entries()) {
                    const newField = colNames[index];
                    columnDef.headerName = newField;
                    columnDef.field = newField;
                    columnDef.colId = newField;
                    columnDef.tooltipField = newField;
                    columnDef.width = newField.length + 200;
                    const {headerComponentParams} = columnDef;
                    columnDef.headerComponentParams = {
                        ...headerComponentParams,
                        onChangeFormatters: (formatterCodes: string[]) =>
                            handleChangeFormatters(newField, formatterCodes),
                    };
                }

                const columnDefs = (gridRef.current?.api?.getColumnDefs() ?? []) as ColDef[];
                const findIndex = columnDefs.findIndex(col => col.colId === currentColId);
                columnDefs.splice(position === 'left' ? findIndex : findIndex + 1, 0, ...newColumnDefs);
                gridRef.current?.api?.setGridOption('columnDefs', columnDefs);
            }
            catch {
                message.error('插入失败');
            }
        },
        [dataset, draftSessionId, gridRef, oid, path]
    );

    const handleRowRemoved = useCallback(
        (currentRowId: string) => {
            const params = {
                dataset,
                path,
                oid,
                timestamp: Date.now(),
                sessionId: draftSessionId,
                opType: 'ROW_DELETE',
                updateRowId: currentRowId,
            };

            apiPostSaveDatasetFileEdit(params);
        },
        [dataset, draftSessionId, oid, path]
    );

    const handleColRemoved = useCallback(
        (currentColId: string) => {
            const params = {
                dataset,
                path,
                oid,
                timestamp: Date.now(),
                sessionId: draftSessionId,
                opType: 'COL_DELETE',
                updateContent: {
                    column: currentColId,
                },
            };

            apiPostSaveDatasetFileEdit(params);
        },
        [dataset, draftSessionId, oid, path]
    );

    const handleSaveHeaderName = useCallback(
        (oldName: string, newName: string) => {
            const params = {
                dataset,
                path,
                oid,
                timestamp: Date.now(),
                sessionId: draftSessionId,
                opType: 'COL_RENAME',
                updateContent: {
                    column: oldName,
                    from: oldName,
                    to: newName,
                },
            };

            apiPostSaveDatasetFileEdit(params);
        },
        [dataset, draftSessionId, oid, path]
    );

    return {
        handleUpdateRenderProperties,
        handleCellValueChanged,
        handleColumnMoved,
        handleRowAdded,
        handleColAdded,
        handleRowRemoved,
        handleColRemoved,
        handleSaveHeaderName,
    };
};
