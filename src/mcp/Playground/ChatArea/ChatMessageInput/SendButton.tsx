import {Tooltip} from 'antd';
import {IconSend, IconSendActived, IconStopGenerate} from '@/icons/mcp';

interface SendButtonProps {
    canSend: boolean;
    isGenerating: boolean;
    sending: boolean;
    actived: boolean;
    onSend: () => void;
    onStop?: () => void;
}

const SendButton = ({
    isGenerating,
    sending,
    onSend,
    onStop,
    actived,
}: SendButtonProps) => {
    const isLoading = sending || isGenerating;

    const handleStopClick = () => {
        if (onStop) {
            onStop();
        }
    };

    return (
        <>
            {isLoading ? (
                <Tooltip title="停止生成">
                    <IconStopGenerate
                        onClick={handleStopClick}
                        style={{fontSize: '28px', cursor: 'pointer'}}
                    />
                </Tooltip>
            ) : actived ? (
                <IconSendActived onClick={onSend} style={{fontSize: '28px'}} />
            ) : (
                <IconSend onClick={onSend} style={{fontSize: '28px'}} />
            )}
        </>
    );
};

export default SendButton;
