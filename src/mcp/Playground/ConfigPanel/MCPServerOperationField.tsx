import {Button, message} from '@panda-design/components';
import {Flex, Form, Switch} from 'antd';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {css} from '@emotion/css';
import {MCPServerConfigButton} from '@/components/MCP/MCPToolDebug/MCPServerConfigButton';
import {IconSetting} from '@/icons/mcp';
import {
    useMCPGlobalVarsContext,
    MCPGlobalVarsProvider,
    GlobalVar,
} from '@/components/MCP/MCPToolDebug/Providers/MCPServerConfigProvider';
import {useMCPServerConfigModalControl} from '@/components/MCP/MCPToolDebug/hooks/useMCPServerConfigModalControl';
import {MCPServerFieldValues} from './types';
import {useConfigPanelContext} from './index';

const highlightedCss = css`
    background: #CCE5FF!important;
    svg {
        color: #0080FF!important;
    }
`;
interface Props {
    name: number | string;
    server: MCPServerFieldValues;
}
function MCPServerOperationField({name, server}: Props) {
    const {config} = useConfigPanelContext();
    const form = Form.useFormInstance();
    const isOfficialExample = server?.officialExample;
    const [highlighted, setHighlighted] = useState(false);
    const {validateStatus, resetGlobalVars} = useMCPGlobalVarsContext();
    const {visible, openModal, closeModal} = useMCPServerConfigModalControl();
    const closeModalWithResetForm = useCallback(
        (res?: GlobalVar[]) => {
            closeModal();
            resetGlobalVars(res);
        },
        [closeModal, resetGlobalVars]
    );
    const handleChangeEnable = useCallback(
        (checked: boolean) => {
            // 官方示例不用填参数，也就不需要验证了
            if (!validateStatus && isOfficialExample !== true) {
                message.warning('请先进行服务配置后启用');
                form.setFieldValue(['mcpServers', name, 'enable'], false);
                setHighlighted(true);
                setTimeout(() => {
                    setHighlighted(false);
                }, 3000);
            }
            else {
                form.setFieldValue(['mcpServers', name, 'enable'], checked);
            }
        },
        [form, name, validateStatus, isOfficialExample]
    );

    const handleSuccess = (serverParam: any) => {
        form.setFieldValue(['mcpServers', name, 'serverParams'], serverParam);
    };

    useEffect(
        () => {
            // 这个server是从form表单取的值，所以有可能为空
            if (config.openServerConfigId && server?.id === config.openServerConfigId) {
                openModal();
            }
        },
        [config.openServerConfigId, openModal, server?.id]
    );
    const showConfigButton = useMemo(
        () => {
            return Boolean(server?.serverParams?.length && !isOfficialExample);
        },
        [isOfficialExample, server?.serverParams?.length]
    );
    return (
        <Flex gap={4} align="center">
            <div style={{display: showConfigButton ? 'flex' : 'none'}}>
                {/* 全局参数的初始化验证是在Modal组件中做的，所以这个Button必须要渲染，否则验证会错误 */}
                <MCPServerConfigButton
                    onSuccess={handleSuccess}
                    openModal={openModal}
                    closeModal={closeModalWithResetForm}
                    modalVisible={visible}
                >
                    <Button
                        icon={<IconSetting />}
                        type="text"
                        className={highlighted ? highlightedCss : undefined}
                    />
                </MCPServerConfigButton>
            </div>
            <Form.Item
                name={[name, 'enable']}
                noStyle
            >
                <Switch size="small" onChange={handleChangeEnable} />
            </Form.Item>
        </Flex>
    );
}

export default ({name, server}: Props) => {
    return (
        <MCPGlobalVarsProvider mcpId={server?.id} temporary={false}>
            <MCPServerOperationField name={name} server={server} />
        </MCPGlobalVarsProvider>
    );
};
