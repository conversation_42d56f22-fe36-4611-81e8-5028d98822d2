import {SearchOutlined} from '@ant-design/icons';
import {Checkbox, Flex, Form, FormInstance, FormProps, Input, Tabs, Typography} from 'antd';
import {useDebouncedCallback} from 'huse';
import {isNil} from 'lodash';
import {useCallback, useEffect, useState} from 'react';

export type MCPServerListType = 'square' | 'example';

export const useServerListTabs = (
    {afterChangeValues, type, open}: {afterChangeValues: () => void, type: MCPServerListType, open: boolean}
) => {
    const [form] = Form.useForm();
    const onlySeleted = Form.useWatch(['onlySeleted'], form);
    const tabsKey = Form.useWatch(['tabsKey'], form);

    const [keywords, setKeywords] = useState('');
    const debouncedHanldeKeywords = useDebouncedCallback(
        (keywords: string) => {
            setKeywords(keywords);
            afterChangeValues();
        },
        300
    );

    const handleValuesChange = useCallback(
        (changedValues: Record<string, any>) => {
            if (!isNil(changedValues.searchText)) {
                debouncedHanldeKeywords(changedValues.searchText);
            }
            if (!isNil(changedValues.onlySeleted) || !isNil(changedValues.tabsKey)) {
                afterChangeValues();
            }
        },
        [debouncedHanldeKeywords, afterChangeValues]
    );

    const resetValues = useCallback(
        () => {
            form.resetFields();
            setKeywords('');
            afterChangeValues();
        },
        [form, afterChangeValues]
    );

    useEffect(
        () => {
            if (open) {
                form.setFieldValue('tabsKey', type);
            }
        },
        [type, open, form]
    );

    return {
        tabsKey,
        onlySeleted,
        keywords,
        handleValuesChange,
        resetValues,
        form,
    };
};
interface Props {
    form?: FormInstance;
    onValuesChange?: FormProps['onValuesChange'];
}
export default function ServerListTabs({
    form,
    onValuesChange,
}: Props) {
    const tabsKey = Form.useWatch('tabsKey', form);
    return (
        <Form
            form={form}
            onValuesChange={onValuesChange}
            initialValues={{
                onlySeleted: false,
                searchText: '',
                tabsKey: 'square',
            }}
        >
            <Form.Item name="tabsKey" noStyle>
                <Tabs
                    activeKey={tabsKey}
                    items={[
                        {
                            key: 'square',
                            label: '广场MCP',
                        },
                        {
                            key: 'example',
                            label: '官方示例MCP',
                        },
                    ]}
                    tabBarExtraContent={(
                        <Flex align="center" gap={4}>
                            <Form.Item name="onlySeleted" valuePropName="checked" noStyle>
                                <Checkbox>
                                    <Typography.Text>仅看已选择MCP</Typography.Text>
                                </Checkbox>
                            </Form.Item>
                            <Form.Item name="searchText" noStyle>
                                <Input
                                    placeholder="请输入MCP名称/描述搜索"
                                    allowClear
                                    style={{width: 240}}
                                    prefix={<SearchOutlined />}
                                />
                            </Form.Item>
                        </Flex>
                    )}
                    style={{marginBottom: 16}}
                />
            </Form.Item>
        </Form>
    );
}
