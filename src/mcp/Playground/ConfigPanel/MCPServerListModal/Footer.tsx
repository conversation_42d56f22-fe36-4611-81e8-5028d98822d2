import {Button} from '@panda-design/components';
import {Alert, Flex, Typography} from 'antd';

interface Props {
    onCancel: () => void;
    onOk: () => void;
    selectedCount: number;
}
export default function Footer({onCancel, onOk, selectedCount}: Props) {
    return (
        <Flex align="center" justify="space-between">
            <Typography.Text type="secondary">
                已选 {selectedCount} 条
            </Typography.Text>
            <Flex align="center" gap={12}>
                <Button onClick={onCancel}>取消</Button>
                <Button type="primary" onClick={onOk}>确定</Button>
            </Flex>
        </Flex>
    );
}

export function ExampleAlert({type}: {type: string}) {
    return (
        <Alert
            message={type === 'example'
                ? (
                    <>
                        <div>官方示例MCP提供有默认的鉴权Token，仅提供部分工具的体验。</div>
                        <div>广场MCP有对应的正式MCP Server，需要输入鉴权参数，即可使用MCP Server的所有工具，添加广场MCP后，官方示例将被覆盖。</div>
                    </>
                ) : (
                    <div>广场MCP在输入鉴权参数后，即可使用所有工具；若仅体验，添加官方示例MCP后，对应的广场MCP将被覆盖。</div>
                )
            }
            style={{marginBottom: 16}}
        />
    );
}
