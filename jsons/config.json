{"code": 200, "data": {"announcement": {"contentList": ["【评估】评估策略中非必填维度支持清空已有内容", "【评估】case级流转评估任务支持批量完成验收", "【评估】修复评估划词若干问题", "【评估】GSB评估时增加“两者都不好”选项，最终报告中也会单独统计", "【评估】Web端抓取能力增加联网搜索参数配置", "【Prompt】Prompt评估支持上传仅包含图片的评估集", "【模型接入】批量预测支持多轮推理（以message形式提供历史对话）", "【模型接入】增加ERNIE-4.5-Turbo-128K-Preview模型支持"], "version": 113}, "autoEvaluateAnnouncement": {"contentList": ["[上线时间]：4月26日 13:00-14:00", "[上线内容]：", "1、创作专项：新增接入文本创作指令follow-v2评估策略", "2、问答专项：事实正确性评估支持EB4评估模型", "[上线影响]：上线期间，自动评估服务不可用；未完成的任务在上线后会继续执行。", "[业务配合]：上线期间不要提交自动评估任务；上线后请业务观察影响，有问题请及时反馈。", "[上线步骤]：按停止服务、更新、启动服务流程上线。上线后回归验证。", "[上线接口人]：丁名时", "[上线异常处理]：回滚到上一个稳定版本"], "startTime": "2024-04-26 13:00:00", "endTime": "2024-04-26 14:00:00"}, "space": {"settingNeedAdminSpaceList": ["eb_eval_nlp", "yiyanautotest", "yiyan-test", "yiyan-autotest", "RAG_evaluate", "di<PERSON><PERSON>-shu<PERSON><PERSON>", "trainingProject09", "trainingProject10", "trainingProject11"], "yiYanSpaceList": ["eb_eval_nlp", "yiyanautotest", "yiyan-test", "yiyan-autotest", "RAG_evaluate"], "woWuSpaceList": ["ERNIE-Evalue"], "ebSandboxSpaceList": ["ERNIE-Evalue", "ERNIE-Evalue-Info_Process", "20230321", "di<PERSON><PERSON>-shu<PERSON><PERSON>", "test_model_ernie", "ERINEandFEED", "autocomate", "EB_GAOKAO_TC", "ailegal"], "duZhiLiaoSpaceList": ["show_case_default", "digital_human_prompt_engineering", "llm_evaluate"], "uniconfSpaceList": ["llm_evaluate", "show_case_default", "hui<PERSON>"], "yiYanCompareSpaceList": ["123", "demo0802", "novel", "shangye", "<PERSON><PERSON><PERSON>", "check", "wenkuaaa", "SO-knowledge-flow", "so-ku", "t2i", "tieba", "Dujia_Sft_Evaluation", "<PERSON><PERSON><PERSON>", "fmx_prompt", "demo1227", "119", "szyd", "Plugin_Strategy", "badcase_close", "res_get", "newapp", "lingjing-eval", "web_memory", "acg_xyb", "hycx_sft_check", "wangpan", "brandagenttest", "tpg-kg_chenmoye_sft_magicnote-v1", "magic-note", "Health_EB4_Evaluate", "423655dfsh", "0426", "show_case_default", "AIIP_AUTO_EVAL", "wenjin-space_default", "shangye_pinpai", "miaobi_jiankang", "travel_strategy"], "llmWebSpaceList": ["ERNIE-Evalue"], "autoAssistantSpaceList": ["assistant", "SEO"], "yiYanDiySpaceList": ["autoeval", "show_case_default"], "yiYanMergeReportSpaceList": ["NLP_PM_SHENZHEN", "human_4_4turbo", "human_lite", "ERNIE_LiteV", "hhr8685"], "ebV3SpaceList": ["zsj", "ERNIE-Evalue", "ERNIE-Evalue-Info_Process", "ernie-wenchaung", "security_eval"], "promptDirSpaceWhitelist": ["1024square-2024", "trainingProject09", "trainingProject10", "trainingProject11"]}, "templateReviewers": [{"name": "苏琳", "username": "sulin01", "departmentName": "产品组"}, {"name": "黄一鹤", "username": "<PERSON><PERSON><PERSON><PERSON>", "departmentName": "工具研发组"}, {"name": "张兆鑫", "username": "zhangzhaoxin01", "departmentName": "工具研发组"}, {"name": "陈耀宗", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "departmentName": "工具研发组"}], "promptConfig": {"square1024": {"startTime": "2024-09-29 00:00:00", "endTime": "2024-10-21 12:00:00"}}, "model": {"graphicModelList": ["gpt-4o", "gpt-4-turbo-with-vision", "gpt-4o-mini", "ERNIE-LiteV-Preview", "qwen2_vl", "qwen25-vl", "DeepSeek-VL2", "Fuyu-8B", "DeepSeek-VL2-Small", "ERNIE-4.5-8K-Preview", "ERNIE-Lite-V"], "modelCaseLimitList": ["ERNIE-X1-32K-Preview", "DeepSeek-R1"], "hotModels": ["ERNIE-X1-Turbo-32K", "ERNIE-4.5-Turbo-32K", "ERNIE-4.5-Turbo-128K", "ERNIE-4.5-Turbo-VL-32K"], "comingOfflineModels": ["ERNIE-X1-32K", "ERNIE-X1-32K-Preview", "ERNIE-4.5-8K-Preview"]}, "links": {"trainingProjectUrl": "https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/WjGjiS_SK_/a6hOszvRJBDcUw"}, "card": {"versionCardTypes": ["Prompt Story", "Prompt Task", "GenAI Data Story", "GenAI Data Task", "Story"]}}, "msg": "SUCC"}